// Type definitions for pos-calculator 1.0.0
// Project: https://github.com/storehubnet/pos-calculator
// Definitions by: <PERSON> <https://github.com/cctv1237>
// Definitions: https://github.com/DefinitelyTyped/DefinitelyTyped
// TypeScript Version: 3.0

declare namespace calculator {
    export function calculate(
        transaction: Object,
        isTaxInclusiveDisplay: boolean,
        source?: string, // 'DEFAULT' | 'POS' | 'Beep'
    ): Object;
    export function receipt(
        transaction: Object,
        isTaxInclusiveDisplay: boolean,
        countryCode: string,
        hasDiscountColumn: boolean,
        unitPriceUnrounding?: boolean,
    ): Object;
    export function applyPromotion(transaction: Object, promotion: any[], business: Object): Object;
    export function checkConditionSatisfied(condition: Object, ...args: any[]): Object;
    export function isNotInDateRange(promotion: Object, timezone: string): boolean;
    export function isNotInAcrossTimeRange(promotion: Object, timezone: string): boolean;
    export function isNotInWeekdays(promotion: Object, timezone: string): boolean;
    export function isNotInTimeRange(promotion: Object, timezone: string): boolean;
    export const CSource: Object;
}

export = calculator;
