/* eslint-disable consistent-return */

import _, { cloneDeep, filter, find, findIndex, forEach, map, sumBy } from 'lodash';
import conditionCalculator from './conditionCalculator';
import { PromotionConditionsDefaultQuantity, PromotionDiscountType } from '../utils';

function getConditionSatisfiedMaps(items, conditions) {
    let conditionSatisfiedMaps = [];
    for (const conditionItem of conditions) {
        const satisfiedMap = {};
        satisfiedMap.condition = conditionItem;
        satisfiedMap.satisfiedBy = [];
        for (const item of items) {
            if (!item.itemType) {
                const product = item.product;
                const isSatisfied = conditionCalculator.isSatisfied(conditionItem, product);
                if (isSatisfied) {
                    // for item with multiple quantity is count as one satisfiedBy
                    satisfiedMap.satisfiedBy.push(item);
                }
            }
        }

        // if condition is not satisfied by any items return []
        if (satisfiedMap.satisfiedBy.length === 0) {
            return [];
        }

        // if items quantity is not enough to satisfy condition minquantiy return []
        const quantitySum = _.sumBy(satisfiedMap.satisfiedBy, 'availableQuantity');
        if (quantitySum < (conditionItem.minQuantity || 0)) {
            return [];
        }
        conditionSatisfiedMaps.push(satisfiedMap);
    }

    conditionSatisfiedMaps = _.sortBy(
        conditionSatisfiedMaps,
        satisfiedMap => satisfiedMap.satisfiedBy.length,
    );

    return conditionSatisfiedMaps;
}

function deduceConditionCombo(baseItem, appendableItems, condition, prevConditionsCombo = {}) {
    let combo = {};
    const baseItemRemainQuantity =
        baseItem.availableQuantity - (prevConditionsCombo[baseItem.id] || 0);
    let requireQuantity = condition.minQuantity || 1;

    if (baseItemRemainQuantity < requireQuantity) {
        combo[baseItem.id] = baseItemRemainQuantity;
    } else {
        combo[baseItem.id] = requireQuantity;
    }
    for (const item of appendableItems) {
        if (item.id !== baseItem.id) {
            let appliedQuantitySum = _.sum(Object.values(combo));
            // once condition minQuantity is satisfied stop deduce combo
            if (condition.minQuantity <= appliedQuantitySum) {
                break;
            }

            requireQuantity = condition.minQuantity - appliedQuantitySum;

            const itemRemainQuantity =
                item.availableQuantity -
                (prevConditionsCombo[item.id] || 0) -
                (combo[item.id] || 0);

            if (itemRemainQuantity > 0) {
                if (!combo[item.id]) {
                    combo[item.id] = 0;
                }
                if (itemRemainQuantity < requireQuantity) {
                    combo[item.id] += itemRemainQuantity;
                } else {
                    combo[item.id] += requireQuantity;
                }
            }
        }
    }

    const comboQuantitySum = _.sum(Object.values(combo));
    if (comboQuantitySum < (condition.minQuantity || 1)) combo = {};
    return combo;
}

// CM-6905 The max apply count of a promotion will be limited by required products when it is isRepeatable
function generateTemporaryConditions(promotion, items) {
    if (
        promotion.discountType === PromotionDiscountType.ABSOLUTE ||
        promotion.discountType === PromotionDiscountType.PERCENTAGE ||
        promotion.discountType === PromotionDiscountType.FIXED_UNIT_PRICE
    ) {
        const productCondition = _.find(
            promotion.conditions,
            condition => condition.entity === 'product',
        );
        // align with backoffice syncUpPromotion function
        if (!promotion.maxQuantity) {
            promotion.maxQuantity = PromotionConditionsDefaultQuantity.MAX; // 2^32 -1
        }
        if (!promotion.minQuantity) {
            promotion.minQuantity = PromotionConditionsDefaultQuantity.MIN;
        }
        const satisfiedItems = items.filter(item =>
            conditionCalculator.isSatisfied(productCondition, item.product),
        );
        const totalAvailableQuantity = _.sumBy(satisfiedItems, 'availableQuantity');

        // calculate maxApplicableCount with Required Products
        const { requiredProducts } = promotion;
        let maxApplicableCountByRequiredProducts;
        if (requiredProducts.length > 0) {
            maxApplicableCountByRequiredProducts = deduceMaxApplicableCaseByRequiredProducts(
                requiredProducts,
                items,
            );
        }
        // calculate maxApplicableCount with conditions
        const { maxApplicableCount, maxApplicableQuantity } = deduceMaxApplicableCase(
            totalAvailableQuantity,
            promotion.maxQuantity,
            promotion.minQuantity,
            promotion.isRepeatable,
            maxApplicableCountByRequiredProducts,
        );
        productCondition.minQuantity = Boolean(maxApplicableQuantity)
            ? maxApplicableQuantity
            : PromotionConditionsDefaultQuantity.MAX;
        promotion.maxApplicableCount = maxApplicableCount;
        productCondition.isMinQuantityTemporary = true;
    }
}

function deduceMaxApplicableCase(
    totalAvailableQuantity,
    maxQuantity,
    minQuantity,
    isRepeatable,
    maxApplicableCountByRequiredProducts,
) {
    // get product of base combo and appendable combos
    // 如果不设置min和max， 那么min = 0.01, max = 4294967295
    // 所以Min和Max都可能不是整数
    let maxApplicableCount;
    let maxApplicableQuantity;
    // 如果Promotion在一个Transaction中可以apply多次
    if (isRepeatable) {
        const maxApplicableCaseByConditions = deduceMaxApplicableCountAndQuantity(
            totalAvailableQuantity,
            minQuantity,
            maxQuantity,
            maxApplicableCountByRequiredProducts,
        );
        maxApplicableCount = maxApplicableCaseByConditions.count;
        maxApplicableQuantity = maxApplicableCaseByConditions.quantity;
    } else {
        // 如果Promotion在一个Transaction中只能apply一次
        maxApplicableCount =
            totalAvailableQuantity >= minQuantity && totalAvailableQuantity <= maxQuantity ? 1 : 0;
        maxApplicableQuantity =
            maxApplicableCount === 0 ? 0 : Math.min(totalAvailableQuantity, maxQuantity);
    }
    return { maxApplicableCount, maxApplicableQuantity };
}

function deduceMaxApplicableCountAndQuantity(
    totalAvailableQuantity,
    minQuantity,
    maxQuantity,
    maxApplicableCountByRequiredProducts = PromotionConditionsDefaultQuantity.MAX,
) {
    let maxApplicableCount;
    let maxApplicableQuantity;
    if (totalAvailableQuantity < minQuantity) {
        maxApplicableCount = 0;
        maxApplicableQuantity = 0;
    } else if (minQuantity <= PromotionConditionsDefaultQuantity.MIN) {
        maxApplicableCount = Math.min(
            Math.floor(totalAvailableQuantity / minQuantity),
            PromotionConditionsDefaultQuantity.MAX,
            maxApplicableCountByRequiredProducts,
        );
        maxApplicableQuantity = totalAvailableQuantity;
    } else {
        maxApplicableCount = Math.min(
            Math.floor(totalAvailableQuantity / minQuantity),
            PromotionConditionsDefaultQuantity.MAX,
            maxApplicableCountByRequiredProducts,
        );
        // 例如当min = 3, max = 5时，那么在ApplicableCount确定的情况下，
        // 可以apply promo的quantity应该是在min * ApplicableCount与max * ApplicableCount之间的
        // 所以这里取最大可以apply的数量
        maxApplicableQuantity = Math.min(totalAvailableQuantity, maxApplicableCount * maxQuantity);
    }
    return { count: maxApplicableCount, quantity: maxApplicableQuantity };
}

function deduceMaxApplicableCaseByRequiredProducts(conditions, items) {
    // 计算出所有match requiredProducts的items
    const {
        conditionSatisfiedMaps,
        conditionSatisfiedMapsWithoutDuplicate,
        conditionSatisfiedMapsWithDuplicate,
    } = getConditionSatisfiedMapsNew(items, conditions);
    let maxApplicableCount;
    let maxApplicableCountWithoutDuplicate;
    let maxApplicableCountWithDuplicate;
    // check for every condition
    for (let index = 0; index < conditions.length; index++) {
        const conditionItem = conditions[index];
        const minQuantity = conditionItem.minQuantity;
        const matchedConditionSatisfiedMap = find(
            conditionSatisfiedMaps,
            conditionSatisfiedMap => conditionSatisfiedMap.conditionIndex === index,
        );
        const { totalAvailableQuantity } = matchedConditionSatisfiedMap;
        // WARNING： 如果重构promotion，这里将会替代原有的检查，目前走不到，可以先注释掉
        // if (totalAvailableQuantity < minQuantity) {
        //     maxApplicableCount = 0;
        //     break;
        // } else {
        // get max count Without Duplicate items
        const matchedConditionSatisfiedMapWithoutDuplicate = find(
            conditionSatisfiedMapsWithoutDuplicate,
            conditionSatisfiedMapWithoutDuplicate =>
                conditionSatisfiedMapWithoutDuplicate.conditionIndex === index,
        );
        if (matchedConditionSatisfiedMapWithoutDuplicate) {
            const caseWithoutDuplicateItems = deduceMaxApplicableCountAndQuantity(
                matchedConditionSatisfiedMapWithoutDuplicate.totalAvailableQuantity,
                minQuantity,
                minQuantity,
            );
            const countWithoutDuplicateItems = caseWithoutDuplicateItems.count;
            maxApplicableCountWithoutDuplicate = maxApplicableCountWithoutDuplicate
                ? Math.min(maxApplicableCountWithoutDuplicate, countWithoutDuplicateItems)
                : countWithoutDuplicateItems;
        } else {
            maxApplicableCountWithoutDuplicate = 0;
        }

        // get max count With Duplicate items
        // requiredProducts中的condition只有minQuantity, maxQuantity= null,所以这里直接把minQuantity作为min和max
        const caseWithDuplicateItems = deduceMaxApplicableCountAndQuantity(
            totalAvailableQuantity,
            minQuantity,
            minQuantity,
        );
        const countWithDuplicateItems = caseWithDuplicateItems.count;
        maxApplicableCountWithDuplicate = maxApplicableCountWithDuplicate
            ? Math.min(maxApplicableCountWithDuplicate, countWithDuplicateItems)
            : countWithDuplicateItems;
        // }
    }
    // WARNING： 如果重构promotion，这里将会替代原有的检查，目前走不到，可以先注释掉
    // if (maxApplicableCountWithoutDuplicate !== undefined) {

    // 如果没有重复的items，或者
    // 有重复的items，且在使用和和不使用重复的items的情况下，apply count是一样的，
    // 那么直接以maxApplicableCountWithDuplicate为最大apply次数
    if (
        conditionSatisfiedMapsWithDuplicate.length === 0 ||
        maxApplicableCountWithoutDuplicate === maxApplicableCountWithDuplicate
    ) {
        maxApplicableCount = maxApplicableCountWithoutDuplicate;
    } else {
        // 否则就是有重复的items且在使用和和不使用重复的items的情况下，apply count是不一样的，
        // 那么则需求继续计算出max appliable count
        // 结合实际场景，这个数值应该不会很大，所以这里采用-1的方式来计算最大apply次数，而不采用二分法
        // 那么先假设最大apply次数是maxApplicableCountWithDuplicate，验证是否可行，如果不可行则继续假定次数-1
        maxApplicableCount = calculateMaxApplicableCountWithDuplicateItems(
            conditions,
            conditionSatisfiedMapsWithoutDuplicate,
            conditionSatisfiedMapsWithDuplicate,
            maxApplicableCountWithDuplicate,
        );
    }
    // }

    return maxApplicableCount;
}

function getConditionSatisfiedMapsNew(items, conditions) {
    let conditionSatisfiedMaps = []; //  {conditionIndex:number, totalAvailableQuantity:number}
    let conditionSatisfiedMapsWithoutDuplicate = []; //  {conditionIndex:number, totalAvailableQuantity:number}
    let conditionSatisfiedMapsWithDuplicate = []; //  {item, matchedConditions:[]number, assignableQuantity:number}
    for (const item of items) {
        if (!item.itemType) {
            const product = item.product;
            const matchedConditions = [];
            for (let index = 0; index < conditions.length; index++) {
                const conditionItem = conditions[index];
                const isSatisfied = conditionCalculator.isSatisfied(conditionItem, product);
                if (isSatisfied) {
                    matchedConditions.push(index);
                }
            }
            if (matchedConditions.length > 0) {
                if (matchedConditions.length === 1) {
                    // the item match only one condition
                    const conditionIndex = matchedConditions[0];
                    const inIndex = findIndex(
                        conditionSatisfiedMapsWithoutDuplicate,
                        conditionSatisfiedMap =>
                            conditionSatisfiedMap.conditionIndex === conditionIndex,
                    );
                    // if already has this condition on the conditionSatisfiedMapsWithoutDuplicate
                    if (inIndex < 0) {
                        conditionSatisfiedMapsWithoutDuplicate.push({
                            conditionIndex: conditionIndex,
                            totalAvailableQuantity: item.availableQuantity,
                            // satisfiedBy: [item],
                        });
                    } else {
                        conditionSatisfiedMapsWithoutDuplicate[inIndex].totalAvailableQuantity +=
                            item.availableQuantity;
                    }
                } else {
                    // the item match multiple condition
                    conditionSatisfiedMapsWithDuplicate.push({
                        item,
                        matchedConditions,
                        assignableQuantity: item.availableQuantity,
                    });
                }
                for (const conditionIndex of matchedConditions) {
                    const inIndex = findIndex(
                        conditionSatisfiedMaps,
                        conditionSatisfiedMap =>
                            conditionSatisfiedMap.conditionIndex === conditionIndex,
                    );
                    // if already has this condition on the conditionSatisfiedMap
                    if (inIndex < 0) {
                        conditionSatisfiedMaps.push({
                            conditionIndex: conditionIndex,
                            totalAvailableQuantity: item.availableQuantity,
                        });
                    } else {
                        conditionSatisfiedMaps[inIndex].totalAvailableQuantity +=
                            item.availableQuantity;
                    }
                }
            }
        }
    }
    return {
        conditionSatisfiedMaps,
        conditionSatisfiedMapsWithoutDuplicate,
        conditionSatisfiedMapsWithDuplicate,
    };
}

function calculateMaxApplicableCountWithDuplicateItems(
    conditions,
    conditionSatisfiedMapsWithoutDuplicate, //  {conditionIndex:number, totalAvailableQuantity:number}
    conditionSatisfiedMapsWithDuplicate, // {item, matchedConditions:[]number, assignableQuantity:number}
    maxApplicableCount,
) {
    // check 假定的最大apply次数是否成
    // 先计算出每个condition成立需要的item数量，以及缺少的数量
    const conditionNeededMaps = []; // {conditionIndex:number, neededQuantity:number}
    for (let index = 0; index < conditions.length; index++) {
        const conditionItem = conditions[index];
        const { minQuantity } = conditionItem;
        const requiredQuantity = minQuantity * maxApplicableCount;
        // 与当前已有的非重复item的数量进行对比
        const existingConditionSatisfiedMapsWithoutDuplicate = find(
            conditionSatisfiedMapsWithoutDuplicate,
            map => map.conditionIndex === index,
        );

        const existingQuantity = existingConditionSatisfiedMapsWithoutDuplicate
            ? existingConditionSatisfiedMapsWithoutDuplicate.totalAvailableQuantity
            : 0;
        // 如果已有数量小于需要的数量，则采集数据
        // 这里conditionNeededMaps一定是有值的

        if (existingQuantity < requiredQuantity) {
            const neededQuantity = requiredQuantity - existingQuantity;
            conditionNeededMaps.push({
                conditionIndex: index,
                neededQuantity,
            });
        }
    }

    const duplicateItemsMaps = map(conditionSatisfiedMapsWithDuplicate, conditionSatisfiedMap => {
        const { item, matchedConditions, assignableQuantity } = conditionSatisfiedMap;
        return { itemId: item.id, matchedConditions, assignableQuantity };
    });

    // Duplicate items分配方案
    // 基于第一个condition列举分配方案，然后后面再基于前面的分配方案进行分配，直到获取所有的可能性
    let remainedDuplicateItemsCombosMaps = [duplicateItemsMaps];
    for (const conditionNeededMap of conditionNeededMaps) {
        const { conditionIndex, neededQuantity } = conditionNeededMap;

        for (const remainedDuplicateItemsComboMaps of remainedDuplicateItemsCombosMaps) {
            // 获取可分配的item
            const matchedItems = filter(remainedDuplicateItemsComboMaps, map =>
                map.matchedConditions.includes(conditionIndex),
            );
            // 计算可分配的总数
            // const totalAssignableQuantity = sumBy(matchedItems, assignableQuantity);
            // 如果剩余的可分配数量足够时，排列与组合 ： 阶乘
            // 直接每种可能性后剩余的item的quantity
            remainedDuplicateItemsCombosMaps = getRemaindItemsCombosWithCondition(
                neededQuantity,
                remainedDuplicateItemsComboMaps,
                matchedItems,
            );
        }
    }
    if (remainedDuplicateItemsCombosMaps.length === 0) {
        return calculateMaxApplicableCountWithDuplicateItems(
            conditions,
            conditionSatisfiedMapsWithoutDuplicate, //  {conditionIndex:number, totalAvailableQuantity:number}
            conditionSatisfiedMapsWithDuplicate, // {item, matchedConditions:[]number, assignableQuantity:number}
            maxApplicableCount - 1,
        );
    } else {
        return maxApplicableCount;
    }
}

function getRemaindItemsCombosWithCondition(neededQuantity, items, matchedItems) {
    const remainedDuplicateItemsCombosMaps = [];
    let remainedComosMaps = [items];
    for (const matchedItem of matchedItems) {
        const newRemainedItemsMaps = [];
        for (const remainedItems of remainedComosMaps) {
            const targetItemsIndex = findIndex(
                remainedItems,
                item => item.itemId === matchedItem.itemId,
            );
            const targetItem = remainedItems[targetItemsIndex];
            const curTotal = sumBy(remainedItems, 'usedQuantity') || 0;
            for (
                let i = 0;
                i <= Math.min(neededQuantity - curTotal, targetItem.assignableQuantity);
                i++
            ) {
                //减去 newResul中的quantity
                const newRemainedItems = cloneDeep(remainedItems);
                newRemainedItems[targetItemsIndex].assignableQuantity -= i;
                newRemainedItems[targetItemsIndex].usedQuantity = i;
                const newTotal = sumBy(newRemainedItems, 'usedQuantity');
                if (newTotal === neededQuantity) {
                    forEach(newRemainedItems, item => (item.usedQuantity = 0));
                    remainedDuplicateItemsCombosMaps.push(newRemainedItems);
                } else {
                    newRemainedItemsMaps.push(newRemainedItems);
                }
            }
        }
        remainedComosMaps = newRemainedItemsMaps;
    }
    return remainedDuplicateItemsCombosMaps;
}

function deduceCombo(combos, combo, itemsMap, options) {
    const mergedCombos = getCombosProduct(combos, [combo], itemsMap);

    if (mergedCombos.length === 0) {
        if (_.sum(Object.values(combo)) < options.minQuantity) {
            return null;
        }
        return combo;
    }

    for (const mc of mergedCombos) {
        // eslint-disable-next-line
        return deduceCombo(combos, mc, itemsMap, options);
    }
}

function getCombosProduct(combos1, combos2, itemsMap) {
    const newCombos = [];

    for (const c1 of combos1) {
        for (const c2 of combos2) {
            const itemIds1 = Object.keys(c1);
            const itemIds2 = Object.keys(c2);
            const itemIds = _.uniq(itemIds1.concat(itemIds2));
            let newCombo = {};
            // 当applied promo的quantity大于item的quantity时，就跳出循环
            // 比如，当min = 4, max = 5时
            // 如果quantityA = 4, quantity = 5, 那么结果是对的
            // 如果 quantityA = 7, 那么应该是 {appliedA = 4, appliedB = 1}或者{appliedA = 0, appliedB = 5}
            // 如果是其他的，可能会有更复杂的可能性
            for (const id of itemIds) {
                const appliedQuantity = (c1[id.toString()] || 0) + (c2[id.toString()] || 0);
                if (itemsMap[id.toString()].availableQuantity >= appliedQuantity) {
                    newCombo[id.toString()] = appliedQuantity;
                } else {
                    newCombo = {};
                    break;
                }
            }
            if (!_.isEmpty(newCombo)) {
                newCombos.push(newCombo);
            }
        }
    }
    return newCombos;
}

function generateItemsMap(items) {
    const itemsMap = {};
    if (items.length === 0) return {};
    for (const item of items) {
        if (!item.itemType) {
            itemsMap[item.id] = item;
        }
    }
    return itemsMap;
}

export default {
    getConditionSatisfiedMaps,
    deduceConditionCombo,
    deduceCombo,
    generateItemsMap,
    getCombosProduct,
    deduceMaxApplicableCase,
    generateTemporaryConditions,
};
