import moment from 'moment-timezone';
import _ from 'lodash';
import { SourceType, PromotionType, getOrderSource } from '../utils';
import combination from './combination';
import combinationHelpers from './combinationHelpers';
import { PromotionError, ParameterError } from './error';

export const isNotInDateRange = (promotion, timezone) => {
    const { validFrom, validTo } = promotion;
    const from = moment(validFrom).tz(timezone);
    const to = moment(validTo).tz(timezone);
    const now = moment().tz(timezone);
    return (validFrom && now.isBefore(from)) || (validTo && now.isAfter(to));
};

export const isNotInWeekdays = (promotion, timezone) => {
    const { validDays } = promotion;
    const now = moment().tz(timezone);
    const weekday = now.weekday();

    if (validDays && validDays.length > 0) {
        return !validDays.includes(weekday + 1);
    }
    return false;
};

export const isNotInTimeRange = (promotion, timezone) => {
    const { validTimeFrom, validTimeTo } = promotion;
    const now = moment().tz(timezone);
    const hour = now.hour();
    return (validTimeFrom && hour < validTimeFrom) || (validTimeTo && hour >= validTimeTo);
};

export const isNotInAcrossTimeRange = (promotion, timezone) => {
    const { validTimeFrom, validTimeTo } = promotion;
    const now = moment().tz(timezone);
    const hour = now.hour();
    if (validTimeFrom && validTimeTo && validTimeTo <= validTimeFrom) {
        if (validTimeTo === validTimeFrom) {
            return false;
        } else {
            return hour >= validTimeTo && hour < validTimeFrom;
        }
    }
    return (validTimeFrom && hour < validTimeFrom) || (validTimeTo && hour >= validTimeTo);
};

export const checkPromotionCustomerValid = (promotionCustomers, customerId, timezone) => {
    if (Array.isArray(promotionCustomers) && promotionCustomers.length > 0) {
        for (const promoCustomer of promotionCustomers) {
            const { validFrom, validTo, restClaimCount } = promoCustomer;
            if (
                promoCustomer.customerId === customerId &&
                !isNotInDateRange({ validFrom, validTo }, timezone) &&
                restClaimCount > 0
            ) {
                return true;
            }
        }
    }
    return false;
};

export const checkPromotionValid = (
    promotion,
    {
        timezone,
        storeId,
        businessName,
        source,
        userId,
        customerClaimCount,
        clientType,
        hasUniversalFirstPurchase,
        businessClaimedCount,
        promotionCustomers,
        customerId,
        channel,
    },
) => {
    const {
        _id,
        isEnabled,
        isDeleted,
        appliedStores,
        enableClaimLimit = false,
        maxClaimCount,
        claimedCount = 0,
        appliedSources,
        enablePerCustomerClaimLimit,
        enablePerBusinessClaimLimit,
        perCustomerClaimLimit,
        perBusinessClaimLimit,
        conditions,
        appliedClientTypes,
        isBindCampaign,
    } = promotion;

    if (!_id) {
        throw new ParameterError('promotion._id');
    }

    if (
        enablePerCustomerClaimLimit ||
        _.find(conditions, condition =>
            _.includes(
                ['hasFirstPurchaseBeep', 'hasFirstPurchaseEcommerce', 'hasFirstPurchasePOS'],
                _.get(condition, 'propertyName'),
            ),
        )
    ) {
        if (!userId) {
            throw new PromotionError.REQUIRE_CUSTOMER();
        }

        if (isNaN(customerClaimCount) && source !== SourceType.ECOMMERCE) {
            throw new ParameterError('customerClaimCount');
        }

        if (customerClaimCount >= perCustomerClaimLimit) {
            throw new PromotionError.REACH_CUSTOMER_CLAIM_COUNT_LIMIT();
        }
    }

    if (enablePerBusinessClaimLimit && businessClaimedCount >= perBusinessClaimLimit) {
        throw new PromotionError.REACH_MAX_BUSINESS_CLAIM_COUNT();
    }

    if (isDeleted) {
        throw new PromotionError.DELETED_PROMOTION();
    }

    if (!isEnabled) {
        throw new PromotionError.NOT_ENABLE();
    }

    if (
        promotion.type !== PromotionType.UNIVERSAL &&
        (!businessName || !promotion.business || businessName !== promotion.business)
    ) {
        throw new PromotionError.REQUIRE_SAME_BUSINESS();
    }

    if (source === SourceType.UNKNOWN || !Object.values(SourceType).includes(source)) {
        throw new PromotionError.REQUIRE_SOURCE();
    }

    if (Array.isArray(appliedSources) && !appliedSources.includes(source)) {
        throw new PromotionError.SOURCE_NOT_SATISFIED();
    }

    // exclude ECOMMERCE
    if (SourceType.ECOMMERCE !== source) {
        if (
            !Array.isArray(appliedStores) ||
            (!appliedStores.includes(storeId) && !appliedStores.includes('All'))
        ) {
            throw new PromotionError.STORE_NOT_SATISFIED();
        }
    }

    // claim count check
    if (SourceType.POS !== source && enableClaimLimit && !(claimedCount < maxClaimCount)) {
        throw new PromotionError.REACH_MAX_CLAIM_COUNT();
    }

    // date valid check
    if (isNotInDateRange(promotion, timezone)) {
        throw new PromotionError.DATE_NOT_SATISFIED();
    }

    if (isNotInWeekdays(promotion, timezone)) {
        throw new PromotionError.WEEKDAY_NOT_SATISFIED();
    }
    // time valid check
    if (isNotInAcrossTimeRange(promotion, timezone)) {
        throw new PromotionError.TIME_NOT_SATISFIED();
    }

    if (
        appliedClientTypes &&
        appliedClientTypes.length > 0 &&
        !appliedClientTypes.includes(clientType)
    ) {
        throw new PromotionError.CLIENT_NOT_SATISFIED();
    }

    if (
        promotion.type === PromotionType.UNIVERSAL &&
        promotion.requireFirstPurchase &&
        hasUniversalFirstPurchase
    ) {
        throw new PromotionError.REQUIRE_FIRST_TIME_PURCHASE();
    }

    if (isBindCampaign && !checkPromotionCustomerValid(promotionCustomers, customerId, timezone)) {
        throw new PromotionError.PROMOTION_CUSTOMER();
    }

    return null;
};

export const checkRequiredProducts = (promotion, transaction) => {
    const { items } = transaction;
    let { requiredProducts } = promotion; // eslint-disable-line
    const itemsMap = combinationHelpers.generateItemsMap(items);
    if (requiredProducts.length !== 0) {
        const combos = combination.generatePossibleUnitCombos(
            { conditions: requiredProducts },
            items,
            itemsMap,
        );
        if (!combos || combos.length === 0) {
            throw new PromotionError.REQUIRED_PRODUCTS_NOT_SATISFIED();
        }
    }
    return null;
};

export default function check(promotion, transaction, businessSettings) {
    const source = getOrderSource(transaction);
    const options = {
        timezone: businessSettings.timezone,
        storeId: transaction.storeId,
        businessName: transaction.business,
        userId: transaction.userId,
        channel: transaction.channel,
        customerClaimCount: (transaction.customer || {}).customerClaimCount,
        source,
        clientType: transaction.clientType,
        hasUniversalFirstPurchase: (transaction.customer || {}).hasUniversalFirstPurchase,
        businessClaimedCount: (businessSettings.businessPromotionClaimedCountMap || {})[
            transaction.business
        ],
        promotionCustomers: transaction.promotionCustomers,
        customerId: transaction.customerId,
    };
    checkPromotionValid(promotion, options);
    checkRequiredProducts(promotion, transaction);
}
