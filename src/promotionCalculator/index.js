import _ from 'lodash';
import combination from './combination';
import conditionCalculator from './conditionCalculator';
import check from './check';
import { ParameterError, PromotionCheckConditionError, PromotionError } from './error';
import { PromotionType, sortConditions, ChannelType, PromotionDiscountType } from '../utils';

const transactionLevelPromotions = [
    PromotionDiscountType.ABSOLUTE,
    PromotionDiscountType.COMBO,
    PromotionDiscountType.FREE_SHIPPING,
];
const itemLevelPromotions = [
    PromotionDiscountType.PERCENTAGE,
    PromotionDiscountType.FIXED_UNIT_PRICE,
    PromotionDiscountType.BUY_X_FREE_Y,
];

/* Changes logs.
      1: Updated in 3.8.6 for fix the CM-4585.
        - add promotionPlaceholderQuantityMap field on Transaction to record the X in buyXFreeY, 
          So for buyXfreeY Promo, the logic now is that after applying it, neither X nor Y can 
          apply other item level Promo. Before that, only Y can't apply. 
          This solves the issue that multiple Buyxfreeys can be applied to the same items at the same time
        - add quantity field on applied promotions, when calculating the Promotion discount, 
          it will be calculated based on it instead of item.quantity
*/
export function calculate(transaction, promotions, business) {
    // initialize
    delete transaction.promotions;
    for (const item of transaction.items) {
        item.availableQuantity = item.quantity;
        delete item.promotions;
        delete item.promotionAppliedQuantityMap;
        delete item.promotionPlaceholderQuantityMap;
    }

    // remove useless fields
    for (const promotion of promotions) {
        if (
            promotion.discountType === PromotionDiscountType.COMBO ||
            promotion.discountType === PromotionDiscountType.FREE_SHIPPING ||
            promotion.discountType === PromotionDiscountType.BUY_X_FREE_Y
        ) {
            delete promotion.maxQuantity;
            delete promotion.minQuantity;
        }
        // if channel === ChannelType.POS, should remove all Conditions which propertyName is included in ['hasFirstPurchaseBeep', 'hasFirstPurchaseEcommerce', 'hasFirstPurchasePOS','hasUniversalFirstPurchase']
        if (transaction.channel === ChannelType.POS) {
            promotion.conditions = _.filter(promotion.conditions, condition => {
                return !_.includes(
                    [
                        'hasFirstPurchaseBeep',
                        'hasFirstPurchaseEcommerce',
                        'hasFirstPurchasePOS',
                        'hasUniversalFirstPurchase',
                    ],
                    _.get(condition, 'propertyName'),
                );
            });
        }
    }

    const errors = [];
    for (const promotion of promotions) {
        try {
            check(promotion, transaction, business);
            if (promotion.type === 'universal') {
                promotion.conditions = sortConditions(promotion.conditions);
            }
            const itemLevelConditions = promotion.conditions.filter(
                cond => cond.entity === 'product',
            );
            const transactionLevelConditions = promotion.conditions.filter(
                cond =>
                    cond.entity === 'transaction' ||
                    cond.entity === 'customer' ||
                    cond.entity === 'business',
            );

            if (promotion.discountType === 'buyXFreeY') {
                itemLevelConditions[0].minQuantity += promotion.discountValue;
            }

            // check transactionLevelConditions is satisfied
            for (const condition of transactionLevelConditions) {
                let condSatisfied = false;
                if (condition.entity === 'customer') {
                    if (!transaction.customer) throw new ParameterError('transaction.customer');
                    condSatisfied = conditionCalculator.isSatisfied(
                        condition,
                        transaction.customer,
                    );
                } else if (condition.entity === 'transaction') {
                    condSatisfied = conditionCalculator.isSatisfied(condition, transaction);
                } else if (condition.entity === 'business') {
                    condSatisfied = conditionCalculator.isSatisfied(condition, business);
                }

                if (
                    !condSatisfied &&
                    _.includes(
                        [
                            'hasFirstPurchaseBeep',
                            'hasFirstPurchaseEcommerce',
                            'hasFirstPurchasePOS',
                        ],
                        condition.propertyName,
                    )
                ) {
                    throw new PromotionError.REQUIRE_FIRST_TIME_PURCHASE();
                }

                if (!condSatisfied) {
                    throw new PromotionCheckConditionError(condition);
                }
            }

            // check itemLevelConditions is satisfied
            let combo;
            let comboTaxRate;
            let promotionWithResult;
            if (itemLevelConditions.length > 0) {
                for (const item of transaction.items) {
                    if (!item.itemType) {
                        if (!item.product) throw new ParameterError('items.product');
                    }
                }
                const promotionWithItemLevelConditions = {
                    ...promotion,
                    maxQuantity: promotion.maxQuantity,
                    minQuantity: promotion.minQuantity,
                    conditions: itemLevelConditions,
                };

                const calculateComboResult = combination.getBestCombo(
                    transaction.items,
                    promotionWithItemLevelConditions,
                );
                combo = calculateComboResult.bestCombo;
                promotionWithResult = calculateComboResult.promotionWithResult;

                if (_.isEmpty(combo)) {
                    throw new PromotionCheckConditionError();
                } else {
                    // add promotionAppliedQuantityMap
                    for (const item of transaction.items) {
                        if (!item.itemType) {
                            if (!item.promotionAppliedQuantityMap)
                                item.promotionAppliedQuantityMap = {};
                            if (!item.promotionPlaceholderQuantityMap)
                                item.promotionPlaceholderQuantityMap = {};
                            if (combo[item.id]) {
                                item.promotionAppliedQuantityMap[promotion._id.toString()] =
                                    combo[item.id];
                            }
                            const placeholderItemIds = _.get(combo, 'placeholderItemIds', {});
                            if (placeholderItemIds[item.id]) {
                                item.promotionPlaceholderQuantityMap[promotion._id.toString()] =
                                    placeholderItemIds[item.id];
                            }
                        }
                    }
                }
            }
            // if we can remove this limit for all amount off promotions?
            // now, if removed, tax inclusive, transaction.promotions.discount(always tax exclusive) is wrong
            // because there is no taxRate to calculate tax exclusive discount.
            // check if applied items share same taxCode
            if (promotion.discountType === PromotionDiscountType.COMBO) {
                let itemIds;
                if (combo) {
                    itemIds = Object.keys(combo);
                } else {
                    itemIds = transaction.items.filter(item => !item.itemType).map(item => item.id);
                }

                for (let i = 0; i < itemIds.length; i += 1) {
                    const currItem = transaction.items.find(item => item.id === itemIds[i]);
                    if (
                        promotion.discountType === PromotionDiscountType.COMBO &&
                        typeof promotion.taxCode === 'string' &&
                        currItem.taxCode !== promotion.taxCode
                    ) {
                        throw new PromotionCheckConditionError(null, 'taxcode');
                    }
                    if (itemIds[i + 1]) {
                        const nextItem = transaction.items.find(item => item.id === itemIds[i + 1]);

                        if (isNaN(currItem.taxRate) || isNaN(nextItem.taxRate)) {
                            throw new ParameterError('items.taxRate');
                        }
                        if (currItem.taxRate !== nextItem.taxRate) {
                            throw new PromotionCheckConditionError(null, 'taxcode');
                        }
                    }
                    comboTaxRate = currItem.taxRate;
                }
            }

            if (promotion.discountType === PromotionDiscountType.COMBO) {
                if (typeof promotion.taxCode !== 'string') {
                    throw new ParameterError('promotion.taxCode');
                }
            }

            if (itemLevelConditions.length === 0) {
                for (const item of transaction.items) {
                    if (!item.itemType) {
                        if (!item.promotions) item.promotions = [];

                        if (itemLevelConditions.length === 0) {
                            if (!item.promotionAppliedQuantityMap)
                                item.promotionAppliedQuantityMap = {};
                            if (!item.promotionPlaceholderQuantityMap)
                                item.promotionPlaceholderQuantityMap = {};
                            item.promotionAppliedQuantityMap[promotion._id.toString()] =
                                item.availableQuantity;
                            item.promotionPlaceholderQuantityMap[promotion._id.toString()] = 0;
                        }
                    }
                }
            }
            // add appliedPromotion for transactionLevelPromotions
            if (transactionLevelPromotions.includes(promotion.discountType)) {
                let promotionQuantity;
                if (!transaction.promotions) transaction.promotions = [];

                const appliedPromotion = {
                    inputValue: promotion.discountValue,
                    type: promotion.discountType,
                    discountType: promotion.discountType,
                    originalDiscountType: promotion.originalDiscountType,
                    promotionId: promotion._id.toString(),
                    promotionCode: promotion.promotionCode,
                    promotionName: promotion.name,
                    taxCode: promotion.taxCode,
                    taxRate: comboTaxRate || promotion.taxRate,
                    storehubPaidPercentage: promotion.storehubPaidPercentage,
                    promotionType: promotion.type || PromotionType.MERCHANT,
                    maxDiscountAmount: promotion.maxDiscountAmount,
                };

                if (Boolean(promotion.uniquePromotionCodeInfo)) {
                    appliedPromotion.uniquePromotionCodeId = promotion.uniquePromotionCodeInfo.id;
                    appliedPromotion.uniquePromotionCode =
                        promotion.uniquePromotionCodeInfo.promotionCode;
                }

                // calculate promotion quantity and taxRate
                if (promotion.discountType === PromotionDiscountType.COMBO) {
                    const totalComboConsumedQty = _.sum(_.values(combo));
                    const onePromotionQuantity = _.sumBy(promotion.conditions, 'maxQuantity');
                    promotionQuantity = _.round(totalComboConsumedQty / onePromotionQuantity);
                    appliedPromotion.quantity = promotionQuantity;
                } else if (promotion.discountType === PromotionDiscountType.ABSOLUTE) {
                    let applyCount = 1;
                    if (Boolean(combo) && Boolean(promotionWithResult)) {
                        applyCount = promotionWithResult.maxApplicableCount;
                    }
                    appliedPromotion.quantity = applyCount;
                }

                transaction.promotions.push(appliedPromotion);
            }

            // add appliedPromotion for itemLevelPromotions
            if (itemLevelPromotions.includes(promotion.discountType)) {
                if (itemLevelConditions.length === 0) {
                    const totalAvailableQuantity = _.sumBy(
                        transaction.items.filter(item => !item.itemType),
                        'availableQuantity',
                    );

                    if (totalAvailableQuantity === 0) {
                        throw new PromotionCheckConditionError(null, 'quantity not enough');
                    }
                }
                for (const item of transaction.items) {
                    if (
                        !item.itemType &&
                        itemLevelPromotions.includes(promotion.discountType) &&
                        item.availableQuantity > 0
                    ) {
                        if (!item.promotions) item.promotions = [];
                        if (
                            itemLevelConditions.length === 0 ||
                            item.promotionAppliedQuantityMap[promotion._id.toString()]
                        ) {
                            let inputValue;
                            let quantity;
                            if (promotion.discountType === PromotionDiscountType.BUY_X_FREE_Y) {
                                inputValue =
                                    _.get(item, [
                                        'promotionAppliedQuantityMap',
                                        promotion._id.toString(),
                                    ]) || 0;
                                quantity =
                                    inputValue +
                                        _.get(item, [
                                            'promotionPlaceholderQuantityMap',
                                            promotion._id.toString(),
                                        ]) || 0;
                            } else {
                                inputValue = _.get(promotion, 'discountValue') || 0;
                                quantity =
                                    _.get(item, [
                                        'promotionAppliedQuantityMap',
                                        promotion._id.toString(),
                                    ]) || 0;
                            }
                            const appliedPromotion = {
                                inputValue,
                                quantity,
                                type: promotion.discountType,
                                discountType: promotion.discountType,
                                originalDiscountType: promotion.originalDiscountType,
                                promotionId: promotion._id.toString(),
                                promotionCode: promotion.promotionCode,
                                promotionName: promotion.name,
                                taxCode: promotion.taxCode,
                                storehubPaidPercentage: promotion.storehubPaidPercentage,
                                promotionType: promotion.type || PromotionType.MERCHANT,
                                maxDiscountAmount: promotion.maxDiscountAmount,
                            };
                            if (Boolean(promotion.uniquePromotionCodeInfo)) {
                                appliedPromotion.uniquePromotionCodeId =
                                    promotion.uniquePromotionCodeInfo.id;
                                appliedPromotion.uniquePromotionCode =
                                    promotion.uniquePromotionCodeInfo.promotionCode;
                            }

                            item.promotions.push(appliedPromotion);
                        }
                    }
                }
            }

            // calculate availableQuantity
            // 这里对于buyXFreeY的处理不正确，因为这里combo对应的数量只是打折的数量，并且并没有标记其他参与buyXFreeY但是未打折的商品
            // 比如，buy5free1 (with Product A or Product B), 当我购买3个A和3个B时，假设最终A是free1， 这里的计算结果会是A:  availableQuantity = 2 ,  B:  availableQuantity = 3
            // 那么这样会造成，当有多个buyXFreeY的promotion时，就会造成Promo重复，比如同时有buy5free1 and buy8free2, 那么当买10个的时候，就会造成同时apply这两个Promo
            // 由于当前计算库中，对于percentage off和amount off的Promotion，并没有明确的界定何时为item level何时为order level，所以当前去支持一个item只能apply一个item level promo是没有意义的
            for (const item of transaction.items) {
                if (!item.itemType) {
                    if (
                        promotion.discountType === PromotionDiscountType.COMBO ||
                        promotion.discountType === PromotionDiscountType.BUY_X_FREE_Y
                    ) {
                        item.availableQuantity -=
                            (_.get(combo, ['placeholderItemIds', item.id]) || 0) +
                            (_.get(combo, item.id) || 0);
                    }
                }
            }
        } catch (error) {
            errors.push({
                code: error.code,
                message: error.message,
                promotionId: promotion._id,
            });
        }
    }

    transaction.items.forEach(item => delete item.availableQuantity);
    return errors;
}

export default {
    calculate,
};
