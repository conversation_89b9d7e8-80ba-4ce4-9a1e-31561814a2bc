export class PromotionErrorBase extends Error {
    constructor(message, code) {
        super();
        this.message = message;
        this.name = 'PromotionError';
        this.code = code;
    }
}

export class PromotionCheckConditionError extends Error {
    constructor(condition, description) {
        super();
        this.message = `conditions_not_satisfied ${description || ''} ${
            condition
                ? `${condition.entity}.${condition.propertyName} ${
                      condition.operator
                  } ${JSON.stringify(condition.operand)}`
                : ''
        }`;
        this.name = 'PromotionCheckConditionError';
        this.code = '4407';
    }
}

export class ParameterError extends Error {
    constructor(paramName) {
        super();
        this.message = `invalid_param: ${paramName}`;
        this.name = 'ParameterError';
        this.code = '5500';
    }
}

export const PromotionError = {
    NOT_ENABLE: PromotionErrorBase.bind(null, 'not_enable', '4401'),
    NOT_ONLINE: PromotionErrorBase.bind(null, 'not_online', '4402'),
    DATE_NOT_SATISFIED: PromotionErrorBase.bind(null, 'date_not_satisfied', '4403'),
    WEEKDAY_NOT_SATISFIED: PromotionErrorBase.bind(null, 'weekday_not_satisfied', '4404'),
    TIME_NOT_SATISFIED: PromotionErrorBase.bind(null, 'time_not_satisfied', '4405'),
    REQUIRED_PRODUCTS_NOT_SATISFIED: PromotionErrorBase.bind(
        null,
        'required_products_not_satisfied',
        '4406',
    ),

    REQUIRE_SAME_BUSINESS: PromotionErrorBase.bind(null, 'require_same_business', '4408'),
    STORE_NOT_SATISFIED: PromotionErrorBase.bind(null, 'store_not_satisfied', '4409'),
    DELETED_PROMOTION: PromotionErrorBase.bind(null, 'deleted_promotion', '4410'),
    REACH_MAX_CLAIM_COUNT: PromotionErrorBase.bind(null, 'reach_max_claim_count', '4411'),
    REQUIRE_CUSTOMER: PromotionErrorBase.bind(null, 'require_customer', '4412'),
    REACH_CUSTOMER_CLAIM_COUNT_LIMIT: PromotionErrorBase.bind(
        null,
        'reach_customer_claim_count_limit',
        '4413',
    ),
    REQUIRE_FIRST_TIME_PURCHASE: PromotionErrorBase.bind(
        null,
        'require_first_time_purchase',
        '4414',
    ),
    REQUIRE_SOURCE: PromotionErrorBase.bind(null, 'require_source', '4415'),
    SOURCE_NOT_SATISFIED: PromotionErrorBase.bind(null, 'source_not_satisfied', '4416'),
    CLIENT_NOT_SATISFIED: PromotionErrorBase.bind(null, 'client_not_satisfied', '4418'),
    REACH_MAX_BUSINESS_CLAIM_COUNT: PromotionErrorBase.bind(
        null,
        'reach_max_business_claim_count',
        '4420',
    ),
    PROMOTION_CUSTOMER: PromotionErrorBase.bind(null, 'promotion_customers_not_satisfied', '4421'),
};

export default {
    PromotionError,
    ParameterError,
    PromotionCheckConditionError,
};
