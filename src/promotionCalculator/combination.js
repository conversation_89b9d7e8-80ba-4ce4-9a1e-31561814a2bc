/* eslint-disable no-continue, no-loop-func */

import _ from 'lodash';
import combinationHelpers from './combinationHelpers';
import conditionCalculator from './conditionCalculator';
import { PromotionDiscountType } from '../utils';

export function generatePossibleUnitCombos(promotion, items, itemsMap) {
    const conditionSatisfiedMaps = combinationHelpers.getConditionSatisfiedMaps(
        items,
        promotion.conditions,
    );

    let possibleCombos = [{}];

    const conditionsCombos = [];
    if (promotion.minQuantity === 0 || promotion.maxQuantity === 0) {
        return [];
    }

    for (const satisfiedMap of conditionSatisfiedMaps) {
        const { condition } = satisfiedMap;
        const conditionCombos = [];
        const prevConditionsCombos = _.clone(possibleCombos);
        for (const item of satisfiedMap.satisfiedBy) {
            const conditionCombo = combinationHelpers.deduceConditionCombo(
                item,
                satisfiedMap.satisfiedBy,
                condition,
            );

            if (!_.isEmpty(conditionCombo)) {
                conditionCombos.push(conditionCombo);
            }
        }
        if (conditionCombos.length === 0) {
            return [];
        }
        conditionsCombos.push(conditionCombos);

        // combine combos satisfied previous conditions with combos satisfied current condition
        possibleCombos = combinationHelpers.getCombosProduct(
            conditionCombos,
            prevConditionsCombos,
            itemsMap,
        );
    }

    if (
        possibleCombos.length === 0 ||
        (possibleCombos.length === 1 && _.isEmpty(possibleCombos[0]))
    ) {
        return [];
    }

    items.forEach(item => {
        delete item.consumedQuantity;
    });

    if (!Object.keys(promotion).includes('maxApplicableCount') && promotion.maxQuantity > 0) {
        possibleCombos = possibleCombos.filter(combo => {
            let itemsQuantity = 0;
            _.keys(combo).forEach(itemId => {
                itemsQuantity += itemsMap[itemId].quantity;
            });
            return itemsQuantity <= promotion.maxQuantity;
        });
    }
    return possibleCombos;
}

export function generateSaturatedCombos(promotion, items, itemsMap) {
    const combos = generatePossibleUnitCombos(promotion, items, itemsMap);
    const { maxQuantity, minQuantity, discountType } = promotion;

    /*
    在最开始时，当ABSOLUTE，PERCENTAGE，FIXED_UNIT_PRICE这三种Promo，当avliableQuantity大于maxQuantity时，
    这里的combos会是空数组，因为被上面generatePossibleUnitCombos方法中最后的过滤给过滤掉了
    那么现在的逻辑是，无论是否是repeatale，当avliableQuantity大于maxQuantity时，都要apply promo，
    1. 而repeatable的情况下走的是直接算出有多少item（quantity）应该apply Promo,也就是直接把这个quantity赋值给condition.minQuantity，
    直接截取最终应该有多少item应该apply Promo,这里generatePossibleUnitCombos会直接根据condition.minQuantity算出所有可能的组合，组合中
    的quantity就是condition.minQuantity，所以这里后面的逻辑是不需要的
    2. 那么当不可以repeat的情况下，下面的deduceCombo方法会根据maxQuantity算出更多的applyQuantity,导致当avliableQuantity大于maxQuantity时，
    算出的applyQuantity会是maxQuantity的整数倍，这是错误的，而上面的generatePossibleUnitCombos方法是完全可以满足getBestCombo要求的所有组合的
    3. 当然，这里根据整理的文档来看是过于复杂的，后面可以进行重构，目前的代码性能过低，且可读性太差
    */
    if (
        [
            PromotionDiscountType.ABSOLUTE,
            PromotionDiscountType.PERCENTAGE,
            PromotionDiscountType.FIXED_UNIT_PRICE,
        ].includes(discountType)
    ) {
        return combos.filter(combo => {
            return _.sum(_.values(combo)) >= minQuantity;
        });
    }
    let saturatedCombos = [];
    for (const combo of combos) {
        const usedCombos = saturatedCombos.filter(
            scombo => _.intersection(Object.keys(combo), Object.keys(scombo)).length > 0,
        );

        const appendableCombos = combos.filter(
            c =>
                usedCombos.filter(uc => _.intersection(Object.keys(uc), Object.keys(c)).length > 0)
                    .length <= 0,
        );

        const saturatedCombo = combinationHelpers.deduceCombo(appendableCombos, combo, itemsMap, {
            maxQuantity,
            minQuantity,
        });

        if (_.isEmpty(saturatedCombo)) {
            continue;
        }

        let finalSaturatedCombo = saturatedCombo;
        let remainPossibleCombos = [];
        let comboQuantity = _.sumBy(Object.values(finalSaturatedCombo));

        do {
            const remainItems = items
                .filter(item => !item.itemType)
                .map(item => ({
                    ...item,
                    availableQuantity: item.availableQuantity - (finalSaturatedCombo[item.id] || 0),
                }));

            remainPossibleCombos = generatePossibleUnitCombos(promotion, remainItems, itemsMap);

            if (
                promotion.discountType !== PromotionDiscountType.COMBO &&
                promotion.discountType !== PromotionDiscountType.BUY_X_FREE_Y
            ) {
                if (remainPossibleCombos.length === 0) {
                    for (const condition of promotion.conditions) {
                        const satisfiedItems = remainItems.filter(item =>
                            conditionCalculator.isSatisfied(condition, item.product),
                        );

                        for (const item of satisfiedItems) {
                            const conditionCombo = combinationHelpers.deduceConditionCombo(
                                item,
                                satisfiedItems,
                                condition,
                            );

                            if (!_.isEmpty(conditionCombo))
                                remainPossibleCombos.push(conditionCombo);
                        }
                    }
                }
            }

            if (remainPossibleCombos.length > 0) {
                const newFinalBestCombo = combinationHelpers.deduceCombo(
                    remainPossibleCombos,
                    finalSaturatedCombo,
                    itemsMap,
                    { maxQuantity, minQuantity },
                );
                const newComboQuantity = _.sumBy(Object.values(newFinalBestCombo));

                // stop calculation if quantity can't be add up any more
                if (newComboQuantity > comboQuantity) {
                    comboQuantity = newComboQuantity;
                    finalSaturatedCombo = newFinalBestCombo;
                }
            }
        } while (remainPossibleCombos.length > 0);
        saturatedCombos.push(finalSaturatedCombo);

        // if (!Object.keys(promotion).includes('maxApplicableCount') && promotion.maxQuantity > 0) {
        //     saturatedCombos = saturatedCombos.filter(sc => {
        //         let itemsQuantity = 0;
        //         _.keys(sc).forEach(itemId => {
        //             itemsQuantity += itemsMap[itemId].quantity;
        //         });
        //         return itemsQuantity <= promotion.maxQuantity;
        //     });
        // }
    }
    return saturatedCombos;
}

export function getBestCombo(items, promotion) {
    for (const purchaseItem of items) {
        let productPrice = purchaseItem.unitPrice || 0;
        if (purchaseItem.selectedOptions && purchaseItem.selectedOptions.length > 0) {
            for (const selectedOption of purchaseItem.selectedOptions) {
                productPrice += (selectedOption.optionValue || 0) * selectedOption.quantity;
            }
        }
        purchaseItem.productPrice = productPrice;
    }

    let resultType = 'max';
    if (promotion.discountType === 'buyXFreeY') {
        resultType = 'min';
    }
    const itemsMap = combinationHelpers.generateItemsMap(items);
    combinationHelpers.generateTemporaryConditions(promotion, items);
    let sortCondition;
    if (resultType === 'min') {
        sortCondition = 'productPrice';
    } else {
        sortCondition = '-productPrice';
    }
    const sortedItems = _.sortBy(items, sortCondition);
    // 这里返回针对当前item level promo的各种可能产生的优惠情况，后面再进行比对，得到最优情况
    const saturatedCombos = generateSaturatedCombos(promotion, sortedItems, itemsMap);

    // get the best combo by rule
    let maxAmount = 0;
    let minAmount = Infinity;

    let bestCombo = {};
    // 这里如果是BuyXFreeY则取最小优惠，如果是其他则取最大优惠
    for (const combo of saturatedCombos) {
        const comboItems = Object.keys(combo).map(itemId => itemsMap[itemId.toString()]);
        const amount = _.sumBy(comboItems, item => item.productPrice * combo[item.id]); // NOSONAR
        if (resultType === 'min') {
            if (amount < minAmount) {
                minAmount = amount;
                bestCombo = combo;
            }
        } else {
            if (amount > maxAmount) {
                maxAmount = amount;
                bestCombo = combo;
            }
        }
    }

    // 在这之前，BuyXFreeY的saturatedCombos与其他的Promo一样，比如Buy5Free1，
    // 那么这里的  _.sum(_.values(bestCombo))  会等于6的倍数，而conditionQuantity等于6
    // fix: combo 里面加一个buyXFreeYItemIds :{itemId : quantity},里面仅包含参与了buyXFreeY的X
    if (promotion.discountType === 'buyXFreeY') {
        const finalBestCombo = {};
        let comboQuantity = 0;
        const conditionQuantity = _.sumBy(promotion.conditions, 'minQuantity');
        const promotionQuantity = _.sum(_.values(bestCombo)) / conditionQuantity;

        let itemIds = _.keys(bestCombo);
        itemIds = _.sortBy(itemIds, id => itemsMap[id].productPrice);
        const placeholderItemIds = {};
        // 这里是通过轮询为BuyXFreeY的Promo仅仅分配实际产生discount的item
        // 也就是说，最终只有free的item会有promo，排除了其他参与了BuyXFreeY但是没有promo discount的item（问题所在）
        _.forEach(itemIds, itemId => {
            // 这里的item已经经过上面的排序，比如BuyXFreeY对对item进行价格从小到大排序
            if (comboQuantity < promotion.discountValue * promotionQuantity) {
                const comboItem = items.find(item => item.id === itemId);
                const freeQuantity =
                    comboItem.availableQuantity >
                    promotion.discountValue * promotionQuantity - comboQuantity
                        ? promotion.discountValue * promotionQuantity - comboQuantity
                        : comboItem.availableQuantity;
                const placeholderQuantity = bestCombo[itemId] - freeQuantity;
                if (placeholderQuantity > 0) {
                    placeholderItemIds[itemId] = placeholderQuantity;
                }
                finalBestCombo[itemId] = freeQuantity;
                comboQuantity += finalBestCombo[itemId];
            } else {
                placeholderItemIds[itemId] = bestCombo[itemId];
            }
        });
        finalBestCombo.placeholderItemIds = placeholderItemIds;
        bestCombo = finalBestCombo;
    }
    for (const purchaseItem of items) {
        delete purchaseItem.productPrice;
    }

    return { bestCombo, promotionWithResult: promotion };
}

export default {
    getBestCombo,
    generatePossibleUnitCombos,
    generateSaturatedCombos,
};
