import Condition from './ConditionCalculator.base';

class ProductCondition extends Condition {
    isSatisfied(product = {}) {
        const { propertyName } = this.condition;

        let result;
        switch (propertyName) {
            case 'id':
                result = this.eval((product._id || '').toString());
                break;
            case 'category':
                result = this.eval(product.category);
                break;
            case 'tags':
                result = this.eval(product.tags);
                break;
            default:
                result = false;
                break;
        }
        return result;
    }
}

export default ProductCondition;
