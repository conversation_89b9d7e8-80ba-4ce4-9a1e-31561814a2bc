import ProductConditionCalculator from './ProductConditionCalculator';
import TransactionConditionCalculator from './TransactionConditionCalculator';
import CustomerConditionCalculator from './CustomerConditionCalculator';
import BusinessConditionCalculator from './BusinessConditionCalculator';

const ConditionEntityTypes = {
    PRODUCT: 'product',
    CUSTOMER: 'customer',
    TRANSACTION: 'transaction',
    BUSINESS: 'business',
};
function isSatisfied(condition, ...args) {
    let conditionCalculator;
    switch (condition.entity) {
        case ConditionEntityTypes.PRODUCT:
            conditionCalculator = new ProductConditionCalculator(condition);
            break;
        case ConditionEntityTypes.CUSTOMER:
            conditionCalculator = new CustomerConditionCalculator(condition);
            break;
        case ConditionEntityTypes.TRANSACTION:
            conditionCalculator = new TransactionConditionCalculator(condition);
            break;
        case ConditionEntityTypes.BUSINESS:
            conditionCalculator = new BusinessConditionCalculator(condition);
            break;
        default:
            return false;
    }
    const result = conditionCalculator.isSatisfied(...args);
    return result;
}

export default {
    isSatisfied,
};
