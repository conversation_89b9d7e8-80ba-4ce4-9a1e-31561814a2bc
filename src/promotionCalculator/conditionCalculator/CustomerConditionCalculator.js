import _ from 'lodash';
import Condition from './ConditionCalculator.base';

class CustomerCondition extends Condition {
    isSatisfied(customer = {}) {
        switch (this.condition.propertyName) {
            case 'tags':
                return this.eval(customer.tags);
            case 'hasFirstPurchaseEcommerce':
                return this.eval(customer.hasFirstPurchaseEcommerce);
            case 'hasFirstPurchaseBeep':
                return this.eval(customer.hasFirstPurchaseBeep);
            case 'hasFirstPurchasePOS':
                return this.eval(customer.hasFirstPurchasePOS);
            case 'hasUniversalFirstPurchase':
                return this.eval(customer.hasUniversalFirstPurchase);
            default:
                return false;
        }
    }
}

export default CustomerCondition;
