import Condition from './ConditionCalculator.base';

class TransactionCondition extends Condition {
    isSatisfied(order = {}) {
        switch (this.condition.propertyName) {
            case 'total':
                // eslint-disable-next-line no-case-declarations
                const shippingFee = Math.max(
                    (order.shippingFee || 0) - (order.shippingFeeDiscount || 0),
                    0,
                );
                return this.eval(order.total - shippingFee);
            case 'subtotal':
                return this.eval(order.subtotal);
            default:
                return false;
        }
    }
}

export default TransactionCondition;
