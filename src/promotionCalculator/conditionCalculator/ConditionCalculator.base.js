import _ from 'lodash';

class Condition {
    constructor(condition) {
        this.condition = condition;
    }

    evalInOperation(leftOperand) {
        const rightOperand = this.condition.operand;
        if (rightOperand.indexOf(leftOperand) !== -1) {
            return true;
        }
        return false;
    }

    evalContainsOperation(leftOperand) {
        const rightOperand = this.condition.operand;
        const intersection = _.intersection(leftOperand, rightOperand);
        if (intersection && intersection.length > 0) {
            return true;
        }
        return false;
    }

    evalGteOperation(leftOperand) {
        const rightOperand = parseFloat(this.condition.operand[0]);
        if (leftOperand >= rightOperand) {
            return true;
        }
        return false;
    }

    evalEqOperation(leftOperand) {
        const rightOperand = this.condition.operand[0];
        if (leftOperand === rightOperand) {
            return true;
        }
        return false;
    }

    eval(leftOperand) {
        const { operator } = this.condition;
        switch (operator) {
            case 'in': {
                if (typeof leftOperand !== 'string') {
                    return false;
                }
                return this.evalInOperation(leftOperand);
            }

            case 'gte': {
                if (typeof leftOperand !== 'number') {
                    return false;
                }
                return this.evalGteOperation(leftOperand);
            }

            case 'eq': {
                if (typeof leftOperand !== 'string') {
                    leftOperand = JSON.stringify(leftOperand);
                }
                return this.evalEqOperation(leftOperand);
            }

            case 'contains': {
                if (!(leftOperand instanceof Array)) {
                    return false;
                }
                return this.evalContainsOperation(leftOperand);
            }

            default:
                return false;
        }
    }
}

export default Condition;
