import Condition from './ConditionCalculator.base';

class BusinessCondition extends Condition {
    isSatisfied(business = {}) {
        switch (this.condition.propertyName) {
            case 'country':
                return this.eval(business.country);
            case 'fulfillmentOptions':
                return this.eval(business.fulfillmentOptions);
            case 'planId':
                return this.eval(business.planId);
            case 'addonIds':
                return this.eval(business.addonIds);
            case 'enableCashback':
                return this.eval(business.enableCashback);
            case 'haveBeepFreeShippingZone':
                return this.eval(business.haveBeepFreeShippingZone);
            case 'qrOrderingSettings.searchingTags':
                return this.eval(
                    (business.qrOrderingSettings && business.qrOrderingSettings.searchingTags) ||
                        [],
                );
            case 'qrOrderingSettings.marketingTags':
                return this.eval(
                    (business.qrOrderingSettings && business.qrOrderingSettings.marketingTags) ||
                        [],
                );
            default:
                return false;
        }
    }
}

export default BusinessCondition;
