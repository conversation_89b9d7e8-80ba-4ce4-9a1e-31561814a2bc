import Decimal from 'decimal.js';

export const DiscountTypeName = {
    promotion: 'promotion',
    fullBill: 'fullBill',
    loyalty: 'loyalty',
};

export const discountFieldName = {
    promotion: 'promotionDiscount',
    fullBill: 'fullBillDiscount',
    loyalty: 'loyaltyDiscount',
};

export const discountFlagName = {
    promotion: 'hasPromotionDiscount',
    fullBill: 'hasFullBillDiscount',
    loyalty: 'hasLoyaltyDiscount',
};

export const ChannelType = {
    ECOMMERCE: 1,
    POS: 2,
    BEEP: 3,
};

export const ShippingType = {
    DELIVERY: 'delivery',
    PICKUP: 'pickup',
    DIGITAL: 'digital',
    TAKEAWAY: 'takeaway',
    DINE_IN: 'dineIn',
};

export const SourceType = {
    UNKNOWN: 0,
    POS: 1,
    ECOMMERCE: 2,
    BEEP_PICKUP: 5,
    BEEP_DELIVERY: 6,
    BEEP_TAKEAWAY: 7,
    BEEP_DINE_IN: 8,
};

export const ClientType = {
    WEB: 'web',
    APP: 'app',
};

export const PromotionType = {
    MERCHANT: 'merchant',
    UNIVERSAL: 'universal',
};

export const DiscountType = {
    TRANSACTION_MANUAL_DISCOUNT: 'TransactionManualDiscount',
    ITEM_MANUAL_DISCOUNT: 'ItemManualDiscount',
    LOYALTY_DISCOUNT: 'Loyalty',
    BIR_DISCOUNT: 'BIR',
    PROMOTION: 'Promotion',
};

export const BIRDiscountType = {
    SC_PWD: 'SC/PWD',
    SC: 'SC',
    PWD: 'PWD',
    ATHLETE_AND_COACH: 'ATHLETE_AND_COACH',
    MEDAL_OF_VALOR: 'MEDAL_OF_VALOR',
    DIPLOMAT: 'DIPLOMAT',
    SOLO_PARENT: 'SOLO_PARENT',
};

export const PromotionDiscountType = {
    ABSOLUTE: 'absolute',
    PERCENTAGE: 'percentage',
    FIXED_UNIT_PRICE: 'fixedUnitPrice',
    COMBO: 'combo',
    BUY_X_FREE_Y: 'buyXFreeY',
    FREE_SHIPPING: 'freeShipping',
};

export const PromotionConditionsDefaultQuantity = {
    MIN: 0.01,
    MAX: 4294967295, // 2^32 -1
};

export const CSource = {
    DEFAULT: 'DEFAULT',
    POS: 'POS',
    BEEP: 'BEEP',
};

export const isValidNumber = value =>
    value !== null &&
    value !== undefined &&
    value !== '' &&
    !Number.isNaN(value) &&
    isFinite(value);

export function roundTo2DecimalPlaces(value) {
    if (isValidNumber(value)) {
        return new Decimal(value).toDecimalPlaces(2).toNumber() || 0;
    }
    return 0;
}

export function getLocaleNumberStringAtLeastFixed(value, precision = 2) {
    let result = 0;
    if (isValidNumber(value) && Number(value) !== 0) {
        const parts = Number(value)
            .toString()
            .split('.');
        const decimals = parts.length > 1 ? parts[1].length : 0;
        const precisionCount = value < 0.01 ? Math.min(Math.max(decimals, precision), 8) : 2;
        result = new Decimal(value).toDecimalPlaces(precisionCount).toNumber();
    }
    return result;
}

export const getValidNumber = number => (isValidNumber(number) ? number : 0);

export const getValidDivisor = divisor => (isValidNumber(divisor) && divisor !== 0 ? divisor : 1);

// 除
export function div(dividend, divisor) {
    const number = new Decimal(getValidNumber(dividend));
    const unFixedResult = number.div(getValidDivisor(divisor));
    return unFixedResult.toNumber();
}

export function div2Fixed(dividend, divisor) {
    const fixedResult = parseFloat(div(dividend, divisor).toFixed(2));
    return fixedResult;
}

// 乘
export function mul() {
    let result;
    for (let i = 0; i < arguments.length; i++) {
        const multiplier = arguments[i];
        if (i === 0) {
            result = new Decimal(getValidNumber(multiplier));
        } else {
            result = result.mul(new Decimal(getValidNumber(multiplier)));
        }
    }
    return Boolean(result) ? result.toNumber() : 0;
}

export function mul2Fixed() {
    const fixedResult = parseFloat(mul(...arguments).toFixed(2));
    return fixedResult;
}

// 加
export function add() {
    let result;
    for (let i = 0; i < arguments.length; i++) {
        const multiplier = arguments[i];
        if (i === 0) {
            result = new Decimal(getValidNumber(multiplier));
        } else {
            result = result.add(new Decimal(getValidNumber(multiplier)));
        }
    }
    return Boolean(result) ? result.toNumber() : 0;
}

export function add2Fixed() {
    const fixedResult = parseFloat(add(...arguments).toFixed(2));
    return fixedResult;
}

// 减
export function sub() {
    let result;
    for (let i = 0; i < arguments.length; i++) {
        const multiplier = arguments[i];
        if (i === 0) {
            result = new Decimal(getValidNumber(multiplier));
        } else {
            result = result.sub(new Decimal(getValidNumber(multiplier)));
        }
    }
    return Boolean(result) ? result.toNumber() : 0;
}

export function sub2Fixed() {
    const fixedResult = parseFloat(sub(...arguments).toFixed(2));
    return fixedResult;
}

export const getBeepOrderSource = ({ shippingType }) => {
    switch (shippingType) {
        case ShippingType.PICKUP:
            return SourceType.BEEP_PICKUP;
        case ShippingType.DELIVERY:
            return SourceType.BEEP_DELIVERY;
        case ShippingType.DINE_IN:
            return SourceType.BEEP_DINE_IN;
        case ShippingType.TAKEAWAY:
            return SourceType.BEEP_TAKEAWAY;
        default:
            return SourceType.UNKNOWN;
    }
};

export const getOrderSource = ({ channel, shippingType }) => {
    if (channel === ChannelType.POS) {
        return SourceType.POS;
    }
    if (channel === ChannelType.BEEP) {
        return getBeepOrderSource({ shippingType });
    }
    if (channel === ChannelType.ECOMMERCE) {
        return SourceType.ECOMMERCE;
    }
    return SourceType.UNKNOWN;
};

export const getTaxRateCardinal = obj => {
    const taxRate = obj.taxRate || 0;
    const taxRateCardinal = add(1, taxRate);
    return taxRateCardinal;
};

export const conditionsOrdering = ['business.country', 'business.qrOrderingSettings.marketingTags'];

export const sortConditions = conditions => {
    if (!Array.isArray(conditions) || !conditions.length) {
        return conditions;
    }
    const conditionMap = {};
    conditions.forEach(condition => {
        conditionMap[`${condition.entity}.${condition.propertyName}`] = condition;
    });
    const res = [];
    conditionsOrdering.forEach(key => {
        if (conditionMap[key]) {
            res.push(conditionMap[key]);
            delete conditionMap[key];
        }
    });
    return [...res, ...Object.values(conditionMap)];
};
