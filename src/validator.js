import { PromotionDiscountType } from './utils';

const validations = [
    validateTransaction,
    validateDiscountItem,
    validateServiceChargeItem,
    validateProductItem,
    validateTransactionLevelPromotion,
    validateLoyaltyDiscount,
    validateBirDiscount,
];

export default function validate(transaction, isTaxInclusiveDisplay) {
    for (const validation of validations) {
        const error = validation(transaction, isTaxInclusiveDisplay);
        if (error) {
            return error;
        }
    }
    return null;
}

function validateTransaction(transaction) {
    let error;

    if (transaction.items === undefined) {
        error = Error('Transaction must include "items" field');
    } else if (!Array.isArray(transaction.items)) {
        error = Error('Transaction field "items" should be Array');
    } else {
        error = null;

        let productItemsCount = 0;
        let otherItemsCount = 0;

        for (const purchaseItem of transaction.items) {
            if (
                purchaseItem.itemType &&
                !['Discount', 'ServiceCharge'].includes(purchaseItem.itemType)
            ) {
                error = Error(
                    `The items.itemType === "${
                        purchaseItem.itemType
                    }" cannot be accept, only can be undefined, "Discount" and "ServiceCharge"`,
                );
            }

            if (!purchaseItem.itemType) {
                productItemsCount += 1;
            } else {
                otherItemsCount += 1;
            }
        }

        if (productItemsCount === 0 && otherItemsCount > 0) {
            error = Error(
                'Transaction field "items" should not only include "Discount" or "ServiceCharge" without product items',
            );
        }
    }

    return error;
}

function validateTransactionLevelPromotion(transaction) {
    let error;
    if (transaction.promotions && transaction.promotions.length > 0) {
        for (const promotion of transaction.promotions) {
            if (
                isNaN(promotion.inputValue) &&
                promotion.type !== PromotionDiscountType.FREE_SHIPPING
            ) {
                error = Error(
                    'The transaction.promotions field "inputValue" is required and should be Number',
                );
            } else if (promotion.type === undefined) {
                error = Error('The transaction.promotions must include "type" field');
            } else if (
                promotion.type &&
                ![
                    PromotionDiscountType.ABSOLUTE,
                    PromotionDiscountType.COMBO,
                    PromotionDiscountType.FREE_SHIPPING,
                ].includes(promotion.type)
            ) {
                error = Error(
                    `The transaction.promotion.type === "${
                        promotion.type
                    }" cannot be accept, only can be "absolute", "combo" and "freeShipping"`,
                );
            } else if (promotion.type === PromotionDiscountType.COMBO) {
                if (
                    promotion.quantity === undefined ||
                    isNaN(promotion.quantity) ||
                    promotion.quantity < 0
                ) {
                    error = Error(
                        'The transaction.promotions field "quantity" is required and should be Number when combo',
                    );
                } else if (promotion.promotionId === undefined) {
                    error = Error('The transaction.promotions field "promotionId" is required');
                } else {
                    let hasPromotionAppliedQuantityMap = false;
                    for (const purchaseItem of transaction.items) {
                        if (
                            !purchaseItem.itemType &&
                            purchaseItem.promotionAppliedQuantityMap !== undefined &&
                            Object.keys(purchaseItem.promotionAppliedQuantityMap).length > 0
                        ) {
                            hasPromotionAppliedQuantityMap = true;
                        }
                    }

                    if (!hasPromotionAppliedQuantityMap) {
                        error = Error(
                            'At least one item in field "promotionAppliedQuantityMap" is required and should be Number when combo',
                        );
                    }
                }
            }
            if (error) {
                break;
            }
        }
    }
    return error;
}

function validateLoyaltyDiscount(transaction) {
    let error;
    if (transaction.loyaltyDiscounts && transaction.loyaltyDiscounts.length > 0) {
        for (const loyaltyDiscount of transaction.loyaltyDiscounts) {
            if (isNaN(loyaltyDiscount.inputValue)) {
                error = Error(
                    'The transaction.loyaltyDiscounts field "inputValue" is required and should be Number',
                );
            } else if (loyaltyDiscount.type === undefined) {
                error = Error('The transaction.loyaltyDiscounts must include "type" field');
            } else if (
                loyaltyDiscount.type &&
                !['storeCredit', 'cashback'].includes(loyaltyDiscount.type)
            ) {
                error = Error(
                    `The transaction.loyaltyDiscounts.type === "${
                        loyaltyDiscount.type
                    }" cannot be accept, only can be "storeCredit" and "cashback"`,
                );
            }
            if (error) {
                break;
            }
        }
    }
    return error;
}

function validateBirDiscount(transaction, isTaxInclusiveDisplay) {
    let error;
    if (transaction.birInfo) {
        const {
            headCount,
            pwdCount,
            seniorsCount,
            type,
            discountRate,
            discountType,
        } = transaction.birInfo;

        if (!isTaxInclusiveDisplay) {
            error = Error('The transaction.birInfo only works with tax inclusive display');
        } else if (type === undefined) {
            error = Error('The transaction.birInfo must include "type" field');
        } else if (type && !['retail', 'f&b'].includes(type)) {
            error = Error(
                `The transaction.birInfo.type === "${type}" cannot be accept, only can be "retail" and "f&b"`,
            );
        } else if (isNaN(discountRate)) {
            error = Error(
                'The transaction.birInfo field "discountRate" is required and should be Number',
            );
        } else if (type === 'f&b') {
            if (isNaN(pwdCount) || pwdCount < 0) {
                error = Error(
                    'The transaction.birInfo field "pwdCount" is required and should be a Number > 0',
                );
            } else if (isNaN(seniorsCount) || seniorsCount < 0) {
                error = Error(
                    'The transaction.birInfo field "seniorsCount" is required and should be a Number > 0',
                );
            } else if (isNaN(headCount) || headCount <= 0) {
                error = Error(
                    'The transaction.birInfo field "headCount" is required and should be a Number > 0',
                );
            } else if (seniorsCount + pwdCount > headCount) {
                error = Error(
                    'The transaction.birInfo field "seniorsCount" + "pwdCount" should be equal or less than headCount',
                );
            } else if (discountType === 'SOLO_PARENT') {
                error = Error('The solo parent discount only support Retail');
            }
        } else {
            if (isNaN(pwdCount) || pwdCount < 0 || pwdCount > 1) {
                error = Error(
                    'The transaction.birInfo field "pwdCount" is required and should be a Number > 0 and <= 1',
                );
            } else if (isNaN(seniorsCount) || seniorsCount < 0 || seniorsCount > 1) {
                error = Error(
                    'The transaction.birInfo field "seniorsCount" is required and should be a Number > 0 and <= 1',
                );
            } else if (isNaN(headCount) || headCount <= 0 || headCount > 1) {
                error = Error(
                    'The transaction.birInfo field "headCount" is required and should be a Number > 0  and <= 1',
                );
            } else if (seniorsCount == 1 && pwdCount == 1) {
                error = Error(
                    'The transaction.birInfo field "seniorsCount" and "pwdCount" should not exist simultaneously',
                );
            }
        }

        if (!error) {
            for (const purchaseItem of transaction.items) {
                if (
                    (!purchaseItem.itemType &&
                        Object.keys(purchaseItem).findIndex(key => key === 'taxRate') === -1) ||
                    (!purchaseItem.itemType &&
                        !purchaseItem.isAmusementTax &&
                        purchaseItem.taxRate !== 0 &&
                        purchaseItem.taxRate !== 0.12)
                ) {
                    error = Error(
                        'The transaction.purchaseItem field "taxRate" is required and only can be 0 or 0.12 when calculate BIR',
                    );
                }
            }
        }
    }
    return error;
}

function validateDiscountItem(transaction) {
    let error;
    let discountItemCount = 0;

    for (const purchaseItem of transaction.items) {
        if (purchaseItem.itemType === 'Discount') {
            discountItemCount += 1;
            if (isNaN(purchaseItem.inputValue)) {
                error = Error('Discount item field "inputValue" is required and should be Number');
            } else if (purchaseItem.type === undefined) {
                error = Error('Discount item field "type" is required');
            } else if (purchaseItem.type && !['amount', 'percent'].includes(purchaseItem.type)) {
                error = Error(
                    `The discountItem.type === "${
                        purchaseItem.type
                    }" cannot be accept, only can be "amount" and "percent"`,
                );
            } else {
                error = null;
            }
        }
    }

    if (discountItemCount > 1) {
        error = Error('Only able to have ONE discount item');
    }

    return error;
}

function validateServiceChargeItem(transaction) {
    let error;
    let serviceChargeItemCount = 0;
    const { birInfo } = transaction;

    for (const purchaseItem of transaction.items) {
        if (purchaseItem.itemType === 'ServiceCharge') {
            serviceChargeItemCount += 1;
            if (isNaN(purchaseItem.rate)) {
                error = Error('Service Charge item field "rate" is required and should be Number');
            } else if (purchaseItem.taxRate !== undefined && isNaN(purchaseItem.taxRate)) {
                error = Error('Service Charge item field "taxRate" should be Number');
            } else if (Boolean(birInfo) && Boolean(purchaseItem.taxRate)) {
                error = Error('Service Charge item field "taxRate" should be 0 in PH');
            } else {
                error = null;
            }
        }
    }

    if (serviceChargeItemCount > 1) {
        error = Error('Only able to have ONE Service Charge item');
    }

    return error;
}

function validateProductItem(transaction) {
    let error;

    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            if (isNaN(purchaseItem.unitPrice)) {
                error = Error('Product item field "unitPrice" is required and should be Number');
            } else if (isNaN(purchaseItem.quantity) || purchaseItem.quantity <= 0) {
                error = Error('Product item field "quantity" is required and should be Number > 0');
            } else if (purchaseItem.taxRate !== undefined && isNaN(purchaseItem.taxRate)) {
                error = Error('Product item field "taxRate" should be Number');
            } else if (purchaseItem.isAmusementTax && !purchaseItem.isVatExempted) {
                error = Error('"isAmusementTax" must be based on "isVatExempted"');
            } else {
                error = null;
            }

            if (purchaseItem.selectedOptions && purchaseItem.selectedOptions.length > 0) {
                for (const selectedOption of purchaseItem.selectedOptions) {
                    if (isNaN(selectedOption.optionValue)) {
                        error = Error(
                            'The item.selectedOptions field "optionValue" is required and should be Number',
                        );
                    } else if (isNaN(selectedOption.quantity) || selectedOption.quantity < 1) {
                        error = Error(
                            'The item.selectedOptions field "quantity" is required and should be Number >= 1',
                        );
                    }
                }
            }
            if (error) {
                break;
            }

            if (purchaseItem.itemLevelDiscount) {
                const itemLevelDiscount = purchaseItem.itemLevelDiscount;
                if (isNaN(itemLevelDiscount.inputValue)) {
                    error = Error(
                        'The item.itemLevelDiscount field "inputValue" is required and should be Number',
                    );
                } else if (itemLevelDiscount.type === undefined) {
                    error = Error('The item.itemLevelDiscount field "type" is required');
                } else if (
                    itemLevelDiscount.type &&
                    !['amount', 'percent'].includes(itemLevelDiscount.type)
                ) {
                    error = Error(
                        `The item.itemLevelDiscount.type === "${
                            itemLevelDiscount.type
                        }" cannot be accept, only can be "amount" and "percent"`,
                    );
                }
            }
            if (error) {
                break;
            }

            if (purchaseItem.promotions && purchaseItem.promotions.length > 0) {
                for (const promotion of purchaseItem.promotions) {
                    if (isNaN(promotion.inputValue)) {
                        error = Error(
                            'The item.promotions field "inputValue" is required and should be Number',
                        );
                    } else if (promotion.type === undefined) {
                        error = Error('The item.promotions must include "type" field');
                    } else if (
                        promotion.type &&
                        !['percentage', 'fixedUnitPrice', 'buyXFreeY'].includes(promotion.type)
                    ) {
                        error = Error(
                            `The item.promotion.type === "${
                                promotion.type
                            }" cannot be accept, only can be "percentage", "fixedUnitPrice" and "buyXFreeY"`,
                        );
                    }
                }
            }
            if (error) {
                break;
            }

            if (purchaseItem.return) {
                if (isNaN(purchaseItem.return.quantity)) {
                    error = Error(
                        'The item.return field "quantity" is required and should be Number',
                    );
                } else if (purchaseItem.return.quantity === 0) {
                    error = Error('The item.return field "quantity" cannot be 0');
                } else if (purchaseItem.return.total && isNaN(purchaseItem.return.total)) {
                    error = Error('The item.return field "total" should be Number');
                }
            }
            if (error) {
                break;
            }
        }
    }

    return error;
}
