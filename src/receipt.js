import { find, get, map } from 'lodash';
import {
    add,
    add2Fixed,
    div2Fixed,
    isValidNumber,
    mul,
    mul2Fixed,
    roundTo2DecimalPlaces,
} from './utils';

const receiptSupportCountryCodes = ['MY', 'TH', 'PH'];

export default function receipt(
    transaction,
    isTaxInclusiveDisplay,
    countryCode,
    hasDiscountColumn,
    unitPriceUnrounding = false,
) {
    let transactionToReturn = transaction;
    if (receiptSupportCountryCodes.includes(countryCode)) {
        if (countryCode === 'MY') {
            transactionToReturn = calculateMYReceipt(
                transaction,
                isTaxInclusiveDisplay,
                hasDiscountColumn,
                unitPriceUnrounding,
            );
        } else if (countryCode === 'TH') {
            transactionToReturn = calculateTHReceipt(
                transaction,
                isTaxInclusiveDisplay,
                hasDiscountColumn,
                unitPriceUnrounding,
            );
        } else {
            transactionToReturn = calculatePHReceipt(
                transaction,
                isTaxInclusiveDisplay,
                hasDiscountColumn,
                unitPriceUnrounding,
            );
        }
    } else {
        throw Error(
            `countryCode not support, only support ${receiptSupportCountryCodes.join(', ')}.`,
        );
    }
    return transactionToReturn;
}

function calculateTaxInclusiveUnitPrice(
    taxExclusiveUnitPrice,
    taxRateCardinal,
    unitPriceUnrounding = false,
) {
    if (
        unitPriceUnrounding ||
        (isValidNumber(taxExclusiveUnitPrice) && taxExclusiveUnitPrice < 0.01)
    ) {
        return mul(taxExclusiveUnitPrice, taxRateCardinal);
    }
    return mul2Fixed(taxExclusiveUnitPrice, taxRateCardinal);
}

function calculateMYReceipt(
    transaction,
    isTaxInclusiveDisplay,
    hasDiscountColumn,
    unitPriceUnrounding,
) {
    if (!transaction.calculation) {
        return calculateMYReceiptLegacy(transaction, isTaxInclusiveDisplay, hasDiscountColumn);
    }
    // new flow with add-on calculation filed
    // 3.8.13 : these value in  purchaseItem.receipt will not contain takeawayCharges anymore
    for (const purchaseItem of transaction.items) {
        if (isTaxInclusiveDisplay) {
            if (!purchaseItem.itemType) {
                const taxRate = purchaseItem.taxRate;
                const taxRateCardinal = add(1, taxRate);
                const quantity = purchaseItem.quantity;

                const taxExclusiveUnitPrice = get(purchaseItem, ['calculation', 'fullPrice'], 0);
                const taxInclusiveUnitPrice = calculateTaxInclusiveUnitPrice(
                    taxExclusiveUnitPrice,
                    taxRateCardinal,
                    unitPriceUnrounding,
                );

                let total = mul2Fixed(taxInclusiveUnitPrice, quantity); // original total without takeawayCharges

                // calculate discount
                const originalTaxInclusiveTotal = get(
                    purchaseItem,
                    ['calculation', 'original', 'total'],
                    0,
                );
                const taxInclusiveTotal = purchaseItem.total + purchaseItem.tax;
                const taxInclusiveDiscount = originalTaxInclusiveTotal - taxInclusiveTotal;

                // generate taxInclusive Promo
                const calculationDiscounts = get(purchaseItem, ['calculation', 'discounts'], []);
                map(get(purchaseItem, 'promotions'), promotion => {
                    const { promotionId } = promotion;
                    const promoDiscountItem = find(
                        calculationDiscounts,
                        discountItem =>
                            discountItem.promotionId && discountItem.promotionId === promotionId,
                    );
                    /* sonarqube:off */
                    if (promoDiscountItem) {
                        const promoTaxExclusiveDiscount = get(promoDiscountItem, 'discount', 0);
                        const promoDeductedTax = get(promoDiscountItem, 'deductedTax', 0);
                        promotion.discount = add2Fixed(promoTaxExclusiveDiscount, promoDeductedTax);
                    }
                    /* sonarqube:on */
                });

                // calculate discountWithoutPromo, which should be taxInclusive
                let discountWithoutPromo = taxInclusiveDiscount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });

                const a4Total = total - taxInclusiveDiscount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }

                purchaseItem.receipt = {
                    price: taxInclusiveUnitPrice,
                    qty: roundTo2DecimalPlaces(quantity),
                    discount: roundTo2DecimalPlaces(taxInclusiveDiscount),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        } else {
            if (!purchaseItem.itemType) {
                const taxExclusiveUnitPrice = get(purchaseItem, ['calculation', 'fullPrice'], 0);
                const quantity = purchaseItem.quantity;
                const discount = purchaseItem.discount;

                // calculate total
                const taxRate = purchaseItem.taxRate;
                const taxRateCardinal = add(1, taxRate);

                const taxInclusiveUnitPrice = calculateTaxInclusiveUnitPrice(
                    taxExclusiveUnitPrice,
                    taxRateCardinal,
                    unitPriceUnrounding,
                );
                const taxInclusiveSubtotal = mul2Fixed(taxInclusiveUnitPrice, quantity);
                const taxExclusiveSubtotal = div2Fixed(taxInclusiveSubtotal, taxRateCardinal); // without takeawayChages

                // calculate discountWithoutPromo
                let discountWithoutPromo = discount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });

                let total = taxExclusiveSubtotal;

                const a4Total = total - discount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }

                purchaseItem.receipt = {
                    price: taxExclusiveUnitPrice,
                    qty: roundTo2DecimalPlaces(quantity),
                    discount: roundTo2DecimalPlaces(discount),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        }
    }

    const subtotal = transaction.subtotal;
    const receiptDiscount = transaction.discount;
    const receiptServiceCharge = transaction.serviceCharge;
    const receiptTax = transaction.tax;
    const receiptTotal = transaction.total;
    const takeawayCharges = transaction.takeawayCharges || 0;
    const receiptTotalExcTax = subtotal - takeawayCharges;

    transaction.receipt = {
        receiptTotalExcTax: roundTo2DecimalPlaces(receiptTotalExcTax),
        discount: roundTo2DecimalPlaces(receiptDiscount),
        serviceCharge: roundTo2DecimalPlaces(receiptServiceCharge),
        tax: roundTo2DecimalPlaces(receiptTax),
        total: roundTo2DecimalPlaces(receiptTotal),
        takeawayCharges: roundTo2DecimalPlaces(takeawayCharges),
    };

    return transaction;
}

function calculatePHReceipt(
    transaction,
    isTaxInclusiveDisplay,
    hasDiscountColumn,
    unitPriceUnrounding,
) {
    if (!transaction.calculation) {
        return calculatePHReceiptLegacy(transaction, isTaxInclusiveDisplay, hasDiscountColumn);
    }
    // new flow with add-on calculation filed
    for (const purchaseItem of transaction.items) {
        if (isTaxInclusiveDisplay) {
            if (!purchaseItem.itemType) {
                const taxRate = purchaseItem.taxRate;
                const taxRateCardinal = add(1, taxRate);
                const quantity = purchaseItem.quantity;

                const taxExclusiveUnitPrice = get(purchaseItem, ['calculation', 'fullPrice'], 0);
                const taxInclusiveUnitPrice = calculateTaxInclusiveUnitPrice(
                    taxExclusiveUnitPrice,
                    taxRateCardinal,
                    unitPriceUnrounding,
                );

                let total = mul2Fixed(taxInclusiveUnitPrice, quantity); // original total without takeawayCharges

                // calculate discount
                const originalTaxInclusiveTotal = get(
                    purchaseItem,
                    ['calculation', 'original', 'total'],
                    0,
                );
                const taxInclusiveTotal = purchaseItem.total + purchaseItem.tax;
                const taxInclusiveDiscount = originalTaxInclusiveTotal - taxInclusiveTotal;

                // generate taxInclusive Promo
                const calculationDiscounts = get(purchaseItem, ['calculation', 'discounts'], []);
                map(get(purchaseItem, 'promotions'), promotion => {
                    const { promotionId } = promotion;
                    const promoDiscountItem = find(
                        calculationDiscounts,
                        discountItem =>
                            discountItem.promotionId && discountItem.promotionId === promotionId,
                    );

                    // NOSONAR
                    if (promoDiscountItem) {
                        const promoTaxExclusiveDiscount = get(promoDiscountItem, 'discount', 0);
                        const promoDeductedTax = get(promoDiscountItem, 'deductedTax', 0);
                        promotion.discount = add2Fixed(promoTaxExclusiveDiscount, promoDeductedTax);
                    }
                });

                // calculate discountWithoutPromo, which should be taxInclusive
                let discountWithoutPromo = taxInclusiveDiscount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });

                const a4Total = total - taxInclusiveDiscount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }

                purchaseItem.receipt = {
                    price: taxInclusiveUnitPrice,
                    qty: roundTo2DecimalPlaces(quantity),
                    discount: roundTo2DecimalPlaces(taxInclusiveDiscount),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        } else {
            if (!purchaseItem.itemType) {
                const taxExclusiveUnitPrice = get(purchaseItem, ['calculation', 'fullPrice'], 0);
                const quantity = purchaseItem.quantity;
                const discount = purchaseItem.discount;

                // calculate total
                const taxRate = purchaseItem.taxRate;
                const taxRateCardinal = add(1, taxRate);

                const taxInclusiveUnitPrice = calculateTaxInclusiveUnitPrice(
                    taxExclusiveUnitPrice,
                    taxRateCardinal,
                    unitPriceUnrounding,
                );
                const taxInclusiveSubtotal = mul2Fixed(taxInclusiveUnitPrice, quantity);
                const taxExclusiveSubtotal = div2Fixed(taxInclusiveSubtotal, taxRateCardinal); // without takeawayChages

                // calculate discountWithoutPromo
                let discountWithoutPromo = discount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });

                let total = taxExclusiveSubtotal;

                const a4Total = total - discount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }

                purchaseItem.receipt = {
                    price: taxExclusiveUnitPrice,
                    qty: roundTo2DecimalPlaces(quantity),
                    discount: roundTo2DecimalPlaces(discount),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        }
    }

    const receiptTotalDeductedTax = transaction.totalDeductedTax || 0;
    const receiptServiceChargeTax = transaction.serviceChargeTax || 0;
    const receiptLessVAT = receiptTotalDeductedTax + transaction.tax;
    const receiptSeniorDiscount = transaction.seniorDiscount || 0;
    const receiptPwdDiscount = transaction.pwdDiscount || 0;
    const amusementTax = transaction.amusementTax || 0;
    let receiptAthleteAndCoachDiscount = 0;
    let receiptMedalOfValorDiscount = 0;
    let receiptSoloParentDiscount = 0;
    if (transaction.addonBirCompliance) {
        receiptAthleteAndCoachDiscount =
            transaction.addonBirCompliance.athleteAndCoachDiscount || 0;
        receiptMedalOfValorDiscount = transaction.addonBirCompliance.medalOfValorDiscount || 0;
        receiptSoloParentDiscount = transaction.addonBirCompliance.soloParentDiscount || 0;
    }

    const receiptAdhocDiscount =
        transaction.discount -
        receiptSeniorDiscount -
        receiptPwdDiscount -
        receiptAthleteAndCoachDiscount -
        receiptMedalOfValorDiscount -
        receiptSoloParentDiscount;
    const takeawayCharges = transaction.takeawayCharges || 0;
    const receiptDiscountable = transaction.subtotal - takeawayCharges;
    const subtotal = transaction.subtotal;
    const receiptServiceCharge = (transaction.serviceCharge || 0) + receiptServiceChargeTax;
    const receiptTotal = transaction.total;
    const receiptRoundedAmount = transaction.roundedAmount || 0;

    const receiptTax = transaction.tax - receiptServiceChargeTax - amusementTax;
    const receiptTaxableSales = transaction.taxableSales || 0;
    const receiptTaxExemptedSales = transaction.taxExemptedSales || 0;
    const receiptZeroRatedSales = transaction.zeroRatedSales || 0;

    const receiptSubotal =
        receiptTotal -
        receiptRoundedAmount -
        receiptServiceCharge +
        receiptTotalDeductedTax +
        transaction.discount -
        takeawayCharges;

    const receiptTotalExcTax = subtotal - takeawayCharges;

    transaction.receipt = {
        receiptTotalExcTax: roundTo2DecimalPlaces(receiptTotalExcTax),
        subtotal: roundTo2DecimalPlaces(receiptSubotal),
        lessVAT: roundTo2DecimalPlaces(receiptLessVAT),
        discountable: roundTo2DecimalPlaces(receiptDiscountable),
        adhocDiscount: roundTo2DecimalPlaces(receiptAdhocDiscount),
        seniorDiscount: roundTo2DecimalPlaces(receiptSeniorDiscount),
        pwdDiscount: roundTo2DecimalPlaces(receiptPwdDiscount),
        soloParentDiscount: roundTo2DecimalPlaces(receiptSoloParentDiscount),
        athleteAndCoachDiscount: roundTo2DecimalPlaces(receiptAthleteAndCoachDiscount),
        medalOfValorDiscount: roundTo2DecimalPlaces(receiptMedalOfValorDiscount),
        serviceCharge: roundTo2DecimalPlaces(receiptServiceCharge),
        total: roundTo2DecimalPlaces(receiptTotal),
        taxableSales: roundTo2DecimalPlaces(receiptTaxableSales),
        vatAmount: roundTo2DecimalPlaces(receiptTax),
        taxExemptedSales: roundTo2DecimalPlaces(receiptTaxExemptedSales),
        zeroRatedSales: roundTo2DecimalPlaces(receiptZeroRatedSales),
        takeawayCharges: roundTo2DecimalPlaces(takeawayCharges),
        amusementTax: roundTo2DecimalPlaces(amusementTax),
    };

    return transaction;
}

function calculateMYReceiptLegacy(transaction, isTaxInclusiveDisplay, hasDiscountColumn) {
    for (const purchaseItem of transaction.items) {
        if (isTaxInclusiveDisplay) {
            if (!purchaseItem.itemType) {
                let total = 0;
                if (purchaseItem.taxInclusiveSubtotal > 0) {
                    total = purchaseItem.taxInclusiveSubtotal;
                } else {
                    const originalTax =
                        ((purchaseItem.notRoundedOriginalTax || purchaseItem.tax) /
                            (purchaseItem.subTotal - purchaseItem.discount)) *
                        purchaseItem.subTotal;
                    total = purchaseItem.subTotal + originalTax;
                }
                const price = total / purchaseItem.quantity;

                // calculate discountWithoutPromo
                const discount = purchaseItem.discount;
                let discountWithoutPromo = discount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });

                const a4Total = total - discount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }

                purchaseItem.receipt = {
                    price: roundTo2DecimalPlaces(price),
                    qty: roundTo2DecimalPlaces(purchaseItem.quantity),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discount: roundTo2DecimalPlaces(discount),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        } else {
            if (!purchaseItem.itemType) {
                // calculate discountWithoutPromo
                const discount = purchaseItem.discount;
                let discountWithoutPromo = discount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });
                let total = purchaseItem.subTotal;
                const a4Total = total - discount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }
                purchaseItem.receipt = {
                    price: roundTo2DecimalPlaces(purchaseItem.subTotal / purchaseItem.quantity),
                    qty: roundTo2DecimalPlaces(purchaseItem.quantity),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discount: roundTo2DecimalPlaces(discount),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        }
    }

    const subtotal = transaction.subtotal;
    const receiptDiscount = transaction.discount;
    const receiptServiceCharge = transaction.serviceCharge;
    const receiptTax = transaction.tax;
    const receiptTotal = transaction.total;
    const takeawayCharges = transaction.takeawayCharges || 0;
    const receiptTotalExcTax = subtotal - takeawayCharges;

    transaction.receipt = {
        receiptTotalExcTax: roundTo2DecimalPlaces(receiptTotalExcTax),
        discount: roundTo2DecimalPlaces(receiptDiscount),
        serviceCharge: roundTo2DecimalPlaces(receiptServiceCharge),
        tax: roundTo2DecimalPlaces(receiptTax),
        total: roundTo2DecimalPlaces(receiptTotal),
        takeawayCharges: roundTo2DecimalPlaces(takeawayCharges),
    };

    return transaction;
}

function calculateTHReceipt(
    transaction,
    isTaxInclusiveDisplay,
    hasDiscountColumn,
    unitPriceUnrounding,
) {
    return calculateMYReceipt(
        transaction,
        isTaxInclusiveDisplay,
        hasDiscountColumn,
        unitPriceUnrounding,
    );
}

function calculatePHReceiptLegacy(transaction, isTaxInclusiveDisplay, hasDiscountColumn) {
    for (const purchaseItem of transaction.items) {
        if (isTaxInclusiveDisplay) {
            if (!purchaseItem.itemType) {
                const taxRate = purchaseItem.taxRate;
                const originalTax = purchaseItem.subTotal * taxRate;
                let total = purchaseItem.subTotal + originalTax;
                const price = total / purchaseItem.quantity;

                // calculate discountWithoutPromo
                const discount = purchaseItem.discount;
                let discountWithoutPromo = discount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });

                const a4Total = total - discount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }
                purchaseItem.receipt = {
                    price: roundTo2DecimalPlaces(price),
                    qty: roundTo2DecimalPlaces(purchaseItem.quantity),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discount: roundTo2DecimalPlaces(discount),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        } else {
            if (!purchaseItem.itemType) {
                // calculate discountWithoutPromo
                const discount = purchaseItem.discount;
                let discountWithoutPromo = discount;
                map(get(purchaseItem, 'promotions'), promotion => {
                    const promoDiscount = get(promotion, 'discount', 0);
                    discountWithoutPromo -= promoDiscount;
                });

                let total = purchaseItem.subTotal;
                const a4Total = total - discount;
                if (hasDiscountColumn) {
                    total = a4Total;
                }

                purchaseItem.receipt = {
                    price: roundTo2DecimalPlaces(purchaseItem.subTotal / purchaseItem.quantity),
                    qty: roundTo2DecimalPlaces(purchaseItem.quantity),
                    total: roundTo2DecimalPlaces(total),
                    a4Total: roundTo2DecimalPlaces(a4Total),
                    discount: roundTo2DecimalPlaces(discount),
                    discountWithoutPromo: roundTo2DecimalPlaces(discountWithoutPromo),
                };
            }
        }
    }

    const receiptTotalDeductedTax = transaction.totalDeductedTax || 0;
    const receiptServiceChargeTax = transaction.serviceChargeTax || 0;
    const receiptLessVAT = receiptTotalDeductedTax + transaction.tax;
    const receiptSeniorDiscount = transaction.seniorDiscount || 0;
    const receiptPwdDiscount = transaction.pwdDiscount || 0;
    let receiptAthleteAndCoachDiscount = 0;
    let receiptMedalOfValorDiscount = 0;
    let receiptSoloParentDiscount = 0;
    if (transaction.addonBirCompliance) {
        receiptAthleteAndCoachDiscount =
            transaction.addonBirCompliance.athleteAndCoachDiscount || 0;
        receiptMedalOfValorDiscount = transaction.addonBirCompliance.medalOfValorDiscount || 0;
        receiptSoloParentDiscount = transaction.addonBirCompliance.soloParentDiscount || 0;
    }

    const receiptAdhocDiscount =
        transaction.discount -
        receiptSeniorDiscount -
        receiptPwdDiscount -
        receiptAthleteAndCoachDiscount -
        receiptMedalOfValorDiscount -
        receiptSoloParentDiscount;
    const takeawayCharges = transaction.takeawayCharges || 0;
    const receiptDiscountable = transaction.subtotal - takeawayCharges;
    const subtotal = transaction.subtotal;
    const receiptServiceCharge = (transaction.serviceCharge || 0) + receiptServiceChargeTax;
    const receiptTotal = transaction.total;
    const receiptRoundedAmount = transaction.roundedAmount || 0;

    const receiptTax = transaction.tax - receiptServiceChargeTax;
    const receiptTaxableSales = transaction.taxableSales || 0;
    const receiptTaxExemptedSales = transaction.taxExemptedSales || 0;
    const receiptZeroRatedSales = transaction.zeroRatedSales || 0;

    const receiptSubotal =
        receiptTotal -
        receiptRoundedAmount -
        receiptServiceCharge +
        receiptTotalDeductedTax +
        transaction.discount -
        takeawayCharges;

    const receiptTotalExcTax = subtotal - takeawayCharges;

    transaction.receipt = {
        receiptTotalExcTax: roundTo2DecimalPlaces(receiptTotalExcTax),
        subtotal: roundTo2DecimalPlaces(receiptSubotal),
        lessVAT: roundTo2DecimalPlaces(receiptLessVAT),
        discountable: roundTo2DecimalPlaces(receiptDiscountable),
        adhocDiscount: roundTo2DecimalPlaces(receiptAdhocDiscount),
        seniorDiscount: roundTo2DecimalPlaces(receiptSeniorDiscount),
        pwdDiscount: roundTo2DecimalPlaces(receiptPwdDiscount),
        soloParentDiscount: roundTo2DecimalPlaces(receiptSoloParentDiscount),
        athleteAndCoachDiscount: roundTo2DecimalPlaces(receiptAthleteAndCoachDiscount),
        medalOfValorDiscount: roundTo2DecimalPlaces(receiptMedalOfValorDiscount),
        vatAmount: roundTo2DecimalPlaces(receiptTax),
        serviceCharge: roundTo2DecimalPlaces(receiptServiceCharge),
        total: roundTo2DecimalPlaces(receiptTotal),
        taxableSales: roundTo2DecimalPlaces(receiptTaxableSales),
        taxExemptedSales: roundTo2DecimalPlaces(receiptTaxExemptedSales),
        zeroRatedSales: roundTo2DecimalPlaces(receiptZeroRatedSales),
        takeawayCharges: roundTo2DecimalPlaces(takeawayCharges),
    };

    return transaction;
}
