import { find } from 'lodash';
import calculateItem, { calculateItemWithTakeawayCharge } from './item';
import calculateTransaction from './transaction';
import calculateTransactionDiscount, {
    calculateMaximumDiscountInputValue,
} from './transactionDiscount/transactionDiscount';
import calculateTransactionPromotion from './transactionDiscount/transactionPromotions';
import calculateLoyaltyDiscount from './transactionDiscount/transactionLoyaltyDiscounts';
import calculateServiceCharge, { filterItemsWithoutServiceCharge } from './serviceChargeItem';
import { CSource, discountFieldName } from '../utils';
import calculateBir, { checkBirDiscountAvailable } from './birDiscount';
import {
    calculateItemOriginalFields,
    calculateServiceChargeItemOriginalFields,
} from './item/itemCalculationFields';
import { insertCalculationFields, calculateTransactionOriginalFields } from './calculationFields';

export default function calculate(transaction, isTaxInclusiveDisplay, source = CSource.DEFAULT) {
    // delete fiels will be included in calculation to make sure idempotence
    delete transaction.display;
    delete transaction.maximumDiscountInputValue;
    // The PH law states that a BIR Discount cannot be used in conjunction with any other discount
    checkBirDiscountAvailable(transaction);
    if (transaction.calculationMode === 'Refund') {
        calculateRefund(transaction, isTaxInclusiveDisplay);
    } else {
        calculateSales(transaction, isTaxInclusiveDisplay, source);
    }
    return transaction;
}

function calculateRefund(transaction, isTaxInclusiveDisplay) {
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            calculateItem(purchaseItem, isTaxInclusiveDisplay, true);
        }
    }
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, true, true, true);
}

function calculateSales(transaction, isTaxInclusiveDisplay, source = 'DEFAULT') {
    calculateSalesItemBasics(transaction, isTaxInclusiveDisplay);

    calculateSalesTransactionLevelPromotion(transaction, isTaxInclusiveDisplay);

    calculateSalesFullBillDiscount(transaction, isTaxInclusiveDisplay);

    calculateSalesLoyaltyDiscount(transaction, isTaxInclusiveDisplay);

    calculateSalesServiceCharge(transaction, isTaxInclusiveDisplay);

    calculateSalesBirDiscount(transaction, isTaxInclusiveDisplay);

    calculateSalesTakeawayCharges(transaction, isTaxInclusiveDisplay);

    calculateSalesShippingFee(transaction, isTaxInclusiveDisplay);

    calculateSalesFixedFee(transaction, isTaxInclusiveDisplay, source);

    calculateSalesClearTempFields(transaction);
}

function calculateSalesItemBasics(transaction, isTaxInclusiveDisplay) {
    delete transaction.calculation;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
            // delete fiels will be included in calculation to make sure idempotence
            delete purchaseItem.display;
            delete purchaseItem.adhocDiscount;
            delete purchaseItem.seniorDiscount;
            delete purchaseItem.pwdDiscount;
            delete purchaseItem.soloParentDiscount;
            delete purchaseItem.athleteAndCoachDiscount;
            delete purchaseItem.medalOfValorDiscount;
            delete purchaseItem[discountFieldName.fullBill];
            delete purchaseItem[discountFieldName.promotion];
            delete purchaseItem[discountFieldName.loyalty];
            delete purchaseItem.calculation;
            calculateItemOriginalFields(purchaseItem, transaction.takeawayCharge);
            calculateItem(purchaseItem, isTaxInclusiveDisplay, false);
        }
    }
    calculateServiceChargeItemOriginalFields(transaction);
    calculateTransactionOriginalFields(transaction);
    calculateTransaction(transaction, isTaxInclusiveDisplay, false, false, false, false);
}

function calculateSalesTransactionLevelPromotion(transaction, isTaxInclusiveDisplay) {
    calculateTransactionPromotion(transaction, isTaxInclusiveDisplay);
    calculateTransaction(transaction, isTaxInclusiveDisplay, false, false, false, false);
}

function calculateSalesFullBillDiscount(transaction, isTaxInclusiveDisplay) {
    calculateMaximumDiscountInputValue(transaction, isTaxInclusiveDisplay);
    for (const purchaseItem of transaction.items) {
        if (purchaseItem.itemType === 'Discount') {
            // delete fiels will be included in calculation to make sure idempotence
            delete purchaseItem.display;
            delete purchaseItem.adhocDiscount;

            calculateTransactionDiscount(purchaseItem, transaction, isTaxInclusiveDisplay);
        }
    }
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, false, false, false);
}

function calculateSalesLoyaltyDiscount(transaction, isTaxInclusiveDisplay) {
    calculateLoyaltyDiscount(transaction, isTaxInclusiveDisplay);
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, true, true, false);
}

function calculateSalesServiceCharge(transaction, isTaxInclusiveDisplay) {
    const serviceChargeItem = find(
        transaction.items,
        purchaseItem => purchaseItem.itemType === 'ServiceCharge',
    );
    if (serviceChargeItem) {
        filterItemsWithoutServiceCharge(serviceChargeItem, transaction);
        delete serviceChargeItem.display;
        delete serviceChargeItem.adhocDiscount;
        calculateServiceCharge(serviceChargeItem, transaction, isTaxInclusiveDisplay);
    }
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, true, false, false);
}

function calculateSalesBirDiscount(transaction, isTaxInclusiveDisplay) {
    calculateBir(transaction, isTaxInclusiveDisplay);
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, true, true, false);
}

function calculateSalesTakeawayCharges(transaction, isTaxInclusiveDisplay) {
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            calculateItemWithTakeawayCharge(purchaseItem, transaction.takeawayCharge || 0);
        }
    }
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, true, false);
}

function calculateSalesShippingFee(transaction, isTaxInclusiveDisplay) {
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, true, true, true);
}

function calculateSalesFixedFee(transaction, isTaxInclusiveDisplay, source = 'DEFAULT') {
    calculateTransaction(transaction, isTaxInclusiveDisplay, true, true, true, true, true, source);
}

function calculateSalesClearTempFields(transaction) {
    insertCalculationFields(transaction);
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
            delete purchaseItem.taxExclusiveSubtotal;
            delete purchaseItem.taxInclusiveSubtotal;
            delete purchaseItem.taxInclusiveUnitPrice;

            delete purchaseItem.isBirDiscountCalculated;
            delete purchaseItem.valuesAfterBIR;
            delete purchaseItem.birInfo;
            delete purchaseItem.calculation.original.subTotalWithoutTakeawayCharge;
        }
    }
}
