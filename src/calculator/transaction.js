import { CSource, roundTo2DecimalPlaces } from '../utils';

export default function calculateTransaction(
    transaction,
    isTaxInclusiveDisplay,
    includeDiscountItem,
    includeServiceChargeItem,
    includeBir,
    includeShippingFee,
    includeFixedFee,
    source = 'DEFAULT',
) {
    let subtotal = 0;
    let discount = 0;
    let tax = 0;
    let total = 0;
    let serviceCharge = 0;
    let serviceChargeTax = 0;

    let seniorDiscount = 0;
    let pwdDiscount = 0;
    let athleteAndCoachDiscount = 0;
    let medalOfValorDiscount = 0;
    let soloParentDiscount = 0;
    let taxableSales = 0;
    let taxExemptedSales = 0;
    let zeroRatedSales = 0;
    let totalDeductedTax = 0;

    let displaySubtotal = 0;
    let displayDiscount = 0;
    let displayLoyaltyDiscount = 0;
    let displayTax = 0;
    let displayServiceCharge = 0;
    let displayServiceChargeDiscount = 0;
    let displayServiceChargeBIRDiscount = 0;
    let displayShippingFee = 0;
    let displayFixedFee = 0;
    let displayTotal = 0;
    let takeawayCharges = 0;
    let loyaltyDiscount = 0;
    let amusementTax = 0;

    for (const purchaseItem of transaction.items) {
        if (
            !purchaseItem.itemType ||
            (purchaseItem.itemType === 'Discount' && includeDiscountItem) ||
            (purchaseItem.itemType === 'ServiceCharge' && includeServiceChargeItem)
        ) {
            if (purchaseItem.itemType !== 'ServiceCharge') {
                subtotal += purchaseItem.subTotal || 0;
            }

            if (purchaseItem.itemType === 'ServiceCharge') {
                discount +=
                    (purchaseItem.discount || 0) -
                    (purchaseItem.seniorDiscount || 0) -
                    (purchaseItem.pwdDiscount || 0) -
                    (purchaseItem.athleteAndCoachDiscount || 0) -
                    (purchaseItem.medalOfValorDiscount || 0) -
                    (purchaseItem.soloParentDiscount || 0);
            } else {
                discount += purchaseItem.discount || 0;
            }

            tax += purchaseItem.tax || 0;
            total += (purchaseItem.total || 0) + (purchaseItem.tax || 0);
            if (purchaseItem.loyaltyDiscountInfo) {
                loyaltyDiscount += purchaseItem.loyaltyDiscountInfo.taxExclusiveDiscount || 0;
            } else {
                loyaltyDiscount += purchaseItem.loyaltyDiscount || 0;
            }
            if (purchaseItem.isTakeaway && !purchaseItem.itemType) {
                takeawayCharges += purchaseItem.takeawayCharges;
            }
            if (purchaseItem.display) {
                if (isTaxInclusiveDisplay) {
                    displaySubtotal +=
                        (purchaseItem.display.total || 0) - (purchaseItem.display.tax || 0);
                } else {
                    displaySubtotal += purchaseItem.display.total || 0;
                }
            }
        }

        if (purchaseItem.itemType === 'ServiceCharge' && includeServiceChargeItem) {
            serviceCharge = purchaseItem.total;
            serviceChargeTax = purchaseItem.tax;

            displayServiceCharge = purchaseItem.total;
            displayServiceChargeDiscount = purchaseItem.discount;
            displayServiceChargeBIRDiscount =
                (purchaseItem.seniorDiscount || 0) +
                (purchaseItem.pwdDiscount || 0) +
                (purchaseItem.athleteAndCoachDiscount || 0) +
                (purchaseItem.medalOfValorDiscount || 0) +
                (purchaseItem.soloParentDiscount || 0);
            displayTax += purchaseItem.tax;
        }

        if (purchaseItem.itemType === 'Discount' && purchaseItem.display) {
            displayDiscount = -purchaseItem.display.discount;
        }

        if (purchaseItem.itemType !== 'ServiceCharge' && includeBir) {
            if (transaction.birInfo) {
                seniorDiscount += purchaseItem.seniorDiscount || 0;
                pwdDiscount += purchaseItem.pwdDiscount || 0;
                athleteAndCoachDiscount += purchaseItem.athleteAndCoachDiscount || 0;
                medalOfValorDiscount += purchaseItem.medalOfValorDiscount || 0;
                soloParentDiscount += purchaseItem.soloParentDiscount || 0;
                taxableSales += purchaseItem.taxableAmount || 0;
                taxExemptedSales += purchaseItem.taxExemptAmount || 0;
                zeroRatedSales += purchaseItem.zeroRatedSales || 0;
                totalDeductedTax += purchaseItem.totalDeductedTax || 0;
            }
        }

        if (purchaseItem.isAmusementTax) {
            amusementTax += purchaseItem.tax;
        }
    }

    if (includeShippingFee) {
        if (transaction.shippingFee) {
            transaction.shippingFee = roundTo2DecimalPlaces(transaction.shippingFee);
            displayShippingFee =
                (transaction.shippingFee || 0) - (transaction.shippingFeeDiscount || 0);
        }
        total += (transaction.shippingFee || 0) - (transaction.shippingFeeDiscount || 0);
    }
    if (includeFixedFee) {
        transaction.fixedFee = roundTo2DecimalPlaces(transaction.fixedFee || 0);
        displayFixedFee = transaction.fixedFee || 0;
        total += transaction.fixedFee || 0;
    }
    displayLoyaltyDiscount = roundTo2DecimalPlaces(loyaltyDiscount);
    transaction.subtotal = roundTo2DecimalPlaces(subtotal);
    transaction.discount = roundTo2DecimalPlaces(discount);
    transaction.tax = roundTo2DecimalPlaces(tax);
    transaction.total = roundTo2DecimalPlaces(total);
    transaction.serviceCharge = roundTo2DecimalPlaces(serviceCharge);
    transaction.serviceChargeTax = roundTo2DecimalPlaces(serviceChargeTax);

    transaction.seniorDiscount = roundTo2DecimalPlaces(seniorDiscount);
    transaction.pwdDiscount = roundTo2DecimalPlaces(pwdDiscount);
    transaction.taxableSales = roundTo2DecimalPlaces(taxableSales);
    transaction.taxExemptedSales = roundTo2DecimalPlaces(taxExemptedSales);
    transaction.zeroRatedSales = roundTo2DecimalPlaces(zeroRatedSales);
    transaction.totalDeductedTax = roundTo2DecimalPlaces(totalDeductedTax);
    transaction.takeawayCharges = roundTo2DecimalPlaces(takeawayCharges);
    transaction.amusementTax = roundTo2DecimalPlaces(amusementTax);
    const birInfo = transaction.birInfo || {};
    if (
        ['SC/PWD', 'ATHLETE_AND_COACH', 'MEDAL_OF_VALOR', 'DIPLOMAT', 'SOLO_PARENT'].includes(
            birInfo.discountType,
        )
    ) {
        const addonBirCompliance = {
            discountType: birInfo.discountType,
            athleteAndCoachDiscount,
            medalOfValorDiscount,
            soloParentDiscount,
        };
        if (birInfo.collectedInfo) {
            addonBirCompliance.collectedInfo = JSON.stringify(birInfo.collectedInfo);
        }
        transaction.addonBirCompliance = addonBirCompliance;
    }

    displayTotal = total;
    displayTax = tax;
    displaySubtotal =
        displayTotal -
        displayTax -
        displayDiscount -
        displayServiceCharge -
        displayServiceChargeDiscount +
        displayServiceChargeBIRDiscount -
        displayShippingFee +
        displayLoyaltyDiscount -
        displayFixedFee;

    transaction.display = {
        subtotal: roundTo2DecimalPlaces(displaySubtotal),
        discount: roundTo2DecimalPlaces(displayDiscount),
        serviceCharge: roundTo2DecimalPlaces(displayServiceCharge),
        tax: roundTo2DecimalPlaces(displayTax),
        total: roundTo2DecimalPlaces(displayTotal),
    };

    if (source === CSource.POS && includeBir) {
        const displayBIRDiscount = roundTo2DecimalPlaces(
            seniorDiscount +
                pwdDiscount +
                athleteAndCoachDiscount +
                medalOfValorDiscount +
                soloParentDiscount,
        );
        transaction.display.birDiscount = displayBIRDiscount;
        transaction.display.subtotal += displayBIRDiscount;
    }

    if (includeShippingFee) {
        if (transaction.shippingFee) {
            transaction.display.shippingFee = displayShippingFee;
        }
    }
    if (includeFixedFee) {
        if (transaction.fixedFee) {
            transaction.display.fixedFee = displayFixedFee;
        }
    }
}
