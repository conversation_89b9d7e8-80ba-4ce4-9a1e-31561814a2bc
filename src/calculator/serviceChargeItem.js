import { get } from 'lodash';
import calculateItem from './item';
import conditionCalculator from '../promotionCalculator/conditionCalculator';

export function taxExclusiveTransactionTotalWithoutTakeawayItems(transaction) {
    const items = transaction.items || [];
    let subtotal = 0;
    if (!Array.isArray(items) || items.length <= 0) {
        return subtotal;
    }
    for (const item of items) {
        if (!item.itemType && !item.isServiceChargeNotApplicable) {
            subtotal += item.subTotal - item.discount;
        }
    }

    return subtotal;
}

export function calculateServiceChargeTaxInclusive(serviceChargeItem, transaction) {
    const serviceChargeRate = serviceChargeItem.rate;
    const taxExclusiveTransactionTotal = taxExclusiveTransactionTotalWithoutTakeawayItems(
        transaction,
    );
    const unitPrice = taxExclusiveTransactionTotal * serviceChargeRate;
    serviceChargeItem.unitPrice = unitPrice;
    serviceChargeItem.quantity = 1;

    delete serviceChargeItem.loyaltyDiscount;
    delete serviceChargeItem.isBirDiscountCalculated;

    calculateItem(serviceChargeItem, true);
}

export function calculateServiceChargeTaxExclusive(serviceChargeItem, transaction) {
    const serviceChargeRate = serviceChargeItem.rate;
    const taxExclusiveTransactionTotal = taxExclusiveTransactionTotalWithoutTakeawayItems(
        transaction,
    );
    const unitPrice = taxExclusiveTransactionTotal * serviceChargeRate;
    serviceChargeItem.unitPrice = unitPrice;
    serviceChargeItem.quantity = 1;

    delete serviceChargeItem.loyaltyDiscount;

    calculateItem(serviceChargeItem, false);
}

function mappingServiceChargeExclusiveCondition(condition) {
    const { type, operator, operand } = condition;
    const applicableCondition = { operator, operand, entity: 'product' };
    switch (type) {
        case 'productIds':
            applicableCondition.propertyName = 'id';
            break;
        case 'categories':
            applicableCondition.propertyName = 'category';
            break;
        case 'tags':
            applicableCondition.propertyName = 'tags';
            applicableCondition.operator = 'contains';

            break;
        default:
            break;
    }
    return applicableCondition;
}

export function calculateItemServiceChargeApplicable(
    purchaseItem,
    excludeServiceChargeProductCondition,
) {
    let isServiceChargeNotApplicable = Boolean(purchaseItem.isTakeaway);
    if (
        !isServiceChargeNotApplicable &&
        excludeServiceChargeProductCondition &&
        excludeServiceChargeProductCondition.isEnabled
    ) {
        const { conditionsMatch, conditions } = excludeServiceChargeProductCondition;
        if (conditions && conditions.length > 0) {
            switch (conditionsMatch) {
                case 'Any':
                    // match any of the conditions is satisfied
                    for (const condition of conditions) {
                        const applicableCondition = mappingServiceChargeExclusiveCondition(
                            condition,
                        );
                        isServiceChargeNotApplicable = conditionCalculator.isSatisfied(
                            applicableCondition,
                            purchaseItem.product,
                        );
                        if (isServiceChargeNotApplicable) break;
                    }
                    break;
                case 'All':
                    // all conditions need to be matched at the same time for the condition to be considered satisfied
                    isServiceChargeNotApplicable = true;
                    for (const condition of conditions) {
                        const applicableCondition = mappingServiceChargeExclusiveCondition(
                            condition,
                        );
                        isServiceChargeNotApplicable =
                            isServiceChargeNotApplicable &&
                            conditionCalculator.isSatisfied(
                                applicableCondition,
                                purchaseItem.product,
                            );
                        if (!isServiceChargeNotApplicable) {
                            break;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
    }
    purchaseItem.isServiceChargeNotApplicable = isServiceChargeNotApplicable;
}

export function filterItemsWithoutServiceCharge(serviceChargeItem, transaction) {
    const excludeServiceChargeProductCondition = get(
        serviceChargeItem,
        'excludeServiceChargeProductCondition',
    );
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            calculateItemServiceChargeApplicable(
                purchaseItem,
                excludeServiceChargeProductCondition,
            );
        }
    }
}

export default function calculateServiceCharge(
    serviceChargeItem,
    transaction,
    isTaxInclusiveDisplay,
) {
    if (isTaxInclusiveDisplay) {
        return calculateServiceChargeTaxInclusive(serviceChargeItem, transaction);
    }
    return calculateServiceChargeTaxExclusive(serviceChargeItem, transaction);
}
