import { get } from 'lodash';
import { DiscountType, PromotionDiscountType, roundTo2DecimalPlaces } from '../../utils';
import {
    insertItemCalculationDiscountWithTaxInclusive,
    insertItemCalculationDiscountWithTaxExclusive,
} from './itemCalculationFields';

export function calculateTakePercentOffPromotion(
    appliedPromotion,
    purchaseItem,
    isTaxInclusiveDisplay,
) {
    if (isTaxInclusiveDisplay) {
        const taxInclusiveUnitPrice = purchaseItem.taxInclusiveUnitPrice;
        const itemQuantity = get(appliedPromotion, 'quantity', 0);
        const discountPercentageNumerator = appliedPromotion.inputValue;
        const taxRate = purchaseItem.taxRate || 0;

        const taxInclusiveDiscount = roundTo2DecimalPlaces(
            taxInclusiveUnitPrice * itemQuantity * (discountPercentageNumerator / 100),
        );
        insertItemCalculationDiscountWithTaxInclusive(
            purchaseItem,
            DiscountType.PROMOTION,
            taxInclusiveDiscount,
            PromotionDiscountType.PERCENTAGE,
            appliedPromotion.promotionId,
        );

        const taxExclusiveDiscount = taxInclusiveDiscount / (1 + taxRate);

        appliedPromotion.discount = taxExclusiveDiscount;
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxInclusiveDiscount),
        };
    } else {
        const taxExclusiveUnitPrice = purchaseItem.taxExclusiveUnitPrice;
        const itemQuantity = get(appliedPromotion, 'quantity', 0);
        const discountPercentageNumerator = appliedPromotion.inputValue;

        const taxExclusiveDiscount = roundTo2DecimalPlaces(
            taxExclusiveUnitPrice * itemQuantity * (discountPercentageNumerator / 100),
        );
        insertItemCalculationDiscountWithTaxExclusive(
            purchaseItem,
            DiscountType.PROMOTION,
            taxExclusiveDiscount,
            PromotionDiscountType.PERCENTAGE,
            appliedPromotion.promotionId,
        );
        appliedPromotion.discount = taxExclusiveDiscount;
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
        };
    }
}

export function calculateOverwritePromotion(appliedPromotion, purchaseItem, isTaxInclusiveDisplay) {
    if (isTaxInclusiveDisplay) {
        const taxInclusiveUnitPrice = purchaseItem.taxInclusiveUnitPrice;
        const itemQuantity = get(appliedPromotion, 'quantity', 0);
        const taxExclusiveDiscountedUnitPrice = appliedPromotion.inputValue;
        const taxRate = purchaseItem.taxRate || 0;

        const taxInclusiveDiscountedUnitPrice = roundTo2DecimalPlaces(
            taxExclusiveDiscountedUnitPrice * (1 + taxRate),
        );
        const taxInclusiveDiscount = roundTo2DecimalPlaces(
            (taxInclusiveUnitPrice - taxInclusiveDiscountedUnitPrice) * itemQuantity,
        );
        insertItemCalculationDiscountWithTaxInclusive(
            purchaseItem,
            DiscountType.PROMOTION,
            taxInclusiveDiscount,
            PromotionDiscountType.FIXED_UNIT_PRICE,
            appliedPromotion.promotionId,
        );
        const taxExclusiveDiscount = taxInclusiveDiscount / (1 + taxRate);

        appliedPromotion.discount = taxExclusiveDiscount;
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxInclusiveDiscount),
        };
    } else {
        const taxExclusiveUnitPrice = purchaseItem.taxExclusiveUnitPrice;
        const itemQuantity = get(appliedPromotion, 'quantity', 0);
        const taxExclusiveDiscountedUnitPrice = appliedPromotion.inputValue;

        const taxExclusiveDiscount = roundTo2DecimalPlaces(
            (taxExclusiveUnitPrice - taxExclusiveDiscountedUnitPrice) * itemQuantity,
        );
        insertItemCalculationDiscountWithTaxExclusive(
            purchaseItem,
            DiscountType.PROMOTION,
            taxExclusiveDiscount,
            PromotionDiscountType.FIXED_UNIT_PRICE,
            appliedPromotion.promotionId,
        );
        appliedPromotion.discount = taxExclusiveDiscount;
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
        };
    }
}

export function calculateBuyXGetYFreePromotion(
    appliedPromotion,
    purchaseItem,
    isTaxInclusiveDisplay,
) {
    if (isTaxInclusiveDisplay) {
        const taxInclusiveUnitPrice = purchaseItem.taxInclusiveUnitPrice;
        const freedItemQuantity = appliedPromotion.inputValue;
        const taxRate = purchaseItem.taxRate || 0;

        const taxInclusiveDiscount = roundTo2DecimalPlaces(
            taxInclusiveUnitPrice * freedItemQuantity,
        );
        insertItemCalculationDiscountWithTaxInclusive(
            purchaseItem,
            DiscountType.PROMOTION,
            taxInclusiveDiscount,
            PromotionDiscountType.BUY_X_FREE_Y,
            appliedPromotion.promotionId,
        );
        const taxExclusiveDiscount = taxInclusiveDiscount / (1 + taxRate);

        appliedPromotion.discount = taxExclusiveDiscount;
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxInclusiveDiscount),
        };
    } else {
        const taxExclusiveUnitPrice = purchaseItem.taxExclusiveUnitPrice;
        const freedItemQuantity = appliedPromotion.inputValue;

        const taxExclusiveDiscount = roundTo2DecimalPlaces(
            taxExclusiveUnitPrice * freedItemQuantity,
        );
        insertItemCalculationDiscountWithTaxExclusive(
            purchaseItem,
            DiscountType.PROMOTION,
            taxExclusiveDiscount,
            PromotionDiscountType.BUY_X_FREE_Y,
            appliedPromotion.promotionId,
        );
        appliedPromotion.discount = taxExclusiveDiscount;
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
        };
    }
}

export default function calculateItemPromotions(purchaseItem, isTaxInclusiveDisplay) {
    if (purchaseItem.promotions && purchaseItem.promotions.length > 0) {
        for (const appliedPromotion of purchaseItem.promotions) {
            switch (appliedPromotion.type) {
                case PromotionDiscountType.PERCENTAGE: // percentage
                    calculateTakePercentOffPromotion(
                        appliedPromotion,
                        purchaseItem,
                        isTaxInclusiveDisplay,
                    );
                    break;

                case PromotionDiscountType.FIXED_UNIT_PRICE: // fixedUnitPrice
                    calculateOverwritePromotion(
                        appliedPromotion,
                        purchaseItem,
                        isTaxInclusiveDisplay,
                    );
                    break;

                case PromotionDiscountType.BUY_X_FREE_Y: //buyXFreeY
                    calculateBuyXGetYFreePromotion(
                        appliedPromotion,
                        purchaseItem,
                        isTaxInclusiveDisplay,
                    );
                    break;

                default:
                    break;
            }
        }
    }
}
