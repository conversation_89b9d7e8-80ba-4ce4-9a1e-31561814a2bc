import { discountFlagName, add, div2Fixed, mul2Fixed, roundTo2DecimalPlaces } from '../../utils';
import calculateItemPromotions from './itemPromotions';
import calculateItemDiscount from './itemDiscount';
import calculateBirFields from './specials/birFields';

export default function calculateItem(purchaseItem, isTaxInclusiveDisplay, isReturn) {
    if (isTaxInclusiveDisplay) {
        if (isReturn) {
            return calculateReturnItemTaxInclusive(purchaseItem);
        }
        return calculateItemTaxInclusive(purchaseItem);
    }
    if (isReturn) {
        return calculateReturnItemTaxExclusive(purchaseItem);
    }
    return calculateItemTaxExclusive(purchaseItem);
}

export function calculateItemTaxInclusive(purchaseItem) {
    let productPrice = purchaseItem.unitPrice || 0;
    if (purchaseItem.selectedOptions && purchaseItem.selectedOptions.length > 0) {
        for (const selectedOption of purchaseItem.selectedOptions) {
            productPrice += selectedOption.optionValue * selectedOption.quantity;
        }
    }
    const quantity = purchaseItem.quantity;
    const taxRate = purchaseItem.taxRate || 0;

    const taxRateCardinal = add(1, taxRate);

    const taxInclusiveUnitPrice = mul2Fixed(productPrice, taxRateCardinal);

    const taxInclusiveSubtotal = mul2Fixed(taxInclusiveUnitPrice, quantity);
    const taxExclusiveSubtotal = div2Fixed(taxInclusiveSubtotal, taxRateCardinal);

    // product purchaseItem don't have itemType
    // service charge item will calculate loyalty discount
    if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
        purchaseItem.taxInclusiveUnitPrice = taxInclusiveUnitPrice;
        calculateItemPromotions(purchaseItem, true);
        if (!purchaseItem.promotionAppliedQuantityMap) {
            delete purchaseItem.taxInclusiveUnitPrice;
        }

        purchaseItem.taxInclusiveSubtotal = taxInclusiveSubtotal;
        purchaseItem.taxExclusiveSubtotal = taxExclusiveSubtotal;
        calculateItemDiscount(purchaseItem, true);
    }

    const adhocDiscount = purchaseItem.adhocDiscount || 0;
    if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
        purchaseItem.adhocDiscount = adhocDiscount;
    }

    let taxInclusiveDiscount = mul2Fixed(adhocDiscount, taxRateCardinal);
    if (purchaseItem.promotions && purchaseItem.promotions.length > 0) {
        for (const appliedPromotion of purchaseItem.promotions) {
            taxInclusiveDiscount += mul2Fixed(appliedPromotion.discount, taxRateCardinal);
        }
    }

    const taxInclusiveTotal = taxInclusiveSubtotal - taxInclusiveDiscount;
    let taxExclusiveTotal = div2Fixed(taxInclusiveTotal, taxRateCardinal);

    let tax = taxInclusiveTotal - taxExclusiveTotal;
    const originalTax = taxInclusiveSubtotal - taxExclusiveSubtotal;

    let taxExclusiveDiscount = taxInclusiveDiscount + tax - originalTax;

    // only product and service charge item need to calculate BIR related fields
    if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
        purchaseItem.taxInclusiveTotal = taxInclusiveTotal;
        purchaseItem.taxInclusiveDiscount = taxInclusiveDiscount;

        calculateBirFields(purchaseItem, true);

        if (purchaseItem.valuesAfterBIR) {
            taxExclusiveTotal = purchaseItem.valuesAfterBIR.taxExclusiveTotal;
            tax = purchaseItem.valuesAfterBIR.tax;
            taxExclusiveDiscount = purchaseItem.valuesAfterBIR.taxExclusiveDiscount;
        }

        delete purchaseItem.taxInclusiveTotal;
        delete purchaseItem.taxInclusiveDiscount;
    }

    purchaseItem.total = roundTo2DecimalPlaces(taxExclusiveTotal);
    purchaseItem.tax = roundTo2DecimalPlaces(tax);
    purchaseItem.notRoundedOriginalTax = tax;
    purchaseItem.discount = roundTo2DecimalPlaces(taxExclusiveDiscount);
    purchaseItem.subTotal = roundTo2DecimalPlaces(taxExclusiveSubtotal);

    if (
        (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') &&
        !purchaseItem[discountFlagName.fullBill] &&
        !purchaseItem[discountFlagName.promotion] &&
        !purchaseItem[discountFlagName.loyalty]
    ) {
        purchaseItem.display = {
            total: roundTo2DecimalPlaces(taxExclusiveTotal + tax),
            subtotal: roundTo2DecimalPlaces(taxInclusiveSubtotal),
            tax: roundTo2DecimalPlaces(tax),
        };
    }
    delete purchaseItem[discountFlagName.fullBill];
    delete purchaseItem[discountFlagName.promotion];
    delete purchaseItem[discountFlagName.loyalty];
}

export function calculateItemTaxExclusive(purchaseItem) {
    let productPrice = purchaseItem.unitPrice || 0;
    if (purchaseItem.selectedOptions && purchaseItem.selectedOptions.length > 0) {
        for (const selectedOption of purchaseItem.selectedOptions) {
            productPrice += selectedOption.optionValue * selectedOption.quantity;
        }
    }
    const quantity = purchaseItem.quantity;
    const taxRate = purchaseItem.taxRate || 0;

    const taxExclusiveSubtotal = roundTo2DecimalPlaces(productPrice * quantity);
    const taxInclusiveSubtotal = roundTo2DecimalPlaces(taxExclusiveSubtotal * (1 + taxRate));

    // product purchaseItem don't have itemType
    // service charge item will calculate loyalty discount
    if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
        const taxExclusiveUnitPrice = roundTo2DecimalPlaces(productPrice);
        purchaseItem.taxExclusiveUnitPrice = taxExclusiveUnitPrice;
        calculateItemPromotions(purchaseItem, false);
        if (!purchaseItem.promotionAppliedQuantityMap) {
            delete purchaseItem.taxExclusiveUnitPrice;
        }

        purchaseItem.taxExclusiveSubtotal = taxExclusiveSubtotal;
        purchaseItem.taxInclusiveSubtotal = taxInclusiveSubtotal;
        calculateItemDiscount(purchaseItem, false);
    }

    const adhocDiscount = purchaseItem.adhocDiscount || 0;
    if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
        purchaseItem.adhocDiscount = adhocDiscount;
    }

    let taxExclusiveDiscount = roundTo2DecimalPlaces(adhocDiscount);
    if (purchaseItem.promotions && purchaseItem.promotions.length > 0) {
        for (const appliedPromotion of purchaseItem.promotions) {
            taxExclusiveDiscount += roundTo2DecimalPlaces(appliedPromotion.discount);
        }
    }

    const taxExclusiveTotal = taxExclusiveSubtotal - taxExclusiveDiscount;

    const tax = roundTo2DecimalPlaces(taxExclusiveTotal * taxRate);

    purchaseItem.total = roundTo2DecimalPlaces(taxExclusiveTotal);
    purchaseItem.tax = roundTo2DecimalPlaces(tax);
    purchaseItem.notRoundedOriginalTax = tax;
    purchaseItem.discount = roundTo2DecimalPlaces(taxExclusiveDiscount);
    purchaseItem.subTotal = roundTo2DecimalPlaces(taxExclusiveSubtotal);

    if (
        (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') &&
        !purchaseItem[discountFlagName.fullBill] &&
        !purchaseItem[discountFlagName.promotion] &&
        !purchaseItem[discountFlagName.loyalty]
    ) {
        purchaseItem.display = {
            total: roundTo2DecimalPlaces(taxExclusiveTotal),
            subtotal: roundTo2DecimalPlaces(taxExclusiveSubtotal),
            tax: roundTo2DecimalPlaces(tax),
        };
    }
    delete purchaseItem[discountFlagName.fullBill];
    delete purchaseItem[discountFlagName.promotion];
    delete purchaseItem[discountFlagName.loyalty];
}

export function calculateReturnItemTaxInclusive(purchaseItem) {
    // product purchaseItem don't have itemType
    if (!purchaseItem.itemType) {
        const originQty = purchaseItem.quantity;
        const originTax = purchaseItem.tax;
        const originTaxExclusiveTotal = purchaseItem.total;
        const originTaxExclusiveSubTotal = purchaseItem.subTotal;
        const originTaxExclusiveDiscount = purchaseItem.discount;

        const returnQty = purchaseItem.return.quantity;
        let returnTaxInclusiveTotal = purchaseItem.return.total || 0;
        let returnTaxExclusiveTotal = 0;
        let returnTax = 0;

        const returnUnitTax = originTax / originQty;
        const returnUnitTaxExclusiveTotal = originTaxExclusiveTotal / originQty;
        const returnUnitTaxExclusiveSubTotal = originTaxExclusiveSubTotal / originQty;
        const returnUnitTaxExclusiveDiscount = originTaxExclusiveDiscount / originQty;

        let returnTaxExclusiveDiscount = roundTo2DecimalPlaces(
            returnUnitTaxExclusiveDiscount * returnQty,
        );
        if (!returnTaxInclusiveTotal) {
            returnTaxExclusiveTotal = roundTo2DecimalPlaces(
                returnUnitTaxExclusiveTotal * returnQty,
            );
            returnTax = roundTo2DecimalPlaces(returnUnitTax * returnQty);
            returnTaxInclusiveTotal = returnTaxExclusiveTotal + returnTax;
        } else {
            const taxRate = purchaseItem.taxRate || 0;
            returnTaxExclusiveTotal = roundTo2DecimalPlaces(
                returnTaxInclusiveTotal / (1 + taxRate),
            );
            returnTax = returnTaxInclusiveTotal - returnTaxExclusiveTotal;
            returnTaxExclusiveDiscount +=
                roundTo2DecimalPlaces(returnUnitTaxExclusiveTotal * returnQty) -
                returnTaxExclusiveTotal;
        }
        const returnTaxExclusiveSubTotal = roundTo2DecimalPlaces(
            returnUnitTaxExclusiveSubTotal * returnQty,
        );

        purchaseItem.quantity = returnQty;
        if (purchaseItem.return.total === 0) {
            purchaseItem.discount = 0;
            purchaseItem.subTotal = 0;
            purchaseItem.tax = 0;
            purchaseItem.total = 0;
        } else {
            purchaseItem.discount = returnTaxExclusiveDiscount;
            purchaseItem.subTotal = returnTaxExclusiveSubTotal;
            purchaseItem.tax = returnTax;
            purchaseItem.total = returnTaxExclusiveTotal;
        }

        purchaseItem.display = {
            total: roundTo2DecimalPlaces(returnTaxInclusiveTotal),
            tax: roundTo2DecimalPlaces(returnTax),
        };
    }
}

export function calculateReturnItemTaxExclusive(purchaseItem) {
    // product purchaseItem don't have itemType
    if (!purchaseItem.itemType) {
        const originQty = purchaseItem.quantity;
        const originTax = purchaseItem.tax;
        const originTaxExclusiveTotal = purchaseItem.total;
        const originTaxExclusiveSubTotal = purchaseItem.subTotal;
        const originTaxExclusiveDiscount = purchaseItem.discount;

        const returnQty = purchaseItem.return.quantity;
        let returnTaxExclusiveTotal = purchaseItem.return.total || 0;
        let returnTax = 0;

        const returnUnitTax = originTax / originQty;
        const returnUnitTaxExclusiveTotal = originTaxExclusiveTotal / originQty;
        const returnUnitTaxExclusiveSubTotal = originTaxExclusiveSubTotal / originQty;
        const returnUnitTaxExclusiveDiscount = originTaxExclusiveDiscount / originQty;

        let returnTaxExclusiveDiscount = roundTo2DecimalPlaces(
            returnUnitTaxExclusiveDiscount * returnQty,
        );
        if (!returnTaxExclusiveTotal) {
            returnTaxExclusiveTotal = roundTo2DecimalPlaces(
                returnUnitTaxExclusiveTotal * returnQty,
            );
            returnTax = roundTo2DecimalPlaces(returnUnitTax * returnQty);
        } else {
            const taxRate = purchaseItem.taxRate || 0;
            returnTax = roundTo2DecimalPlaces(returnTaxExclusiveTotal * taxRate);
            returnTaxExclusiveDiscount +=
                roundTo2DecimalPlaces(returnUnitTaxExclusiveTotal * returnQty) -
                returnTaxExclusiveTotal;
        }
        const returnTaxExclusiveSubTotal = roundTo2DecimalPlaces(
            returnUnitTaxExclusiveSubTotal * returnQty,
        );

        purchaseItem.quantity = returnQty;
        if (purchaseItem.return.total === 0) {
            purchaseItem.discount = 0;
            purchaseItem.subTotal = 0;
            purchaseItem.tax = 0;
            purchaseItem.total = 0;
        } else {
            purchaseItem.discount = returnTaxExclusiveDiscount;
            purchaseItem.subTotal = returnTaxExclusiveSubTotal;
            purchaseItem.tax = returnTax;
            purchaseItem.total = returnTaxExclusiveTotal;
        }

        purchaseItem.display = {
            total: roundTo2DecimalPlaces(returnTaxExclusiveTotal),
        };
    }
}

export function calculateItemWithTakeawayCharge(purchaseItem, takeawayCharge) {
    if (!purchaseItem.itemType) {
        if (purchaseItem.isTakeaway) {
            const takeawayCharges = takeawayCharge * purchaseItem.quantity;
            const subTotal = purchaseItem.subTotal + takeawayCharges;
            const total = purchaseItem.total + takeawayCharges;

            purchaseItem.subTotal = roundTo2DecimalPlaces(subTotal);
            purchaseItem.total = roundTo2DecimalPlaces(total);
            purchaseItem.takeawayCharges = takeawayCharges;
            if (purchaseItem.display) {
                const displayTotal = roundTo2DecimalPlaces(
                    purchaseItem.display.total + takeawayCharges,
                );
                const displaySubtotal = roundTo2DecimalPlaces(
                    purchaseItem.display.subtotal + takeawayCharges,
                );
                purchaseItem.display.total = displayTotal;
                purchaseItem.display.subtotal = displaySubtotal;
            }
        }
    }
}
