import { find, findIndex, get } from 'lodash';
import {
    mul,
    mul2Fixed,
    div2Fixed,
    add,
    div,
    sub,
    sub2Fixed,
    roundTo2DecimalPlaces,
    DiscountType,
} from '../../utils';

export function calculateItemOriginalFields(purchaseItem, takeawayCharge) {
    // full unitprice means unitPrice + priceDiff(s)
    if (!purchaseItem.itemType) {
        let fullPrice = purchaseItem.unitPrice;
        if (purchaseItem.selectedOptions && purchaseItem.selectedOptions.length > 0) {
            for (const selectedOption of purchaseItem.selectedOptions) {
                fullPrice += selectedOption.optionValue * selectedOption.quantity;
            }
        }

        // original fields
        const quantity = purchaseItem.quantity;
        const taxRate = purchaseItem.taxRate || 0;
        const taxRateCardinal = add(1, taxRate);

        const taxInclusiveUnitPrice = mul2Fixed(fullPrice, taxRateCardinal);
        const taxInclusiveSubtotal = mul2Fixed(taxInclusiveUnitPrice, quantity);
        const taxExclusiveSubtotal = div2Fixed(taxInclusiveSubtotal, taxRateCardinal);

        const tax = sub2Fixed(taxInclusiveSubtotal, taxExclusiveSubtotal);

        let taxExclusiveSubtotalWithTakeawayCharges = taxExclusiveSubtotal;
        if (!purchaseItem.itemType && purchaseItem.isTakeaway) {
            const takeawayCharges = (takeawayCharge || 0) * quantity;
            taxExclusiveSubtotalWithTakeawayCharges += takeawayCharges;
        }
        // total include takeaway charges
        const originalTax = tax;
        const originalSubTotal = taxExclusiveSubtotalWithTakeawayCharges;
        const originalTotal = originalSubTotal + originalTax;
        purchaseItem.calculation = {
            fullPrice,
            discounts: [],
            taxes: [],
            original: {
                tax: originalTax,
                subtotal: originalSubTotal,
                total: originalTotal,
                subTotalWithoutTakeawayCharge: taxExclusiveSubtotal,
            },
        };
    }
}

export function calculateServiceChargeItemOriginalFields(transaction) {
    const serviceChargeItem = find(
        transaction.items,
        purchaseItem => purchaseItem.itemType === 'ServiceCharge',
    );

    if (serviceChargeItem) {
        // total include takeaway charges
        const serviceChargeRate = serviceChargeItem.rate;
        let taxExclusiveTransactionTotalWithoutTakeawayCharge = 0;

        for (const purchaseItem of transaction.items) {
            if (!purchaseItem.itemType && !purchaseItem.isTakeaway) {
                const taxExclusiveItemTotalWithoutTakeawayCharge = get(
                    purchaseItem,
                    ['calculation', 'original', 'subTotalWithoutTakeawayCharge'],
                    0,
                );
                taxExclusiveTransactionTotalWithoutTakeawayCharge += taxExclusiveItemTotalWithoutTakeawayCharge;
            }
        }

        const fullPrice = mul(taxExclusiveTransactionTotalWithoutTakeawayCharge, serviceChargeRate);
        const quantity = 1;

        const taxRate = serviceChargeItem.taxRate || 0;
        const taxRateCardinal = add(1, taxRate);

        const taxInclusiveUnitPrice = mul2Fixed(fullPrice, taxRateCardinal);
        const taxInclusiveSubtotal = mul2Fixed(taxInclusiveUnitPrice, quantity);
        const taxExclusiveSubtotal = div2Fixed(taxInclusiveSubtotal, taxRateCardinal);

        const tax = sub2Fixed(taxInclusiveSubtotal, taxExclusiveSubtotal);

        const originalTax = tax;
        const originalSubTotal = taxExclusiveSubtotal;
        const originalTotal = originalSubTotal + originalTax;
        serviceChargeItem.calculation = {
            fullPrice,
            discounts: [],
            taxes: [],
            original: {
                tax: originalTax,
                subtotal: originalSubTotal,
                total: originalTotal,
            },
        };
    }
}

// itemLevel    ✅
// fullbill     ✅
// loyalty      ✅
// BIR          ✅
// Promotion    ✅
export function insertItemCalculationDiscount(
    purchaseItem,
    discountType,
    discountValue,
    deductedTax,
    discountSubType = undefined,
    promotionId = undefined,
) {
    const calculationDiscounts = get(purchaseItem, ['calculation', 'discounts']);
    // 需要防止这里重复push discount，判断discounts里面是否有改item，如果有则覆盖，如果没有则push到结尾
    // TRANSACTION_MANUAL_DISCOUNT: single & filter by type
    // ITEM_MANUAL_DISCOUNT:  single & filter by type
    // LOYALTY_DISCOUNT: single & filter by type
    // BIR_DISCOUNT:  single & filter by type
    // PROMOTION: multiple & filter by promotionId
    const inputCalculationDiscount = {
        type: discountType,
        discount: roundTo2DecimalPlaces(discountValue),
        deductedTax: roundTo2DecimalPlaces(deductedTax),
    };
    if (discountSubType) inputCalculationDiscount.subType = discountSubType;
    if (promotionId) inputCalculationDiscount.promotionId = promotionId;
    let foundIndex = -1;
    if (discountType === DiscountType.PROMOTION) {
        foundIndex = findIndex(
            calculationDiscounts,
            discountItem =>
                discountItem.type === discountType && discountItem.promotionId === promotionId,
        );
    } else if (discountType === DiscountType.BIR_DISCOUNT) {
        foundIndex = findIndex(
            calculationDiscounts,
            discountItem =>
                discountItem.type === discountType && discountItem.subType === discountSubType,
        );
    } else {
        foundIndex = findIndex(
            calculationDiscounts,
            discountItem => discountItem.type === discountType,
        );
    }

    if (foundIndex >= 0) {
        purchaseItem.calculation.discounts[foundIndex] = inputCalculationDiscount;
    } else {
        purchaseItem.calculation.discounts.push(inputCalculationDiscount);
    }
}

export function insertItemCalculationDiscountWithTaxInclusive(
    purchaseItem,
    discountType,
    taxInclusiveDiscount, // unrounded taxInclusiveDiscount
    discountSubType = undefined,
    promotionId = undefined,
) {
    if (!get(purchaseItem, ['calculation', 'discounts'])) return;

    const taxRate = purchaseItem.taxRate || 0;
    const taxRateCardinal = add(1, taxRate);
    const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal);
    const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

    insertItemCalculationDiscount(
        purchaseItem,
        discountType,
        taxExclusiveDiscount,
        discountTax,
        discountSubType,
        promotionId,
    );
}

export function insertItemCalculationDiscountDirectly(
    purchaseItem,
    discountType,
    taxExclusiveDiscount,
    discountTax,
    discountSubType = undefined,
    promotionId = undefined,
) {
    if (!get(purchaseItem, ['calculation', 'discounts'])) return;

    insertItemCalculationDiscount(
        purchaseItem,
        discountType,
        taxExclusiveDiscount,
        discountTax,
        discountSubType,
        promotionId,
    );
}

export function insertItemCalculationDiscountWithTaxExclusive(
    purchaseItem,
    discountType,
    taxExclusiveDiscount, // unrounded taxExclusiveDiscount
    discountSubType = undefined,
    promotionId = undefined,
) {
    if (!get(purchaseItem, ['calculation', 'discounts'])) return;

    const taxRate = purchaseItem.taxRate || 0;
    const taxRateCardinal = add(1, taxRate);
    const taxInclusiveDiscount = mul(taxExclusiveDiscount, taxRateCardinal);
    const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

    insertItemCalculationDiscount(
        purchaseItem,
        discountType,
        taxExclusiveDiscount,
        discountTax,
        discountSubType,
        promotionId,
    );
}

export function insertItemCalculationTaxes(purchaseItem) {
    if (!get(purchaseItem, ['calculation', 'taxes'])) return [];
    if (!get(purchaseItem, 'taxCode')) return [];

    const taxRate = purchaseItem.taxRate || 0;
    const tax = purchaseItem.tax || 0;
    const { taxCode, isVatExempted = false, isAmusementTax = false } = purchaseItem;

    purchaseItem.calculation.taxes.push({
        tax,
        taxCode,
        taxRate,
        isVatExempted,
        isAmusementTax,
    });
    return purchaseItem.calculation.taxes;
}
