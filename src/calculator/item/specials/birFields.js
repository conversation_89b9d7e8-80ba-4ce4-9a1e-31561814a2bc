import {
    roundTo2DecimalPlaces,
    BIRDiscountType,
    DiscountType,
    div,
    mul2Fixed,
    add,
    sub,
} from '../../../utils';
import { insertItemCalculationDiscount } from '../itemCalculationFields';

export default function calculateBirFields(purchaseItem, isTaxInclusiveDisplay) {
    if (purchaseItem.birInfo && isTaxInclusiveDisplay) {
        calculateBirTaxInclusive(
            purchaseItem.birInfo,
            purchaseItem,
            purchaseItem.isBirDiscountCalculated,
        );
    }
}

// eslint-disable-next-line no-unused-vars
export function calculateBirTaxInclusive(birInfo, purchaseItem, isBirDiscountCalculated) {
    calculateBirTaxInclusiveBeforeLoyaltyDiscount(birInfo, purchaseItem);
}

export function calculateBirTaxInclusiveBeforeLoyaltyDiscount(birInfo, purchaseItem) {
    //  discountType: [ "SC/PWD","SC","PWD", "ATHLETE_AND_COACH", "MEDAL_OF_VALOR", "DIPLOMAT" ]

    delete purchaseItem.zeroRatedSales;
    delete purchaseItem.taxableAmount;
    delete purchaseItem.taxExemptAmount;
    delete purchaseItem.totalDeductedTax;

    const { discountRate, seniorsCount, pwdCount, headCount, discountType } = birInfo;
    switch (discountType) {
        // Senior citizen, Person with disabilities (SC/PWD)
        // Cap at 5% of PHP1300 (tax exclusive) a week: PHP65
        // Each person can only enjoy this once a week. But our POS will not validate identification of a customer hence our check is only max of PHP65 discount per transaction.
        // Books & Donation  ---> VAT Exempted Sales
        case BIRDiscountType.SC_PWD:
            switch (birInfo.type) {
                case 'retail':
                    {
                        const taxRate = purchaseItem.taxRate || 0;

                        let taxInclusiveTotal = purchaseItem.taxInclusiveTotal;
                        let taxExclusiveTotal = roundTo2DecimalPlaces(
                            taxInclusiveTotal / (1 + taxRate),
                        );
                        let taxableAmount = taxExclusiveTotal;
                        let taxExclusiveDiscount = purchaseItem.discount;
                        const taxInclusiveDiscount = purchaseItem.taxInclusiveDiscount;
                        const discountTax = taxInclusiveDiscount - taxExclusiveDiscount;
                        const totalDeductedTax = discountTax;
                        const taxExclusiveBirDiscount = birInfo.expectedBIRDiscount || 0;
                        const tax = taxInclusiveTotal - taxExclusiveTotal;
                        const taxExclusiveSeniorDiscount =
                            taxExclusiveBirDiscount === 0
                                ? 0
                                : roundTo2DecimalPlaces(
                                      (taxExclusiveBirDiscount * seniorsCount) /
                                          (seniorsCount + pwdCount),
                                  );
                        const taxExclusivePwdDiscount =
                            taxExclusiveBirDiscount - taxExclusiveSeniorDiscount;

                        purchaseItem.seniorDiscount = roundTo2DecimalPlaces(
                            taxExclusiveSeniorDiscount,
                        );
                        purchaseItem.pwdDiscount = roundTo2DecimalPlaces(taxExclusivePwdDiscount);

                        // Retail SC/PWD only 5% discount on special items, without VAT exemption, so won't deduct any tax
                        if (taxExclusiveSeniorDiscount > 0) {
                            insertItemCalculationDiscount(
                                purchaseItem,
                                DiscountType.BIR_DISCOUNT,
                                taxExclusiveSeniorDiscount,
                                0,
                                BIRDiscountType.SC,
                            );
                        }
                        if (taxExclusivePwdDiscount > 0) {
                            insertItemCalculationDiscount(
                                purchaseItem,
                                DiscountType.BIR_DISCOUNT,
                                taxExclusivePwdDiscount,
                                0,
                                BIRDiscountType.PWD,
                            );
                        }

                        purchaseItem.isBirDiscountCalculated = true;

                        taxExclusiveDiscount += taxExclusiveBirDiscount;
                        taxInclusiveTotal -= taxExclusiveBirDiscount;

                        taxExclusiveTotal = taxInclusiveTotal - tax;
                        taxableAmount = taxExclusiveTotal;

                        purchaseItem.valuesAfterBIR = {
                            taxExclusiveTotal,
                            tax,
                            taxExclusiveDiscount,
                        };

                        if (purchaseItem.isVatExempted) {
                            // taxRate === 0 and isVatExempted
                            purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(
                                purchaseItem.taxInclusiveTotal,
                            );
                        } else if (taxRate === 0) {
                            purchaseItem.zeroRatedSales = roundTo2DecimalPlaces(taxExclusiveTotal);
                        } else {
                            purchaseItem.taxableAmount = roundTo2DecimalPlaces(taxableAmount);
                            purchaseItem.taxExemptAmount = 0;
                            purchaseItem.totalDeductedTax = roundTo2DecimalPlaces(totalDeductedTax);
                        }
                    }
                    break;

                default:
                    // f&b
                    {
                        const taxRate = purchaseItem.taxRate || 0;

                        let taxInclusiveTotal = purchaseItem.taxInclusiveTotal;

                        const taxExemptAmountWithTax = roundTo2DecimalPlaces(
                            taxInclusiveTotal * ((seniorsCount + pwdCount) / headCount),
                        );
                        const taxExemptAmount = roundTo2DecimalPlaces(
                            taxExemptAmountWithTax / (1 + taxRate),
                        );
                        const exemptedTax = taxExemptAmountWithTax - taxExemptAmount;

                        const taxableAmountWithTax = taxInclusiveTotal - taxExemptAmountWithTax;
                        const taxableAmount = roundTo2DecimalPlaces(
                            taxableAmountWithTax / (1 + taxRate),
                        );
                        const tax = taxableAmountWithTax - taxableAmount;

                        let taxExclusiveDiscount = purchaseItem.discount;
                        const taxInclusiveDiscount = purchaseItem.taxInclusiveDiscount;
                        const discountTax = taxInclusiveDiscount - taxExclusiveDiscount;
                        const totalDeductedTax = discountTax + exemptedTax;

                        const taxExclusiveBirDiscount = roundTo2DecimalPlaces(
                            taxExemptAmount * discountRate,
                        );
                        // SC
                        const cardinal = div(seniorsCount, add(seniorsCount, pwdCount));
                        const taxExclusiveSeniorDiscount = mul2Fixed(
                            taxExclusiveBirDiscount,
                            cardinal,
                        );
                        const seniorExemptedTax = mul2Fixed(exemptedTax, cardinal);
                        // PWD
                        const taxExclusivePwdDiscount = sub(
                            taxExclusiveBirDiscount,
                            taxExclusiveSeniorDiscount,
                        );
                        const pwdExemptedTax = sub(exemptedTax, seniorExemptedTax);

                        if (taxExclusiveSeniorDiscount > 0) {
                            insertItemCalculationDiscount(
                                purchaseItem,
                                DiscountType.BIR_DISCOUNT,
                                taxExclusiveSeniorDiscount,
                                seniorExemptedTax,
                                BIRDiscountType.SC,
                            );
                        }
                        if (taxExclusivePwdDiscount > 0) {
                            insertItemCalculationDiscount(
                                purchaseItem,
                                DiscountType.BIR_DISCOUNT,
                                taxExclusivePwdDiscount,
                                pwdExemptedTax,
                                BIRDiscountType.PWD,
                            );
                        }

                        purchaseItem.seniorDiscount = roundTo2DecimalPlaces(
                            taxExclusiveSeniorDiscount,
                        );
                        purchaseItem.pwdDiscount = roundTo2DecimalPlaces(taxExclusivePwdDiscount);
                        purchaseItem.isBirDiscountCalculated = true;

                        taxExclusiveDiscount += taxExclusiveBirDiscount;
                        taxInclusiveTotal =
                            taxableAmountWithTax + taxExemptAmount - taxExclusiveBirDiscount;

                        const taxExclusiveTotal = taxInclusiveTotal - tax;

                        purchaseItem.valuesAfterBIR = {
                            taxExclusiveTotal,
                            tax,
                            taxExclusiveDiscount,
                        };

                        if (purchaseItem.isVatExempted) {
                            // taxRate === 0 and isVatExempted
                            purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(taxExclusiveTotal);
                        } else if (taxRate === 0) {
                            purchaseItem.zeroRatedSales = roundTo2DecimalPlaces(taxExclusiveTotal);
                        } else {
                            purchaseItem.taxableAmount = roundTo2DecimalPlaces(taxableAmount);
                            purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(
                                taxExemptAmount - taxExclusiveBirDiscount,
                            );
                            purchaseItem.totalDeductedTax = roundTo2DecimalPlaces(totalDeductedTax);
                        }
                    }
                    break;
            }
            break;
        // ATHLETE_AND_COACH or MEDAL_OF_VALOR : only 20% discount
        // Books & Donation  ---> VAT Exempted Sales
        case BIRDiscountType.ATHLETE_AND_COACH:
        case BIRDiscountType.MEDAL_OF_VALOR:
            {
                // athleteAndCoachDiscount
                const taxRate = purchaseItem.taxRate || 0;

                let taxInclusiveTotal = purchaseItem.taxInclusiveTotal;

                const taxableAmountWithTax = taxInclusiveTotal;
                let taxableAmount = roundTo2DecimalPlaces(taxableAmountWithTax / (1 + taxRate));
                const taxExemptAmount = taxableAmount;
                const tax = taxableAmountWithTax - taxableAmount;

                let taxExclusiveDiscount = purchaseItem.discount;

                const taxExclusiveBirDiscount = roundTo2DecimalPlaces(taxableAmount * discountRate);
                insertItemCalculationDiscount(
                    purchaseItem,
                    DiscountType.BIR_DISCOUNT,
                    taxExclusiveBirDiscount,
                    0,
                    discountType,
                );

                if (discountType === 'ATHLETE_AND_COACH') {
                    purchaseItem.athleteAndCoachDiscount = taxExclusiveBirDiscount;
                } else {
                    purchaseItem.medalOfValorDiscount = taxExclusiveBirDiscount;
                }
                purchaseItem.isBirDiscountCalculated = true;

                taxExclusiveDiscount += taxExclusiveBirDiscount;
                taxInclusiveTotal = taxableAmountWithTax - taxExclusiveBirDiscount;

                const taxExclusiveTotal = taxInclusiveTotal - tax;
                taxableAmount = taxExclusiveTotal;

                purchaseItem.valuesAfterBIR = {
                    taxExclusiveTotal,
                    tax,
                    taxExclusiveDiscount,
                };

                if (purchaseItem.isVatExempted) {
                    purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(taxExclusiveTotal);
                } else if (taxRate === 0) {
                    purchaseItem.zeroRatedSales = roundTo2DecimalPlaces(taxExclusiveTotal);
                } else {
                    purchaseItem.taxableAmount = roundTo2DecimalPlaces(taxableAmount);
                }
            }
            break;
        // DIPLOMAT : only no tax  ---> Zero Rated Sales
        // Books & Donation  ---> VAT Exempted Sales
        case BIRDiscountType.DIPLOMAT:
            {
                const taxRate = purchaseItem.taxRate || 0;

                let taxInclusiveTotal = purchaseItem.taxInclusiveTotal;

                const taxExemptAmountWithTax = taxInclusiveTotal;
                const taxExemptAmount = roundTo2DecimalPlaces(
                    taxExemptAmountWithTax / (1 + taxRate),
                );

                const exemptedTax = taxExemptAmountWithTax - taxExemptAmount;
                const totalDeductedTax = exemptedTax;
                insertItemCalculationDiscount(
                    purchaseItem,
                    DiscountType.BIR_DISCOUNT,
                    0,
                    exemptedTax,
                    discountType,
                );

                const taxExclusiveDiscount = purchaseItem.discount;

                purchaseItem.isBirDiscountCalculated = true;
                taxInclusiveTotal = taxExemptAmount;

                const taxExclusiveTotal = taxInclusiveTotal;

                purchaseItem.valuesAfterBIR = {
                    taxExclusiveTotal,
                    tax: 0,
                    taxExclusiveDiscount,
                };

                if (purchaseItem.isVatExempted) {
                    purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(taxExclusiveTotal);
                } else {
                    purchaseItem.zeroRatedSales = roundTo2DecimalPlaces(taxExclusiveTotal);
                    purchaseItem.totalDeductedTax = roundTo2DecimalPlaces(totalDeductedTax);
                }
            }
            break;

        // Retail only , Books & Donation  ---> VAT Exempted Sales
        // SOLO_PARENT : 10% discount and no tax  ---> Zero Rated Sales if taxRate ===0, else VAT Exempted Sales

        case BIRDiscountType.SOLO_PARENT:
            {
                const taxRate = purchaseItem.taxRate || 0;

                const taxInclusiveTotal = purchaseItem.taxInclusiveTotal;

                const taxExemptAmountWithTax = taxInclusiveTotal;
                const taxExemptAmount = roundTo2DecimalPlaces(
                    taxExemptAmountWithTax / (1 + taxRate),
                );

                const exemptedTax = taxExemptAmountWithTax - taxExemptAmount;

                const totalDeductedTax = exemptedTax;

                let taxExclusiveDiscount = purchaseItem.discount;

                let taxExclusiveBirDiscount;
                if (purchaseItem.itemType === 'ServiceCharge') {
                    taxExclusiveBirDiscount = birInfo.expectedBIRDiscount || 0;
                } else {
                    taxExclusiveBirDiscount = roundTo2DecimalPlaces(taxExemptAmount * discountRate);
                }
                insertItemCalculationDiscount(
                    purchaseItem,
                    DiscountType.BIR_DISCOUNT,
                    taxExclusiveBirDiscount,
                    totalDeductedTax,
                    discountType,
                );
                purchaseItem.soloParentDiscount = taxExclusiveBirDiscount;

                purchaseItem.isBirDiscountCalculated = true;

                taxExclusiveDiscount += taxExclusiveBirDiscount;
                const taxExclusiveTotal = taxExemptAmount - taxExclusiveBirDiscount;

                purchaseItem.valuesAfterBIR = {
                    taxExclusiveTotal,
                    tax: 0,
                    taxExclusiveDiscount,
                };

                if (purchaseItem.isVatExempted) {
                    // taxRate === 0 and isVatExempted
                    purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(taxExclusiveTotal);
                } else if (taxRate === 0) {
                    purchaseItem.zeroRatedSales = roundTo2DecimalPlaces(taxExclusiveTotal);
                } else {
                    purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(taxExclusiveTotal);
                    purchaseItem.totalDeductedTax = roundTo2DecimalPlaces(totalDeductedTax);
                }
            }
            break;

        default:
            // BIRInfo is required when BIR enabled
            // BIR enabled but without BIR Discount
            // eslint-disable-next-line no-case-declarations
            const taxRate = purchaseItem.taxRate || 0;

            // eslint-disable-next-line no-case-declarations
            const taxInclusiveTotal = purchaseItem.taxInclusiveTotal;

            // eslint-disable-next-line no-case-declarations
            const taxExclusiveTotal = roundTo2DecimalPlaces(taxInclusiveTotal / (1 + taxRate));

            if (purchaseItem.isVatExempted) {
                purchaseItem.taxExemptAmount = roundTo2DecimalPlaces(taxExclusiveTotal);
            } else if (taxRate === 0) {
                purchaseItem.zeroRatedSales = roundTo2DecimalPlaces(taxExclusiveTotal);
            } else {
                purchaseItem.taxableAmount = roundTo2DecimalPlaces(taxExclusiveTotal);
            }
            break;
    }
}
