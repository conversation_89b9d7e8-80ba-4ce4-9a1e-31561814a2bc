import {
    discountFieldName,
    roundTo2DecimalPlaces,
    add,
    div2Fixed,
    div,
    mul2Fixed,
    mul,
    sub,
    DiscountType,
} from '../../utils';

import {
    insertItemCalculationDiscountWithTaxInclusive,
    insertItemCalculationDiscountWithTaxExclusive,
} from './itemCalculationFields';

export function calculateItemDiscountTaxInclusive(purchaseItem) {
    const taxRate = purchaseItem.taxRate || 0;
    let adhocDiscount = purchaseItem.adhocDiscount || 0;
    let taxInclusiveDiscount = 0;
    const taxRateCardinal = add(1, taxRate);

    if (purchaseItem.itemLevelDiscount) {
        const inputValue = purchaseItem.itemLevelDiscount.inputValue;
        const discountType = purchaseItem.itemLevelDiscount.type;

        let taxInclusiveItemLevelPromotionDiscount = 0;
        if (purchaseItem.promotions && purchaseItem.promotions.length > 0) {
            for (const appliedPromotion of purchaseItem.promotions) {
                taxInclusiveItemLevelPromotionDiscount += mul2Fixed(
                    appliedPromotion.discount,
                    taxRateCardinal,
                );
            }
        }

        const taxInclusiveTotal = sub(
            purchaseItem.taxInclusiveSubtotal,
            taxInclusiveItemLevelPromotionDiscount,
        );

        let equivalentValue = 0;
        let itemManualDiscount = 0;
        if (discountType === 'amount') {
            itemManualDiscount = roundTo2DecimalPlaces(Math.min(inputValue, taxInclusiveTotal));
            taxInclusiveDiscount += itemManualDiscount;
            equivalentValue = div2Fixed(mul(inputValue, 100), taxInclusiveTotal);
        } else if (discountType === 'percent') {
            itemManualDiscount = div2Fixed(mul(taxInclusiveTotal, inputValue), 100);
            taxInclusiveDiscount += itemManualDiscount;
            equivalentValue = itemManualDiscount;
        }
        insertItemCalculationDiscountWithTaxInclusive(
            purchaseItem,
            DiscountType.ITEM_MANUAL_DISCOUNT,
            itemManualDiscount,
        );
        purchaseItem.itemLevelDiscount.equivalentValue = equivalentValue;
    }

    if (!isNaN(purchaseItem[discountFieldName.fullBill])) {
        taxInclusiveDiscount += purchaseItem[discountFieldName.fullBill];
    }

    if (!isNaN(purchaseItem[discountFieldName.promotion])) {
        taxInclusiveDiscount += purchaseItem[discountFieldName.promotion];
    }

    let taxExclusiveDiscount = 0;

    if (taxInclusiveDiscount !== 0) {
        taxExclusiveDiscount = taxInclusiveDiscount / (1 + taxRate);
    }

    // loyalty discount already have it tax exclusive discount, cannot use inclusive one in items
    // if there is bir discount, need to calculate bir discount in loyalty discount
    if (!isNaN(purchaseItem[discountFieldName.loyalty])) {
        const taxInclusiveLoyaltyDiscount = purchaseItem[discountFieldName.loyalty];
        purchaseItem.loyaltyDiscountInfo = {};
        const taxExclusiveLoyaltyDiscount = taxInclusiveLoyaltyDiscount / (1 + taxRate);

        const loyaltyDiscountTax = taxInclusiveLoyaltyDiscount - taxExclusiveLoyaltyDiscount;

        taxExclusiveDiscount += taxExclusiveLoyaltyDiscount;

        purchaseItem.loyaltyDiscountInfo.taxExclusiveDiscount = taxExclusiveLoyaltyDiscount;
        purchaseItem.loyaltyDiscountInfo.discountTax = roundTo2DecimalPlaces(loyaltyDiscountTax);
    }

    if (taxExclusiveDiscount !== 0) {
        adhocDiscount = taxExclusiveDiscount;
    }

    purchaseItem.adhocDiscount = adhocDiscount;
}

export function calculateItemDiscountTaxExclusive(purchaseItem) {
    let adhocDiscount = purchaseItem.adhocDiscount || 0;
    let taxExclusiveDiscount = 0;

    if (purchaseItem.itemLevelDiscount) {
        const inputValue = purchaseItem.itemLevelDiscount.inputValue;
        const discountType = purchaseItem.itemLevelDiscount.type;

        let taxExclusiveItemLevelPromotionDiscount = 0;
        if (purchaseItem.promotions && purchaseItem.promotions.length > 0) {
            for (const appliedPromotion of purchaseItem.promotions) {
                taxExclusiveItemLevelPromotionDiscount += roundTo2DecimalPlaces(
                    appliedPromotion.discount,
                );
            }
        }

        const taxExclusiveTotal =
            purchaseItem.taxExclusiveSubtotal - taxExclusiveItemLevelPromotionDiscount;

        let equivalentValue = 0;
        let itemManualDiscount = 0;
        if (discountType === 'amount') {
            itemManualDiscount = roundTo2DecimalPlaces(Math.min(inputValue, taxExclusiveTotal));
            taxExclusiveDiscount += itemManualDiscount;
            equivalentValue = div2Fixed(mul(inputValue, 100), taxExclusiveTotal);
        } else if (discountType === 'percent') {
            itemManualDiscount = div2Fixed(mul(taxExclusiveTotal, inputValue), 100);
            taxExclusiveDiscount += itemManualDiscount;
            equivalentValue = itemManualDiscount;
        }
        purchaseItem.itemLevelDiscount.equivalentValue = equivalentValue;

        insertItemCalculationDiscountWithTaxExclusive(
            purchaseItem,
            DiscountType.ITEM_MANUAL_DISCOUNT,
            itemManualDiscount,
        );
    }

    if (!isNaN(purchaseItem[discountFieldName.fullBill])) {
        taxExclusiveDiscount += purchaseItem[discountFieldName.fullBill];
    }

    if (!isNaN(purchaseItem[discountFieldName.promotion])) {
        taxExclusiveDiscount += purchaseItem[discountFieldName.promotion];
    }

    if (!isNaN(purchaseItem[discountFieldName.loyalty])) {
        taxExclusiveDiscount += purchaseItem[discountFieldName.loyalty];
    }

    if (taxExclusiveDiscount !== 0) {
        adhocDiscount = taxExclusiveDiscount;
    }

    purchaseItem.adhocDiscount = adhocDiscount;
}

export default function calculateItemDiscount(purchaseItem, isTaxInclusiveDisplay) {
    if (isTaxInclusiveDisplay) {
        return calculateItemDiscountTaxInclusive(purchaseItem);
    }
    return calculateItemDiscountTaxExclusive(purchaseItem);
}
