import { entries, filter, find, findIndex, get, groupBy, isEqual, sumBy } from 'lodash';
import { roundTo2DecimalPlaces, DiscountType, add2Fixed } from '../utils';
import { insertItemCalculationTaxes } from './item/itemCalculationFields';

export function calculateTransactionOriginalFields(transaction) {
    // original fields
    // subTotal did not include service charge and tax, but include takeawayCharges, sum of item.subTotal
    let originalSubtotal = 0;
    // tax is sum of item.tax
    let originalTax = 0;
    // total = subtotal(include takeawayCharges) + tax + serviceCharge + serviceChargeTax + shippingFee + fixedFee
    let originalTotal = 0;
    for (const purchaseItem of transaction.items) {
        if (purchaseItem.itemType !== 'Discount') {
            const itemOriginalSubTotal = get(purchaseItem, ['calculation', 'original', 'subtotal']);
            const itemOriginalTax = get(purchaseItem, ['calculation', 'original', 'tax']) || 0;
            const itemOriginalTotal = get(purchaseItem, ['calculation', 'original', 'total']);
            if (!purchaseItem.itemType) {
                originalSubtotal += itemOriginalSubTotal;
            }
            // there include service charge item,
            // means tax will include serviceChargeTax and total will include serviceCharge and serviceChargeTax
            originalTax += itemOriginalTax;
            originalTotal += itemOriginalTotal;
        }
    }
    const shippingFee = get(transaction, 'shippingFee') || 0;
    const fixedFee = get(transaction, 'fixedFee') || 0;
    originalTotal += shippingFee + fixedFee;

    transaction.calculation = {
        discounts: [],
        taxes: [],
        original: {
            tax: roundTo2DecimalPlaces(originalTax),
            subtotal: roundTo2DecimalPlaces(originalSubtotal),
            total: roundTo2DecimalPlaces(originalTotal),
        },
    };
}

// fullbill     ✅
// loyalty      ✅
// BIR          ✅
// Promotion    ✅
export function insertTrxCalculationDiscount(
    transaction,
    discountType,
    discountValue,
    deductedTax,
    discountSubType = undefined,
    promotionId = undefined,
) {
    const calculationDiscounts = get(transaction, ['calculation', 'discounts']);
    if (!calculationDiscounts) return;
    // 需要防止这里重复push discount，判断discounts里面是否有改item，如果有则覆盖，如果没有则push到结尾
    // TRANSACTION_MANUAL_DISCOUNT: single & filter by type
    // LOYALTY_DISCOUNT: single & filter by type
    // BIR_DISCOUNT:  single & filter by type
    // PROMOTION: multiple & filter by promotionId
    const inputCalculationDiscount = {
        type: discountType,
        discount: roundTo2DecimalPlaces(discountValue),
        deductedTax: roundTo2DecimalPlaces(deductedTax),
    };
    if (discountSubType) inputCalculationDiscount.subType = discountSubType;
    if (promotionId) inputCalculationDiscount.promotionId = promotionId;
    let foundIndex = -1;
    if (discountType === DiscountType.PROMOTION) {
        foundIndex = findIndex(
            calculationDiscounts,
            discountItem =>
                discountItem.type === discountType && discountItem.promotionId === promotionId,
        );
    } else if (discountType === DiscountType.BIR_DISCOUNT) {
        foundIndex = findIndex(
            calculationDiscounts,
            discountItem =>
                discountItem.type === discountType && discountItem.subType === discountSubType,
        );
    } else {
        foundIndex = findIndex(
            calculationDiscounts,
            discountItem => discountItem.type === discountType,
        );
    }

    if (foundIndex >= 0) {
        transaction.calculation.discounts[foundIndex] = inputCalculationDiscount; // NOSONAR
    } else {
        transaction.calculation.discounts.push(inputCalculationDiscount);
    }
}

export function insertTrxCalculationDiscountWithTaxExclusive(
    transaction,
    discountType,
    taxExclusiveDiscount, // unrounded taxExclusiveDiscount
    discountTax,
    discountSubType = undefined,
    promotionId = undefined,
) {
    if (!get(transaction, ['calculation', 'discounts'])) return;
    insertTrxCalculationDiscount(
        transaction,
        discountType,
        taxExclusiveDiscount,
        discountTax,
        discountSubType,
        promotionId,
    );
}

export function insertCalculationFields(transaction) {
    const needCalculateBirDiscount = Boolean(transaction.birInfo);
    // 使用groupBy优化BIR折扣计算
    const allBirDiscountItems = [];

    for (const purchaseItem of get(transaction, 'items', [])) {
        // calculateBirDiscount
        if (needCalculateBirDiscount && !purchaseItem.itemType) {
            const itemCalculationDiscounts = get(purchaseItem, ['calculation', 'discounts'], []);
            const itemBirCalculationDiscountItems = filter(
                itemCalculationDiscounts,
                calculationDiscountItem =>
                    calculationDiscountItem.type === DiscountType.BIR_DISCOUNT,
            );
            allBirDiscountItems.push(...itemBirCalculationDiscountItems);
        }
        // calculateTransactionTaxs
        const itemTaxs = insertItemCalculationTaxes(purchaseItem);
        for (const itemTax of itemTaxs) {
            const findInTax = find(get(transaction, ['calculation', 'taxes']), taxItem =>
                isEqual(taxItem.taxCode, itemTax.taxCode),
            );
            if (!findInTax) {
                transaction.calculation.taxes.push({ ...itemTax });
            } else {
                findInTax.tax = add2Fixed(findInTax.tax, itemTax.tax);
            }
        }
    }

    // 按subType分组并计算总和
    const groupedByType = groupBy(allBirDiscountItems, 'subType');
    const birDiscounts = entries(groupedByType).map(([discountType, items]) => ({
        discountType,
        discount: sumBy(items, 'discount') || 0,
        discountTax: sumBy(items, 'deductedTax') || 0,
    }));

    // calculateBirDiscount
    if (needCalculateBirDiscount) {
        for (const birDiscount of birDiscounts) {
            const discount = get(birDiscount, 'discount') || 0;
            const discountTax = get(birDiscount, 'discountTax') || 0;
            const discountSubType = get(birDiscount, 'discountType');
            insertTrxCalculationDiscount(
                transaction,
                DiscountType.BIR_DISCOUNT,
                discount,
                discountTax,
                discountSubType,
            );
        }
    }
}
