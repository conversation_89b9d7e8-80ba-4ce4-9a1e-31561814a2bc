import {
    roundTo2DecimalPlaces,
    PromotionDiscountType,
    DiscountType,
    DiscountTypeName,
    mul,
    add,
    div,
    getTaxRateCardinal,
    sub,
} from '../../utils';
import {
    distributePromotionToItemsTaxInclusive,
    distributePromotionToItemsTaxExclusive,
} from './distributor';

import { insertTrxCalculationDiscount } from '../calculationFields';

export default function calculateTransactionPromotion(transaction, isTaxInclusiveDisplay) {
    if (transaction.promotions && transaction.promotions.length > 0) {
        for (const appliedPromotion of transaction.promotions) {
            switch (appliedPromotion.type) {
                case PromotionDiscountType.ABSOLUTE:
                    calculateTakeAmountOffPromotion(
                        appliedPromotion,
                        transaction,
                        isTaxInclusiveDisplay,
                    );
                    break;

                case PromotionDiscountType.COMBO:
                    calculateComboPromotion(appliedPromotion, transaction, isTaxInclusiveDisplay);
                    break;

                case PromotionDiscountType.FREE_SHIPPING:
                    calculateFreeShippingPromotion(appliedPromotion, transaction);
                    break;

                default:
                    break;
            }
        }
    }
}

export function calculateTakeAmountOffPromotion(
    appliedPromotion,
    transaction,
    isTaxInclusiveDisplay,
) {
    if (isTaxInclusiveDisplay) {
        return calculateTakeAmountOffPromotionTaxInclusive(appliedPromotion, transaction);
    }
    return calculateTakeAmountOffPromotionTaxExclusive(appliedPromotion, transaction);
}

export function calculateTakeAmountOffPromotionTaxInclusive(appliedPromotion, transaction) {
    const { promotionId, inputValue, quantity = 1 } = appliedPromotion;

    if (inputValue) {
        let taxInclusivePromotionAppliedSubtotal = 0;
        let taxInclusivePromotionAppliedTotal = 0;
        for (const purchaseItem of transaction.items) {
            if (!purchaseItem.itemType) {
                const taxInclusiveUnitPrice = purchaseItem.taxInclusiveUnitPrice || 0;

                let consumedQuantity = 0;
                if (hasPurchaseItemAppliedPromotion(purchaseItem, promotionId)) {
                    consumedQuantity = purchaseItem.promotionAppliedQuantityMap[`${promotionId}`];

                    const taxInclusivePromotionAppliedItemSubtotal = mul(
                        taxInclusiveUnitPrice,
                        consumedQuantity,
                    );

                    const taxInclusivePromotionAppliedItemTotal = mul(
                        add(purchaseItem.total, purchaseItem.tax),
                        div(consumedQuantity, purchaseItem.quantity),
                    );
                    markPromotionAppliedSubtotal(
                        purchaseItem,
                        promotionId,
                        taxInclusivePromotionAppliedItemSubtotal,
                    );

                    taxInclusivePromotionAppliedSubtotal = add(
                        taxInclusivePromotionAppliedSubtotal,
                        taxInclusivePromotionAppliedItemSubtotal,
                    );
                    taxInclusivePromotionAppliedTotal = add(
                        taxInclusivePromotionAppliedTotal,
                        taxInclusivePromotionAppliedItemTotal,
                    );
                }
            }
        }

        markPromotionAppliedSubtotal(
            transaction,
            promotionId,
            taxInclusivePromotionAppliedSubtotal,
        );

        const taxInclusiveDiscount = roundTo2DecimalPlaces(
            Math.min(mul(inputValue, quantity), taxInclusivePromotionAppliedTotal),
        );
        // const taxExclusiveDiscount = taxInclusiveDiscount / (1 + (taxRate || 0));
        const { taxExclusiveDiscount } = distributePromotionToItemsTaxInclusive(
            transaction,
            taxInclusiveDiscount,
            appliedPromotion,
            DiscountTypeName.promotion,
        );

        appliedPromotion.discount = roundTo2DecimalPlaces(taxExclusiveDiscount);
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxInclusiveDiscount),
        };
    }
}

export function calculateTakeAmountOffPromotionTaxExclusive(appliedPromotion, transaction) {
    const { promotionId, inputValue, quantity = 1 } = appliedPromotion;
    if (inputValue) {
        let taxExclusivePromotionAppliedSubtotal = 0;
        let taxExclusivePromotionAppliedTotal = 0;
        for (const purchaseItem of transaction.items) {
            if (!purchaseItem.itemType) {
                const taxExclusiveUnitPrice = purchaseItem.taxExclusiveUnitPrice || 0;

                let consumedQuantity = 0;
                if (hasPurchaseItemAppliedPromotion(purchaseItem, promotionId)) {
                    consumedQuantity = purchaseItem.promotionAppliedQuantityMap[`${promotionId}`];

                    const taxExclusivePromotionAppliedItemSubtotal = mul(
                        taxExclusiveUnitPrice,
                        consumedQuantity,
                    );

                    const taxExclusivePromotionAppliedItemTotal = mul(
                        purchaseItem.total,
                        div(consumedQuantity, purchaseItem.quantity),
                    );

                    markPromotionAppliedSubtotal(
                        purchaseItem,
                        promotionId,
                        taxExclusivePromotionAppliedItemSubtotal,
                    );

                    taxExclusivePromotionAppliedSubtotal = add(
                        taxExclusivePromotionAppliedSubtotal,
                        taxExclusivePromotionAppliedItemSubtotal,
                    );
                    taxExclusivePromotionAppliedTotal = add(
                        taxExclusivePromotionAppliedTotal,
                        taxExclusivePromotionAppliedItemTotal,
                    );
                }
            }
        }

        markPromotionAppliedSubtotal(
            transaction,
            promotionId,
            taxExclusivePromotionAppliedSubtotal,
        );

        const taxExclusiveDiscount = roundTo2DecimalPlaces(
            Math.min(mul(inputValue, quantity), taxExclusivePromotionAppliedTotal),
        );

        distributePromotionToItemsTaxExclusive(
            transaction,
            taxExclusiveDiscount,
            appliedPromotion,
            DiscountTypeName.promotion,
        );

        appliedPromotion.discount = roundTo2DecimalPlaces(taxExclusiveDiscount);
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
        };
    }
}

export function calculateComboPromotion(appliedPromotion, transaction, isTaxInclusiveDisplay) {
    if (isTaxInclusiveDisplay) {
        return calculateComboPromotionTaxInclusive(appliedPromotion, transaction);
    }
    return calculateComboPromotionTaxExclusive(appliedPromotion, transaction);
}

function hasPurchaseItemAppliedPromotion(purchaseItem, promotionId) {
    return (
        purchaseItem.promotionAppliedQuantityMap &&
        purchaseItem.promotionAppliedQuantityMap[`${promotionId}`]
    );
}

function markPromotionAppliedSubtotal(instance, promotionId, value) {
    if (!instance.promotionAppliedSubtotalMap) {
        instance.promotionAppliedSubtotalMap = {};
    }
    instance.promotionAppliedSubtotalMap[`${promotionId}`] = value;
}

export function calculateComboPromotionTaxInclusive(appliedPromotion, transaction) {
    const { promotionId, inputValue, quantity } = appliedPromotion;

    if (inputValue) {
        const taxRateCardinal = getTaxRateCardinal(appliedPromotion);
        const taxInclusiveComboPrice = roundTo2DecimalPlaces(mul(inputValue, taxRateCardinal));
        const taxInclusiveComboTotal = mul(taxInclusiveComboPrice, quantity);

        let taxInclusiveOriginalSubTotal = 0;
        let taxInclusivePromotionAppliedSubtotal = 0;
        for (const purchaseItem of transaction.items) {
            if (!purchaseItem.itemType) {
                const taxInclusiveUnitPrice = purchaseItem.taxInclusiveUnitPrice || 0;

                let consumedQuantity = 0;
                if (hasPurchaseItemAppliedPromotion(purchaseItem, promotionId)) {
                    consumedQuantity = purchaseItem.promotionAppliedQuantityMap[`${promotionId}`];

                    const taxInclusivePromotionAppliedItemSubtotal = mul(
                        taxInclusiveUnitPrice,
                        consumedQuantity,
                    );

                    markPromotionAppliedSubtotal(
                        purchaseItem,
                        promotionId,
                        taxInclusivePromotionAppliedItemSubtotal,
                    );

                    taxInclusivePromotionAppliedSubtotal = add(
                        taxInclusivePromotionAppliedSubtotal,
                        taxInclusivePromotionAppliedItemSubtotal,
                    );
                }

                taxInclusiveOriginalSubTotal = add(
                    taxInclusiveOriginalSubTotal,
                    roundTo2DecimalPlaces(mul(taxInclusiveUnitPrice, consumedQuantity)),
                );
            }
        }

        markPromotionAppliedSubtotal(
            transaction,
            promotionId,
            taxInclusivePromotionAppliedSubtotal,
        );

        const taxInclusiveDiscount = sub(taxInclusiveOriginalSubTotal, taxInclusiveComboTotal);

        const { taxExclusiveDiscount } = distributePromotionToItemsTaxInclusive(
            transaction,
            taxInclusiveDiscount,
            appliedPromotion,
            DiscountTypeName.promotion,
        );

        appliedPromotion.discount = roundTo2DecimalPlaces(taxExclusiveDiscount);
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxInclusiveDiscount),
        };
    }
}

export function calculateComboPromotionTaxExclusive(appliedPromotion, transaction) {
    const { promotionId, inputValue, quantity } = appliedPromotion;

    if (inputValue) {
        const taxExclusiveComboPrice = roundTo2DecimalPlaces(inputValue);
        const taxExclusiveComboTotal = taxExclusiveComboPrice * quantity;

        let taxExclusiveOriginalSubTotal = 0;
        let taxExclusivePromotionAppliedSubtotal = 0;
        for (const purchaseItem of transaction.items) {
            if (!purchaseItem.itemType) {
                const taxExclusiveUnitPrice = purchaseItem.taxExclusiveUnitPrice || 0;

                let consumedQuantity = 0;
                if (hasPurchaseItemAppliedPromotion(purchaseItem, promotionId)) {
                    consumedQuantity = purchaseItem.promotionAppliedQuantityMap[`${promotionId}`];

                    const taxExclusivePromotionAppliedItemSubtotal = mul(
                        taxExclusiveUnitPrice,
                        consumedQuantity,
                    );

                    markPromotionAppliedSubtotal(
                        purchaseItem,
                        promotionId,
                        taxExclusivePromotionAppliedItemSubtotal,
                    );

                    taxExclusivePromotionAppliedSubtotal = add(
                        taxExclusivePromotionAppliedSubtotal,
                        taxExclusivePromotionAppliedItemSubtotal,
                    );
                }

                taxExclusiveOriginalSubTotal = add(
                    taxExclusiveOriginalSubTotal,
                    roundTo2DecimalPlaces(mul(taxExclusiveUnitPrice, consumedQuantity)),
                );
            }
        }

        markPromotionAppliedSubtotal(
            transaction,
            promotionId,
            taxExclusivePromotionAppliedSubtotal,
        );

        const taxExclusiveDiscount = sub(taxExclusiveOriginalSubTotal, taxExclusiveComboTotal);

        distributePromotionToItemsTaxExclusive(
            transaction,
            taxExclusiveDiscount,
            appliedPromotion,
            DiscountTypeName.promotion,
        );

        appliedPromotion.discount = roundTo2DecimalPlaces(taxExclusiveDiscount);
        appliedPromotion.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
        };
    }
}

export function calculateFreeShippingPromotion(appliedPromotion, transaction) {
    appliedPromotion.discount = 0;
    if (transaction.shippingFee) {
        transaction.shippingFee = roundTo2DecimalPlaces(transaction.shippingFee);
        const maxDiscountAmount = appliedPromotion.maxDiscountAmount || Infinity;
        transaction.shippingFeeDiscount = Math.min(transaction.shippingFee, maxDiscountAmount);
        appliedPromotion.shippingFeeDiscount = roundTo2DecimalPlaces(
            transaction.shippingFeeDiscount,
        );
        const { promotionId, type } = appliedPromotion;
        insertTrxCalculationDiscount(
            transaction,
            DiscountType.PROMOTION,
            appliedPromotion.shippingFeeDiscount,
            0,
            type,
            promotionId,
        );
    }
}
