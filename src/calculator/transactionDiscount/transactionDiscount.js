import { roundTo2DecimalPlaces, div2Fixed, mul, DiscountTypeName, sub } from '../../utils';
import {
    distributeDiscountToItemsTaxInclusive,
    distributeDiscountToItemsTaxExclusive,
} from './distributor';

export function calculateTransactionDiscountTaxInclusive(discountItem, transaction) {
    const { inputValue, type } = discountItem;
    const taxInclusiveTransactionTotal = transaction.total;

    if (inputValue && type) {
        let orderManualDiscount = 0;
        let equivalentValue = 0;
        if (type === 'amount') {
            orderManualDiscount = roundTo2DecimalPlaces(
                Math.min(inputValue, taxInclusiveTransactionTotal),
            );
            equivalentValue = div2Fixed(
                mul(orderManualDiscount, 100),
                taxInclusiveTransactionTotal,
            );
        } else if (type === 'percent') {
            orderManualDiscount = div2Fixed(mul(taxInclusiveTransactionTotal, inputValue), 100);
            equivalentValue = orderManualDiscount;
        }
        discountItem.equivalentValue = equivalentValue;
        // orderManualDiscount is a tax inclusive value
        const { taxExclusiveDiscount } = distributeDiscountToItemsTaxInclusive(
            transaction,
            orderManualDiscount,
            DiscountTypeName.fullBill,
        );
        discountItem.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
        };
    }
}

export function calculateTransactionDiscountTaxExclusive(discountItem, transaction) {
    const { inputValue, type } = discountItem;

    const taxExclusiveTransactionTotal = sub(transaction.total, transaction.tax);
    if (inputValue && type) {
        let orderManualDiscount = 0;
        let equivalentValue = 0;
        if (type === 'amount') {
            orderManualDiscount = roundTo2DecimalPlaces(
                Math.min(inputValue, taxExclusiveTransactionTotal),
            );
            equivalentValue = div2Fixed(
                mul(orderManualDiscount, 100),
                taxExclusiveTransactionTotal,
            );
        } else if (type === 'percent') {
            orderManualDiscount = div2Fixed(mul(taxExclusiveTransactionTotal, inputValue), 100);
            equivalentValue = orderManualDiscount;
        }
        discountItem.equivalentValue = equivalentValue;

        const { taxExclusiveDiscount } = distributeDiscountToItemsTaxExclusive(
            transaction,
            orderManualDiscount,
            DiscountTypeName.fullBill,
        );

        discountItem.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
        };
    }
}

export function calculateMaximumDiscountInputValue(transaction, isTaxInclusiveDisplay) {
    if (isTaxInclusiveDisplay) {
        transaction.maximumDiscountInputValue = transaction.total;
    } else {
        transaction.maximumDiscountInputValue = sub(transaction.total, transaction.tax);
    }
}

export default function calculateTransactionDiscount(
    discountItem,
    transaction,
    isTaxInclusiveDisplay,
) {
    if (isTaxInclusiveDisplay) {
        return calculateTransactionDiscountTaxInclusive(discountItem, transaction);
    }
    return calculateTransactionDiscountTaxExclusive(discountItem, transaction);
}
