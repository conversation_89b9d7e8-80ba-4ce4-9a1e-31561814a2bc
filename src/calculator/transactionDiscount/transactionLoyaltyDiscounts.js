import { DiscountTypeName, roundTo2DecimalPlaces, sub } from '../../utils';
import {
    distributeLoyaltyDiscountToItemsTaxExclusive,
    distributeLoyaltyDiscountToItemsTaxInclusive,
} from './distributor';

const MAXIMUM_ALLOWABLE_ERROR_VALUE = 10 ** -5;

function getMaximumLoyaltyDiscountInput(inputValue) {
    let maximumLoyaltyDiscountInput;
    const roundValue = roundTo2DecimalPlaces(inputValue);
    if (Math.abs(sub(roundValue, inputValue)) < MAXIMUM_ALLOWABLE_ERROR_VALUE) {
        maximumLoyaltyDiscountInput = roundValue;
    } else {
        maximumLoyaltyDiscountInput = Math.floor(inputValue * 100) / 100;
    }
    return maximumLoyaltyDiscountInput;
}

export default function calculateLoyaltyDiscount(transaction, isTaxInclusiveDisplay) {
    if (transaction.loyaltyDiscounts && transaction.loyaltyDiscounts.length > 0) {
        for (const loyaltyDiscount of transaction.loyaltyDiscounts) {
            calculateGeneralLoyaltyDiscount(loyaltyDiscount, transaction, isTaxInclusiveDisplay);
        }
    }
}

export function calculateGeneralLoyaltyDiscount(
    loyaltyDiscount,
    transaction,
    isTaxInclusiveDisplay,
) {
    if (isTaxInclusiveDisplay) {
        return calculateLoyaltyDiscountTaxInclusive(loyaltyDiscount, transaction);
    }
    return calculateLoyaltyDiscountTaxExclusive(loyaltyDiscount, transaction);
}

export function calculateLoyaltyDiscountTaxInclusive(loyaltyDiscount, transaction) {
    const inputValue = getMaximumLoyaltyDiscountInput(loyaltyDiscount.inputValue);
    const taxInclusiveTransactionTotal = transaction.total;

    if (!isNaN(inputValue) && inputValue >= 0) {
        const taxInclusiveDiscount = roundTo2DecimalPlaces(
            Math.min(inputValue, taxInclusiveTransactionTotal),
        );

        const {
            taxExclusiveDiscount,
            discountedTax,
        } = distributeLoyaltyDiscountToItemsTaxInclusive(
            transaction,
            taxInclusiveDiscount,
            DiscountTypeName.loyalty,
        );

        loyaltyDiscount.spentValue = roundTo2DecimalPlaces(taxInclusiveDiscount);
        loyaltyDiscount.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
            discountedTax: roundTo2DecimalPlaces(discountedTax),
        };
    }
}

export function calculateLoyaltyDiscountTaxExclusive(loyaltyDiscount, transaction) {
    const inputValue = getMaximumLoyaltyDiscountInput(loyaltyDiscount.inputValue);
    const taxInclusiveTransactionTotal = transaction.total;

    if (!isNaN(inputValue) && inputValue >= 0) {
        const taxInclusiveDiscount = roundTo2DecimalPlaces(
            Math.min(inputValue, taxInclusiveTransactionTotal),
        );

        const {
            taxExclusiveDiscount,
            discountedTax,
        } = distributeLoyaltyDiscountToItemsTaxExclusive(
            transaction,
            taxInclusiveDiscount,
            DiscountTypeName.loyalty,
        );

        loyaltyDiscount.spentValue = roundTo2DecimalPlaces(taxInclusiveDiscount);
        loyaltyDiscount.display = {
            discount: roundTo2DecimalPlaces(taxExclusiveDiscount),
            discountedTax: roundTo2DecimalPlaces(discountedTax),
        };
    }
}
