import {
    DiscountType,
    add,
    discountFieldName,
    discountFlagName,
    div,
    div2Fixed,
    getTaxRateCardinal,
    mul,
    roundTo2DecimalPlaces,
    sub,
} from '../../utils';
import calculateItem from '../item';
import {
    insertTrxCalculationDiscountWithTaxExclusive,
    insertTrxCalculationDiscount,
} from '../calculationFields';

import { insertItemCalculationDiscountDirectly } from '../item/itemCalculationFields';

// the inputDiscount is taxInclusiveDiscount
// the inputDiscount here is a tax inclusive value
export function distributeDiscountToItemsTaxInclusive(transaction, inputDiscount, discountType) {
    const taxInclusiveTransactionTotal = transaction.total;
    if (!taxInclusiveTransactionTotal) {
        return { taxExclusiveDiscount: 0, discountedTax: 0 };
    }

    let lastPurchaseItem;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            lastPurchaseItem = purchaseItem;
            break;
        }
    }
    if (lastPurchaseItem) {
        lastPurchaseItem.isLast = true;
    }

    let lastItemDiscount = inputDiscount;
    let lastItemDiscountRounded = inputDiscount;
    let totalTaxExclusiveDiscount = 0;
    let totalDiscountTax = 0;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType && !purchaseItem.isLast) {
            const taxRateCardinal = getTaxRateCardinal(purchaseItem);
            const taxInclusiveItemTotal = add(purchaseItem.total, purchaseItem.tax);
            const discountRatio = div(taxInclusiveItemTotal, taxInclusiveTransactionTotal);
            const taxInclusiveDiscount = mul(inputDiscount, discountRatio); // taxInclusive
            const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(taxInclusiveDiscount); // taxInclusive
            const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
            const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

            totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscount);
            totalDiscountTax = add(totalDiscountTax, discountTax);
            insertItemCalculationDiscountDirectly(
                purchaseItem,
                DiscountType.TRANSACTION_MANUAL_DISCOUNT,
                taxExclusiveDiscount,
                discountTax,
            );
            purchaseItem[discountFieldName[`${discountType}`]] = taxInclusiveDiscountRounded;
            purchaseItem[discountFlagName[`${discountType}`]] = true;
            lastItemDiscount = sub(lastItemDiscount, taxInclusiveDiscount);
            lastItemDiscountRounded = sub(lastItemDiscountRounded, taxInclusiveDiscountRounded);
            calculateItem(purchaseItem, true);
        }
    }

    if (lastPurchaseItem) {
        const taxRateCardinal = getTaxRateCardinal(lastPurchaseItem);
        const taxInclusiveDiscount = lastItemDiscount; // taxInclusive
        const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(lastItemDiscountRounded); // taxInclusive
        lastPurchaseItem[discountFieldName[`${discountType}`]] = taxInclusiveDiscountRounded;
        const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
        const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

        totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscount);
        totalDiscountTax = add(totalDiscountTax, discountTax);
        insertItemCalculationDiscountDirectly(
            lastPurchaseItem,
            DiscountType.TRANSACTION_MANUAL_DISCOUNT,
            taxExclusiveDiscount,
            discountTax,
        );
        lastPurchaseItem[discountFlagName[`${discountType}`]] = true;
        delete lastPurchaseItem.isLast;
        calculateItem(lastPurchaseItem, true);
    }

    insertTrxCalculationDiscount(
        transaction,
        DiscountType.TRANSACTION_MANUAL_DISCOUNT,
        totalTaxExclusiveDiscount,
        totalDiscountTax,
    );

    return {
        taxExclusiveDiscount: totalTaxExclusiveDiscount,
        discountedTax: totalDiscountTax,
    };
}

// the inputDiscount is taxExclusiveDiscount
export function distributeDiscountToItemsTaxExclusive(transaction, inputDiscount, discountType) {
    const taxExclusiveTransactionTotal = transaction.total - transaction.tax;

    let lastPurchaseItem;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            lastPurchaseItem = purchaseItem;
            break;
        }
    }
    if (lastPurchaseItem) {
        lastPurchaseItem.isLast = true;
    }

    let lastItemDiscount = inputDiscount;
    let totalDiscountTax = 0;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType && !purchaseItem.isLast) {
            const taxRateCardinal = getTaxRateCardinal(purchaseItem);
            const taxExclusiveItemTotal = purchaseItem.total;
            const discountRatio = div(taxExclusiveItemTotal, taxExclusiveTransactionTotal);
            const taxExclusiveDiscount = mul(inputDiscount, discountRatio); // taxExclusive
            const taxExclusiveDiscountRounded = roundTo2DecimalPlaces(taxExclusiveDiscount); // taxExclusive
            const taxInclusiveDiscount = mul(taxExclusiveDiscount, taxRateCardinal);
            const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);
            totalDiscountTax = add(totalDiscountTax, discountTax);
            insertItemCalculationDiscountDirectly(
                purchaseItem,
                DiscountType.TRANSACTION_MANUAL_DISCOUNT,
                taxExclusiveDiscount,
                discountTax,
            );
            purchaseItem[discountFieldName[`${discountType}`]] = taxExclusiveDiscountRounded;
            purchaseItem[discountFlagName[`${discountType}`]] = true;
            lastItemDiscount = sub(lastItemDiscount, taxExclusiveDiscountRounded);
            calculateItem(purchaseItem, false);
        }
    }

    if (lastPurchaseItem) {
        const taxRateCardinal = getTaxRateCardinal(lastPurchaseItem);
        const taxExclusiveDiscount = lastItemDiscount; // taxExclusive
        const taxExclusiveDiscountRounded = roundTo2DecimalPlaces(taxExclusiveDiscount); // taxExclusive
        const taxInclusiveDiscount = mul(taxExclusiveDiscount, taxRateCardinal);
        const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);
        totalDiscountTax = add(totalDiscountTax, discountTax);

        lastPurchaseItem[discountFieldName[`${discountType}`]] = taxExclusiveDiscountRounded;
        insertItemCalculationDiscountDirectly(
            lastPurchaseItem,
            DiscountType.TRANSACTION_MANUAL_DISCOUNT,
            taxExclusiveDiscount,
            discountTax,
        );
        lastPurchaseItem[discountFlagName[`${discountType}`]] = true;
        delete lastPurchaseItem.isLast;
        calculateItem(lastPurchaseItem, false);
    }

    insertTrxCalculationDiscount(
        transaction,
        DiscountType.TRANSACTION_MANUAL_DISCOUNT,
        inputDiscount,
        totalDiscountTax,
    );
    return { taxExclusiveDiscount: inputDiscount, discountedTax: totalDiscountTax };
}

// the inputDiscount is taxInclusiveDiscount
export function distributePromotionToItemsTaxInclusive(
    transaction,
    inputDiscount,
    appliedPromotion,
    discountType,
) {
    const { promotionId, type } = appliedPromotion;
    const taxInclusiveAppliedTransactionTotal =
        transaction.promotionAppliedSubtotalMap[`${promotionId}`];

    let lastPurchaseItem;
    for (const purchaseItem of transaction.items) {
        if (
            !purchaseItem.itemType &&
            purchaseItem.promotionAppliedQuantityMap &&
            purchaseItem.promotionAppliedQuantityMap[`${promotionId}`] > 0
        ) {
            lastPurchaseItem = purchaseItem;
            break;
        }
    }
    if (lastPurchaseItem) {
        lastPurchaseItem.isLast = true;
    }

    let lastItemDiscount = inputDiscount;
    let lastItemDiscountRounded = inputDiscount;
    let totalTaxExclusiveDiscount = 0;
    let totalDiscountTax = 0;
    for (const purchaseItem of transaction.items) {
        if (
            !purchaseItem.itemType &&
            !purchaseItem.isLast &&
            purchaseItem.promotionAppliedQuantityMap &&
            purchaseItem.promotionAppliedQuantityMap[`${promotionId}`] > 0
        ) {
            const taxRateCardinal = getTaxRateCardinal(purchaseItem);
            const taxInclusiveAppliedItemTotal =
                purchaseItem.promotionAppliedSubtotalMap[`${promotionId}`];
            const discountRatio = div(
                taxInclusiveAppliedItemTotal,
                taxInclusiveAppliedTransactionTotal,
            );
            const taxInclusiveDiscount = mul(inputDiscount, discountRatio); // taxInclusive
            const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(taxInclusiveDiscount); // taxInclusive
            const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
            const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

            totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscount);
            totalDiscountTax = add(totalDiscountTax, discountTax);
            insertItemCalculationDiscountDirectly(
                purchaseItem,
                DiscountType.PROMOTION,
                taxExclusiveDiscount,
                discountTax,
                type,
                promotionId,
            );
            addTypeDiscount(purchaseItem, discountType, taxInclusiveDiscountRounded);

            purchaseItem[discountFlagName[`${discountType}`]] = true;
            lastItemDiscount = sub(lastItemDiscount, taxInclusiveDiscount);
            lastItemDiscountRounded = sub(lastItemDiscountRounded, taxInclusiveDiscountRounded);

            calculateItem(purchaseItem, true);
            delete purchaseItem.promotionAppliedSubtotalMap;
        }
    }

    if (lastPurchaseItem) {
        const taxRateCardinal = getTaxRateCardinal(lastPurchaseItem);
        const taxInclusiveDiscount = lastItemDiscount;
        const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(lastItemDiscountRounded); // taxInclusive
        const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
        const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

        totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscount);
        totalDiscountTax = add(totalDiscountTax, discountTax);
        insertItemCalculationDiscountDirectly(
            lastPurchaseItem,
            DiscountType.PROMOTION,
            taxExclusiveDiscount,
            discountTax,
            type,
            promotionId,
        );
        addTypeDiscount(lastPurchaseItem, discountType, taxInclusiveDiscountRounded);
        lastPurchaseItem[discountFlagName[`${discountType}`]] = true;
        delete lastPurchaseItem.isLast;

        calculateItem(lastPurchaseItem, true);
        delete lastPurchaseItem.promotionAppliedSubtotalMap;
        delete transaction.promotionAppliedSubtotalMap;
    }

    insertTrxCalculationDiscountWithTaxExclusive(
        transaction,
        DiscountType.PROMOTION,
        totalTaxExclusiveDiscount,
        totalDiscountTax,
        type,
        promotionId,
    );

    return {
        taxExclusiveDiscount: totalTaxExclusiveDiscount,
        discountedTax: totalDiscountTax,
    };
}

// the inputDiscount is taxInclusiveDiscount
export function distributePromotionToItemsTaxExclusive(
    transaction,
    inputDiscount,
    appliedPromotion,
    discountType,
) {
    const { promotionId, type } = appliedPromotion;

    const taxExclusiveAppliedTransactionTotal =
        transaction.promotionAppliedSubtotalMap[`${promotionId}`];
    let lastPurchaseItem;
    for (const purchaseItem of transaction.items) {
        if (
            !purchaseItem.itemType &&
            purchaseItem.promotionAppliedQuantityMap &&
            purchaseItem.promotionAppliedQuantityMap[`${promotionId}`] > 0
        ) {
            lastPurchaseItem = purchaseItem;
            break;
        }
    }
    if (lastPurchaseItem) {
        lastPurchaseItem.isLast = true;
    }

    let lastItemDiscount = inputDiscount;
    let totalDiscountTax = 0;
    for (const purchaseItem of transaction.items) {
        if (
            !purchaseItem.itemType &&
            !purchaseItem.isLast &&
            purchaseItem.promotionAppliedQuantityMap &&
            purchaseItem.promotionAppliedQuantityMap[`${promotionId}`] > 0
        ) {
            const taxRateCardinal = getTaxRateCardinal(purchaseItem);
            const taxExclusiveAppliedItemTotal =
                purchaseItem.promotionAppliedSubtotalMap[`${promotionId}`];
            const discountRatio = div(
                taxExclusiveAppliedItemTotal,
                taxExclusiveAppliedTransactionTotal,
            );
            const taxExclusiveDiscount = mul(inputDiscount, discountRatio); // taxExclusive
            const taxExclusiveDiscountRounded = roundTo2DecimalPlaces(taxExclusiveDiscount); // taxExclusive
            const taxInclusiveDiscount = mul(taxExclusiveDiscount, taxRateCardinal);
            const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);
            totalDiscountTax = add(totalDiscountTax, discountTax);
            insertItemCalculationDiscountDirectly(
                purchaseItem,
                DiscountType.PROMOTION,
                taxExclusiveDiscount,
                discountTax,
                type,
                promotionId,
            );
            addTypeDiscount(purchaseItem, discountType, taxExclusiveDiscountRounded);

            purchaseItem[discountFlagName[`${discountType}`]] = true;
            lastItemDiscount = sub(lastItemDiscount, taxExclusiveDiscountRounded);
            calculateItem(purchaseItem, false);
            delete purchaseItem.promotionAppliedSubtotalMap;
        }
    }

    if (lastPurchaseItem) {
        const taxRateCardinal = getTaxRateCardinal(lastPurchaseItem);
        const taxExclusiveDiscount = lastItemDiscount;
        const taxExclusiveDiscountRounded = roundTo2DecimalPlaces(taxExclusiveDiscount);
        const taxInclusiveDiscount = mul(taxExclusiveDiscount, taxRateCardinal);
        const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);
        totalDiscountTax = add(totalDiscountTax, discountTax);

        insertItemCalculationDiscountDirectly(
            lastPurchaseItem,
            DiscountType.PROMOTION,
            taxExclusiveDiscount,
            discountTax,
            type,
            promotionId,
        );
        addTypeDiscount(lastPurchaseItem, discountType, taxExclusiveDiscountRounded);
        lastPurchaseItem[discountFlagName[`${discountType}`]] = true;
        delete lastPurchaseItem.isLast;

        calculateItem(lastPurchaseItem, false);
        delete lastPurchaseItem.promotionAppliedSubtotalMap;
        delete transaction.promotionAppliedSubtotalMap;
    }
    insertTrxCalculationDiscountWithTaxExclusive(
        transaction,
        DiscountType.PROMOTION,
        inputDiscount,
        totalDiscountTax,
        type,
        promotionId,
    );
    return { taxExclusiveDiscount: inputDiscount, discountedTax: totalDiscountTax };
}

function addTypeDiscount(purchaseItem, discountType, discountValue) {
    if (purchaseItem[discountFieldName[`${discountType}`]]) {
        purchaseItem[discountFieldName[`${discountType}`]] += discountValue;
    } else {
        purchaseItem[discountFieldName[`${discountType}`]] = discountValue;
    }
}

// the inputDiscount is taxInclusiveDiscount
export function distributeLoyaltyDiscountToItemsTaxInclusive(
    transaction,
    inputDiscount,
    discountType,
) {
    const taxInclusiveTransactionTotal = transaction.total;
    if (!taxInclusiveTransactionTotal) {
        return { taxExclusiveDiscount: 0, discountedTax: 0 };
    }

    let lastPurchaseItem;

    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            lastPurchaseItem = purchaseItem;
            break;
        }
    }
    if (!lastPurchaseItem) return { taxExclusiveDiscount: 0, discountedTax: 0 };
    lastPurchaseItem.isLast = true;

    let lastItemDiscount = inputDiscount;
    let lastItemDiscountRounded = inputDiscount;
    let totalTaxExclusiveDiscount = 0;
    let totalDiscountTax = 0;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType && !purchaseItem.isLast) {
            const taxRateCardinal = getTaxRateCardinal(purchaseItem);
            const taxInclusiveItemTotal = add(purchaseItem.total, purchaseItem.tax);
            const discountRatio = div(taxInclusiveItemTotal, taxInclusiveTransactionTotal);
            const taxInclusiveDiscount = mul(inputDiscount, discountRatio); // taxInclusive
            const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(taxInclusiveDiscount); // taxInclusive
            const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
            const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

            totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscount);
            totalDiscountTax = add(totalDiscountTax, discountTax);
            insertItemCalculationDiscountDirectly(
                purchaseItem,
                DiscountType.LOYALTY_DISCOUNT,
                taxExclusiveDiscount,
                discountTax,
            );
            purchaseItem[discountFieldName[`${discountType}`]] = taxInclusiveDiscountRounded;
            purchaseItem[discountFlagName[`${discountType}`]] = true;
            lastItemDiscount = sub(lastItemDiscount, taxInclusiveDiscount);
            lastItemDiscountRounded = sub(lastItemDiscountRounded, taxInclusiveDiscountRounded);
            calculateItem(purchaseItem, true);
        }
    }

    // last Purchase Item
    const taxRateCardinal = getTaxRateCardinal(lastPurchaseItem);
    const taxInclusiveDiscount = lastItemDiscount;
    const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(lastItemDiscountRounded);
    const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
    const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

    totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscount);
    totalDiscountTax = add(totalDiscountTax, discountTax);
    insertItemCalculationDiscountDirectly(
        lastPurchaseItem,
        DiscountType.LOYALTY_DISCOUNT,
        taxExclusiveDiscount,
        discountTax,
    );
    lastPurchaseItem[discountFieldName[`${discountType}`]] = taxInclusiveDiscountRounded;

    lastPurchaseItem[discountFlagName[`${discountType}`]] = true;
    delete lastPurchaseItem.isLast;
    calculateItem(lastPurchaseItem, true);

    insertTrxCalculationDiscount(
        transaction,
        DiscountType.LOYALTY_DISCOUNT,
        totalTaxExclusiveDiscount,
        totalDiscountTax,
    );

    return {
        taxExclusiveDiscount: totalTaxExclusiveDiscount,
        discountedTax: totalDiscountTax,
    };
}

// the inputDiscount is taxExclusiveDiscount
export function distributeLoyaltyDiscountToItemsTaxExclusive(
    transaction,
    inputDiscount,
    discountType,
) {
    const taxInclusiveTransactionTotal = transaction.total;
    if (!taxInclusiveTransactionTotal) {
        return { taxExclusiveDiscount: 0, discountedTax: 0 };
    }

    let lastPurchaseItem;

    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            lastPurchaseItem = purchaseItem;
            break;
        }
    }
    if (!lastPurchaseItem) return { taxExclusiveDiscount: 0, discountedTax: 0 };
    lastPurchaseItem.isLast = true;

    let lastItemDiscount = inputDiscount;
    let lastItemDiscountRounded = inputDiscount;
    let totalTaxExclusiveDiscount = 0;
    let totalDiscountTax = 0;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType && !purchaseItem.isLast) {
            const taxRateCardinal = getTaxRateCardinal(purchaseItem);
            const taxInclusiveItemTotal = add(purchaseItem.total, purchaseItem.tax);
            const discountRatio = div(taxInclusiveItemTotal, taxInclusiveTransactionTotal);
            const taxInclusiveDiscount = mul(inputDiscount, discountRatio); // taxInclusive
            const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(taxInclusiveDiscount); // taxInclusive
            const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
            const taxExclusiveDiscountRounded = roundTo2DecimalPlaces(taxExclusiveDiscount); // taxExclusive
            const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

            totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscountRounded);
            totalDiscountTax = add(totalDiscountTax, discountTax);
            insertItemCalculationDiscountDirectly(
                purchaseItem,
                DiscountType.LOYALTY_DISCOUNT,
                taxExclusiveDiscountRounded,
                discountTax,
            );
            purchaseItem[discountFieldName[`${discountType}`]] = taxExclusiveDiscountRounded;
            purchaseItem[discountFlagName[`${discountType}`]] = true;
            lastItemDiscount = sub(lastItemDiscount, taxInclusiveDiscount);
            lastItemDiscountRounded = sub(lastItemDiscountRounded, taxInclusiveDiscountRounded);
            calculateItem(purchaseItem, false);
        }
    }

    // last Purchase Item
    const taxRateCardinal = getTaxRateCardinal(lastPurchaseItem);
    const taxInclusiveDiscount = lastItemDiscount;
    const taxInclusiveDiscountRounded = roundTo2DecimalPlaces(lastItemDiscountRounded);
    const taxExclusiveDiscount = div(taxInclusiveDiscount, taxRateCardinal); // taxExclusive
    const taxExclusiveDiscountRounded = div2Fixed(taxInclusiveDiscountRounded, taxRateCardinal); // taxExclusive
    const discountTax = sub(taxInclusiveDiscount, taxExclusiveDiscount);

    totalTaxExclusiveDiscount = add(totalTaxExclusiveDiscount, taxExclusiveDiscountRounded);
    totalDiscountTax = add(totalDiscountTax, discountTax);
    insertItemCalculationDiscountDirectly(
        lastPurchaseItem,
        DiscountType.LOYALTY_DISCOUNT,
        taxExclusiveDiscount,
        discountTax,
    );
    lastPurchaseItem[discountFieldName[`${discountType}`]] = taxExclusiveDiscountRounded;

    lastPurchaseItem[discountFlagName[`${discountType}`]] = true;
    delete lastPurchaseItem.isLast;
    calculateItem(lastPurchaseItem, false);

    insertTrxCalculationDiscount(
        transaction,
        DiscountType.LOYALTY_DISCOUNT,
        totalTaxExclusiveDiscount,
        totalDiscountTax,
    );

    return {
        taxExclusiveDiscount: totalTaxExclusiveDiscount,
        discountedTax: totalDiscountTax,
    };
}
