import { get } from 'lodash';
import { roundTo2DecimalPlaces, BIRDiscountType } from '../utils';
import calculateItem from './item';

const BirRetailScPwdMaxDiscount = 65;

export default function calculateBir(transaction, isTaxInclusiveDisplay) {
    if (transaction.birInfo) {
        const { type, discountType } = transaction.birInfo;
        switch (discountType) {
            case BIRDiscountType.SC_PWD:
                if (type === 'retail') {
                    calculateRetailScPwdDiscount(transaction, isTaxInclusiveDisplay);
                } else {
                    calculateItemsBirFields(transaction, isTaxInclusiveDisplay);
                }
                break;
            case BIRDiscountType.ATHLETE_AND_COACH:
            case BIRDiscountType.MEDAL_OF_VALOR:
            case BIRDiscountType.DIPLOMAT:
                calculateItemsBirFields(transaction, isTaxInclusiveDisplay);
                break;
            case BIRDiscountType.SOLO_PARENT:
                calculateRetailSoloParentDiscount(transaction, isTaxInclusiveDisplay);
                break;

            default:
                calculateItemsBirFields(transaction, isTaxInclusiveDisplay);
                break;
        }
    }
}

// The fields required by the BIR in most cases except Retail(SC/PWD) are calculated here,
// including if there is no BIR Discount
export function calculateItemsBirFields(transaction, isTaxInclusiveDisplay) {
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType || purchaseItem.itemType === 'ServiceCharge') {
            delete purchaseItem.display;
            purchaseItem.birInfo = { ...transaction.birInfo, expectedBIRDiscount: 0 };
            calculateItem(purchaseItem, isTaxInclusiveDisplay, false);
        }
    }
}

export function checkBirDiscountAvailable(transaction) {
    const birDiscountType = get(transaction, ['birInfo', 'discountType']) || '';
    let isBirDiscountAvailable = Boolean(birDiscountType);
    if (birDiscountType === 'SC/PWD') {
        isBirDiscountAvailable = isScPwdBirDiscountAvailable(transaction);
    }
    transaction.isBirDiscountAvailable = isBirDiscountAvailable;
    // in case you need to record this.
    // the issue about how to handle if retail merchant trying to add SC/PWD to an order when the SC/PWD discount condition is not met yet.
    // currently we allow this to happen. There's a potential of the order to receive promotion (auto applied) with higher discount prior to the order meeting SC/PWD discount criteria (4 items or above). and later when the order has 4 items, the promotional discount will be removed, and apply only SC/PWD (cap at PHP70)
    // We can continue with the current behavior for now because this is an edge case with a specific flow.
    if (isBirDiscountAvailable) {
        delete transaction.promotions;
        delete transaction.loyaltyDiscounts;
        for (let i = 0; i < transaction.items.length; i++) {
            const item = transaction.items[i];
            if (!Boolean(item.itemType)) {
                delete item.itemLevelDiscount;
                delete item.promotions;
                delete item.promotionAppliedQuantityMap;
            } else if (item.itemType === 'Discount') {
                item.inputValue = 0;
                item.taxRate = null;
            }
        }
    }
}

export function isScPwdBirDiscountAvailable(transaction) {
    const birType = get(transaction, ['birInfo', 'type']);
    const seniorsCount = get(transaction, ['birInfo', 'seniorsCount'], 0);
    const pwdCount = get(transaction, ['birInfo', 'pwdCount'], 0);
    const items = get(transaction, 'items', []);

    if (birType === 'retail') {
        let retailItemsCount = 0;
        for (const purchaseItem of items) {
            if (!purchaseItem.itemType && purchaseItem.isBasicNecessitiesPH) {
                retailItemsCount += 1;
            }
        }
        // if want to apply retail SC/PWD discount, Consumer need to purchase at least 4 kinds of necessities
        return (seniorsCount > 0 || pwdCount > 0) && retailItemsCount >= 4;
    }

    return seniorsCount > 0 || pwdCount > 0;
}

export function calculateRetailScPwdDiscount(transaction, isTaxInclusiveDisplay) {
    // Senior citizen, Person with disabilities (SC/PWD)
    // Cap at 5% of PHP1300 (tax exclusive) a week: PHP65
    // Each person can only enjoy this once a week. But our POS will not validate identification of a customer hence our check is only max of PHP65 discount per transaction.
    let taxExclusiveRetailTotal = 0;
    let retailItemsCount = 0;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType && purchaseItem.isBasicNecessitiesPH) {
            retailItemsCount += 1;
            taxExclusiveRetailTotal += purchaseItem.total;
        }
    }
    // Consumer need to purchase at least 4 kinds of necessities
    if (
        retailItemsCount < 4 ||
        (transaction.birInfo.seniorsCount === 0 && transaction.birInfo.pwdCount === 0)
    ) {
        calculateItemsBirFields(transaction, isTaxInclusiveDisplay);
    } else {
        const retailTotalDiscount = Math.min(
            BirRetailScPwdMaxDiscount,
            roundTo2DecimalPlaces(taxExclusiveRetailTotal * transaction.birInfo.discountRate),
        );
        let indexTag = 0;
        let totalDiscount = 0;
        for (const purchaseItem of transaction.items) {
            if (purchaseItem.itemType) {
                if (purchaseItem.itemType === 'ServiceCharge') {
                    delete purchaseItem.display;
                    // PH has no service charge tax
                    // service charge
                    purchaseItem.birInfo = {
                        ...transaction.birInfo,
                        expectedBIRDiscount: retailTotalDiscount * purchaseItem.rate,
                    };
                    calculateItem(purchaseItem, isTaxInclusiveDisplay, false);
                }
                // eslint-disable-next-line no-continue
                continue;
            }

            delete purchaseItem.display;
            if (purchaseItem.isBasicNecessitiesPH) {
                indexTag += 1;
                const taxExclusiveTotal = purchaseItem.total;
                const expectedBIRDiscount =
                    indexTag < retailItemsCount
                        ? roundTo2DecimalPlaces(
                              (taxExclusiveTotal / taxExclusiveRetailTotal) * retailTotalDiscount,
                          )
                        : roundTo2DecimalPlaces(retailTotalDiscount - totalDiscount);
                purchaseItem.birInfo = { ...transaction.birInfo, expectedBIRDiscount };
                totalDiscount += expectedBIRDiscount;
            } else {
                purchaseItem.birInfo = { ...transaction.birInfo, expectedBIRDiscount: 0 };
            }
            calculateItem(purchaseItem, isTaxInclusiveDisplay, false);
        }
    }
}

// The Solo Parent  Discount only applies to products with solo parent discount enabled
// so the service charge item should be calculated separately
export function calculateRetailSoloParentDiscount(transaction, isTaxInclusiveDisplay) {
    const { type } = transaction.birInfo;
    let serviceChargeItem;
    let totalBIRDiscount = 0;
    for (const purchaseItem of transaction.items) {
        if (!purchaseItem.itemType) {
            delete purchaseItem.display;
            purchaseItem.birInfo = { ...transaction.birInfo, expectedBIRDiscount: 0 };
            if (!purchaseItem.isSoloParentDiscountApplicable || type !== 'retail') {
                purchaseItem.birInfo.discountType = '';
            }
            calculateItem(purchaseItem, isTaxInclusiveDisplay, false);
            totalBIRDiscount += purchaseItem.soloParentDiscount || 0;
        } else if (purchaseItem.itemType === 'ServiceCharge') {
            serviceChargeItem = purchaseItem;
        }
    }
    if (serviceChargeItem) {
        delete serviceChargeItem.display;
        serviceChargeItem.birInfo = {
            ...transaction.birInfo,
            expectedBIRDiscount: totalBIRDiscount * serviceChargeItem.rate,
        };
        calculateItem(serviceChargeItem, isTaxInclusiveDisplay, false);
    }
}
