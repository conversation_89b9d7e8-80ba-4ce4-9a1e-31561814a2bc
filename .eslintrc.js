/**
 * Copyright © 2016-present Kriasoft.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.txt file in the root directory of this source tree.
 */

const fs = require('fs');

const prettierOptions = JSON.parse(fs.readFileSync('./.prettierrc', 'utf8'));

// http://eslint.org/docs/user-guide/configuring
// https://github.com/prettier/prettier#eslint
module.exports = {
    parser: 'babel-eslint',
    extends: ['airbnb-base', 'plugin:prettier/recommended', 'plugin:security/recommended'],
    plugins: ['prettier', 'security'],
    env: {
        jest: true,
        es6: true,
    },
    rules: {
        'prettier/prettier': ['error', prettierOptions],
        'no-underscore-dangle': ['error', { allow: ['_id'] }],
        'no-param-reassign': ['error', { props: false }],
        'no-restricted-syntax': ['error', 'ForInStatement'],
        'no-use-before-define': ['error', { functions: false }],
        'no-lonely-if': 'off',
        'prefer-destructuring': ['error', { VariableDeclarator: { object: false } }],
        'import/prefer-default-export': 'off',
        'no-restricted-globals': ['error', 'isnan'],
        'import/no-named-as-default-member': 'off'
    },
};
