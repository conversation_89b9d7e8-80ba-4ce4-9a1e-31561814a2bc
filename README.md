# calculator-lib
The core transaction calculation module of Storehub POS
## Documentation
[Transaction Calculation](https://storehub.atlassian.net/wiki/spaces/TS/pages/62554121/Transaction+Calculation)
## Setup
* `npm install npm-cli-login -g`
* `npm config set registry https://npm-proxy.fury.io/byP2nuz4Las5rf72y98D/storehub/ && npm config set ca ""`
* `npm-cli-login -u storehub -e <EMAIL> -p "a[RF8zZy" -r https://npm-proxy.fury.io/byP2nuz4Las5rf72y98D/storehub/`
* `npm i calculator-lib --save` or `yarn add calculator-lib`
## How to use
```ts
// calculate
calculate(transaction: Object, isTaxInclusiveDisplay: boolean): Object;

// receipt, countryCode support 'MY', 'TH'
receipt(transaction: Object, isTaxInclusiveDisplay: boolean, countryCode: string, hasDiscountColumn:boolean): Object;
```
```js
import { calculate } from 'calculator-lib'
const transaction = {...}

const calculatedTransaction = calculate(transaction, true);
or
calculate(transaction, true);
const calculatedTransaction = transaction;
```

```js
import { receipt } from 'calculator-lib'
const calculatedTransaction = {...} // It's from calculate() or read from db

const receiptTransaction = receipt(calculatedTransaction, true, 'MY');
or
receipt(calculatedTransaction, true, 'MY',true);
const receiptTransaction = calculatedTransaction;
```
## Transaction input structure
Only include calculation needed fields, other fields you passed in will return back to you.
When in refund mode, calculation will based on saved transaction, should pass calculationMode
in transaction and refund object in items.
```js
transaction = {
    calculationMode: optional 'Sale' || 'Refund',
    items: required: [
        {
            unitPrice: required,
            selectedOptions: optional: [
                {
                    optionValue: required,
                    quantity: required >= 1
                }
            ],
            isTakeaway: optional,
            quantity: required > 0,
            isVatExempted: optional,
            taxRate: optional,
            itemLevelDiscount: optional: {
                inputValue: required,
                type: required: 'amount' || 'percent'
            },
            promotions: optional: [
                {
                    inputValue: required,
                    type: required: 'percentage' || 'fixedUnitPrice' || 'buyXFreeY'
                }
            ],
            promotionAppliedQuantityMap: optional: { //sum of quantity <= item.quantity
                '<promotionId>': '<quantity>'
            },
            return: optional: {
                quantity: required,
                total: optional
            }
            isServiceChargeNotApplicable: optional,
            excludeServiceChargeProductCondition: optional: { // only on service charge item
                    isEnabled: "true" | "false", // indicate the checkbox state
                    conditionsMatch: "Any" | "All",
                    conditions: Array<{
                        type: String, // values for "tags", "categories", "productIds"
                        operator: "in",
                        operand: Array<ProductId | Tag | Category>,
                    }>
                }
        },
        {
            itemType: required: 'Discount',
            inputValue: required,
            taxRate: required, // taxRate should be same as all current product items
            type: required: 'amount' || 'percent'
        },
        {
            itemType: required: 'ServiceCharge',
            rate: required,
            taxRate: optional
        }
    ],
    promotions: optional: [
        {
            promotionId: required when type is 'combo',
            inputValue: required except type is 'freeShipping',,
            // combo promotion have the highest priority,
            // not able to work with item level discount,
            // not able to work with any other promotions except freeShipping
            type: required: 'absolute' || 'combo' || 'freeShipping',
            quantity: required when type is 'combo',
            taxRate: required when taxInclusiveDisplay,
        }
    ],
    loyaltyDiscounts: optional: [
        {
            type: required: 'storeCredit' || 'cashback',
            inputValue: required,
            taxRate: required,
        }
    ],
    shippingFee: optional,
    takeAwayCharge: optional,
    birInfo: {
        type: required: 'retail' || 'f&b',
        discountType: optional: 'SC/PWD' || 'ATHLETE_AND_COACH' || 'MEDAL_OF_VALOR' || 'DIPLOMAT'
        discountRate: required, // default to 0.2 for f&b
        seniorsCount: optional, // Required for f&b. If there's pwdCount, able to be 0
        pwdCount: optional, // Required for f&b. If there's seniorsCount, able to be 0
        headCount: optional // Required for f&b. At least 1
        collectedInfo: optional: {name: String,pnstmId: String} || {name: String,movId: String} || {name: String,dfaOrVicId: String,address: String}
    }
}
```
## Transaction output structure
Only include calculated fields, other fields you passed in will return back to you.
```js
transaction = {
    items: [
        {
            promotions: [
                {
                    discount,
                    inputValue,
                    quantity,
                    type,
                    discountType,
                    originalDiscountType,
                    promotionId,
                    promotionCode,
                    promotionName,
                    taxCode,
                    storehubPaidPercentage,
                    promotionType,
                    maxDiscountAmount,
                    display: {
                        discount,
                    },
                }
            ],
            calculation:{  // add-on in 3.8.13
                fullPrice, 
                discounts:[{
                    type,  // loyalty, Item Discount, Fullbill, BIR, Promotion
                    subType,  // for BIR / Loyalty
                    discount,
                    deductedTax,
                    promotionId,
                }], 
                original:{
                    tax,
                    subtotal,
                    total // subtotal + tax
                }
            },
            subtotal,
            taxInclusiveSubtotal,
            taxExclusiveSubtotal,
            adhocDiscount,
            fullBillDiscount,
            loyaltyDiscount,
            seniorDiscount,
            pwdDiscount,
            discount,
            tax,
            isVatExempted,
            taxExemptAmount,
            taxableAmount,
            totalDeductedTax,
            zeroRatedSales
            total,
            isServiceChargeNotApplicable,
            display: {
                total,
                subtotal,
                tax
            },
            receipt: {
                price,
                qty,
                total,
                a4Total, // add-on in 3.8.13
                discount,  // add-on in 3.8.13
                discountWithoutPromo  // add-on in 3.8.13
            }
        },
        {
            itemType: 'ServiceCharge',
            unitPrice,
            subtotal,
            seniorDiscount,
            pwdDiscount,
            discount,
            taxExemptAmount,
            taxableAmount,
            totalDeductedTax,
            zeroRatedSales
            tax,
            total,
            excludeServiceChargeProductCondition:{
                    isEnabled, // "true" | "false"
                    conditionsMatch, //  "Any" | "All"
                    conditions: [{
                        type, // values for "tags", "categories", "productIds"
                        operator, // "in"
                        operand // Array<ProductId | Tag | Category>,
                    }]
                }
        }
    ],
    promotions: [
        {
            inputValue,
            type,
            discountType,
            originalDiscountType,
            promotionId:,
            promotionCode,
            promotionName,
            taxCode,
            taxRate,
            storehubPaidPercentage,
            promotionType,
            maxDiscountAmount,
            uniquePromotionCodeId,
            discount,
            display: {
                discount,
            },
        }
    ],
    loyaltyDiscounts: [
        {
            spentValue,
            display: {
                discount,
                discountedTax
            },
        }
    ],
    addonBirCompliance: {
        discountType,
        athleteAndCoachDiscount,
        medalOfValorDiscount,
        collectedInfo
    },
    calculation:{  // add-on in 3.8.13
        discounts:[{
            type,   // loyalty, Item Discount, Fullbill, BIR, Promotion
            subType,  // for BIR / Loyalty
            discount,
            deductedTax,
            promotionId
        }], 
        original:{
            tax,
            subtotal,
            total
        }
    },
    serviceCharge,
    serviceChargeTax,
    subtotal,
    seniorDiscount,
    pwdDiscount,
    discount,
    taxableSales,
    taxExemptedSales,
    zeroRatedSales,
    totalDeductedTax,
    tax,
    shippingFee,
    shippingFeeDiscount,
    takeawayCharges,
    fixedFee,
    total,
    display: {
        subtotal,
        discount,
        serviceCharge,
        tax,
        total
    },
    receipt: {
        receiptTotalExcTax,
        discount,
        serviceCharge,
        tax,
        total,
        takeawayCharges  // add-on in 3.8.13
    }
}
```

## applyPromotion
**Usage**

``` js
calculator.applyPromotion(transaction: Object, promotions: Object[], businessSettings: Object)
```

**Input argument structure**
``` js
transaction = {
    total: : { type: Number },
    items: [
        {
            id: { type: String }
            unitPrice: { type: Number }
            quantity: { type: Number }
            productId: { type: String }
            taxRate: { type: String }
            taxCode: { type: String }
            product: {
                category: { type: String }
                tags: { type: [String] }
                _id: { type: String }
            }
        }
    ]
    storeId: { type: String }
    customer: {
        tags: { type: [String] }
        hasFirstPurchaseEcommerce: { type: [Boolean] }
        hasFirstPurchaseBeep: { type: [Boolean] }
        hasFirstPurchasePOS: { type: [Boolean] }
        hasUniversalFirstPurchase: { type: [Boolean] }
    }
}

promotion = {
    business: {
        type: String
    },
    validFrom: {
        type: Date
    },
    validTo: {
        type: Date
    },
    // From 1 to 7 to represent 7 weekdays, 1 is Monday and 7 is Sunday, empty array means valid on all days
    validDays: {
        type: [Number]
    },
    // 0 to 23, empty means valid for all day
    validTimeFrom: {
        type: Number
    },
    // 1 to 24, empty means till end of the day
    validTimeTo: {
        type: Number
    },
    appliedStores: {
        type: [String]
    },
    isEnabled: {
        type: Boolean
    },
    isDeleted: {
        type: Boolean
    },
    discountType: {
        type: String,
    },
    discountValue: {
        type: Number,
    },

    conditions: [
        {
            entity: {
                type: String
            },

            propertyName: {
                type: String
            },
            operator: {
                type: String
            },
            operand: {
                type: [String]
            },
            minQuantity: {
                type: Number
            },
            maxQuantity: {
                type: Number
            }
        }
    ],
    minQuantity: {
        type: Number
    },
    maxQuantity: {
        type: Number
    },
    requiredProducts: [
         {
            entity: {
                type: String
            },

            propertyName: {
                type: String
            },
            operator: {
                type: String
            },
            operand: {
                type: [String]
            },
            minQuantity: {
                type: Number
            },
            maxQuantity: {
                type: Number
            }
        }
    ],
    // Deprecated. use appliedSources
    applyToOnlineStore: {
        type: Boolean
    },
    appliedSources: {
        type: Number[]
    },
    promotionCode: {
        type: String,
        require: false,
    },
    name: {
        type: String,
        require: false,
    },

    // this field is required by take amount off and combo
    taxCode: {
        type: String,
        require: false,
    },

    // this field is required by combo
    taxRate: {
        type: Number,
        require: false,
    }

}

businessSettings = {
    name: { type: String },
    timezone: { type: String },
    includingTaxInDisplay: { type: Boolean },
    // if using unversal promotion code, needed properties
    country: { type: String },
    haveBeepFreeShippingZone: { type: Boolean },
    fulfillmentOptions: { type: String[] },
    enableCashback: { type: Boolean },
    planId: { type: String },
    addonIds: { type: String[] },
    qrOrderingSettings: { 
        searchTags: { type: String [] },
        marketingTags: { type: String [] },
    },
    businessPromotionClaimedCountMap: {
        type: Object<string, number>
    }
}
```
**Output structure**
``` js
transaction = {
    ...
    items: [
        {
            ...
            promotionAppliedQuantityMap: { '<promotionId>': <quantity> }
            promotions: [
                {
                    inputValue: { type: Number }
                    type: { type: String },
                    promotionId: { type: String },
                    promotionName: { type: String },
                    promotionCode: { type: String },
                    discountType: { type: String },
                }
            ],
        }
    ],

    promotions: [
        {
            inputValue: { type: Number }
            type: { type: String },
            promotionId: { type: String },
            promotionName: { type: String },
            promotionCode: { type: String },
            discountType: { type: String },
            quantity: { type: Number },
            taxCode:  { type: String },
        }
    ],

}
```
if any error occurred during applying, function will return an error array
if multitple promotions applied, partial success will also return error for the promotions that failed
___
| code        | message     |
| ----------- | ----------- |
| 4401        | not_enable        |
| 4402        | not_online        |
| 4403        | date_not_satisfied        |
| 4404        | weekday_not_satisfied        |
| 4405        | time_not_satisfied        |
| 4406        | required_products_not_satisfied     |
| 4407        | conditions_not_satisfied     |
| 4408        | require_same_business     |
| 4409        | store_not_satisfied     |
| 4410        | deleted_promotion     |
| 4411        | reach_max_claim_count     |
| 4412        | require_customer     |
| 4413        | reach_customer_claim_count_limit     |
| 4414        | require_first_time_purchase     |
| 4415        | require_source     |
| 4416        | source_not_satisfied     |
| 5500        | invalid_param < parameter name >     |
___

## checkConditionSatisfied
**Usage**

``` js
calculator.checkConditionSatisfied(condition: Object, ...args): Boolean
```

## Version
3.8.12
