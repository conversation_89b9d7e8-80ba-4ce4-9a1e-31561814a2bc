import calculateServiceCharge from '../src/calculator/serviceChargeItem';
import calculateTransactionDiscount from '../src/calculator/transactionDiscount/transactionDiscount';
import calculateItem from '../src/calculator/item';
import calculateItemDiscount from '../src/calculator/item/itemDiscount';
import calculateItemPromotions from '../src/calculator/item/itemPromotions';
import calculateTransactionPromotion from '../src/calculator/transactionDiscount/transactionPromotions';
import calculateBirFields from '../src/calculator/item/specials/birFields';
import calculateLoyaltyDiscount from '../src/calculator/transactionDiscount/transactionLoyaltyDiscounts';
import calculate from '../src/calculator';

describe('calculator:function:jest', () => {
    test('Func:calculateServiceChargeTaxInclusive', () => {
        const serviceChargeItem = {
            itemType: 'ServiceCharge',
            rate: 0.1,
            taxRate: 0.12,
        };

        calculateServiceCharge(
            serviceChargeItem,
            {
                subtotal: 20,
                discount: 0,
                items: [
                    {
                        itemType: '',
                        isTakeaway: false,
                        subTotal: 20,
                        discount: 0,
                    },
                ],
            },
            true,
        );

        expect(serviceChargeItem).toMatchSnapshot();
    });

    test('Func:calculateServiceChargeTaxInclusive, with takeaway items', () => {
        const serviceChargeItem = {
            itemType: 'ServiceCharge',
            rate: 0.1,
            taxRate: 0.12,
        };

        calculateServiceCharge(
            serviceChargeItem,
            {
                subtotal: 40,
                discount: 0,
                items: [
                    {
                        itemType: '',
                        isTakeaway: false,
                        subTotal: 20,
                        discount: 0,
                    },
                    {
                        itemType: '',
                        isTakeaway: true,
                        subTotal: 20,
                        discount: 0,
                    },
                ],
            },
            true,
        );

        expect(serviceChargeItem).toMatchSnapshot();
    });

    test('Func:calculateServiceChargeTaxInclusive, without items', () => {
        const serviceChargeItem = {
            itemType: 'ServiceCharge',
            rate: 0.1,
            taxRate: 0.12,
        };

        calculateServiceCharge(
            serviceChargeItem,
            {
                subtotal: 40,
                discount: 0,
            },
            true,
        );

        expect(serviceChargeItem).toMatchSnapshot();
    });

    test('Func:calculateServiceChargeTaxInclusive, with items but it is not array', () => {
        const serviceChargeItem = {
            itemType: 'ServiceCharge',
            rate: 0.1,
            taxRate: 0.12,
        };

        calculateServiceCharge(
            serviceChargeItem,
            {
                subtotal: 40,
                discount: 0,
                items: 1,
            },
            true,
        );

        expect(serviceChargeItem).toMatchSnapshot();
    });

    test('Func:calculateServiceChargeTaxExclusive', () => {
        const serviceChargeItem = {
            itemType: 'ServiceCharge',
            rate: 0.1,
            taxRate: 0.12,
        };

        calculateServiceCharge(
            serviceChargeItem,
            {
                subtotal: 20,
                discount: 0,
                items: [
                    {
                        itemType: '',
                        isTakeaway: false,
                        subTotal: 20,
                        discount: 0,
                    },
                ],
            },
            false,
        );

        expect(serviceChargeItem).toMatchSnapshot();
    });

    test('Func:calculateServiceChargeTaxExclusive, with takeaway items', () => {
        const serviceChargeItem = {
            itemType: 'ServiceCharge',
            rate: 0.1,
            taxRate: 0.12,
        };

        calculateServiceCharge(
            serviceChargeItem,
            {
                subtotal: 40,
                discount: 0,
                items: [
                    {
                        itemType: '',
                        isTakeaway: false,
                        subTotal: 20,
                        discount: 0,
                    },
                    {
                        itemType: '',
                        isTakeaway: true,
                        subTotal: 20,
                        discount: 0,
                    },
                ],
            },
            false,
        );

        expect(serviceChargeItem).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxInclusive', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 50,
            type: 'percent',
            taxRate: 0.12,
        };

        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                discountItem,
            ],
            subtotal: 100,
            tax: 12,
            total: 112,
        };

        calculateTransactionDiscount(discountItem, transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxInclusive amount', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 50,
            type: 'amount',
            taxRate: 0.12,
        };

        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                discountItem,
            ],
            subtotal: 100,
            tax: 12,
            total: 112,
        };

        calculateTransactionDiscount(discountItem, transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxInclusive amount, no taxRate', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 50,
            type: 'amount',
        };

        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
                discountItem,
            ],
            subtotal: 100,
            tax: 100,
            total: 100,
        };

        calculateTransactionDiscount(discountItem, transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxInclusive amount, no taxRate, discount item is the first', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 50,
            type: 'amount',
        };

        const transaction = {
            items: [
                discountItem,
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0,
                    total: 25,
                    unitPrice: 25,
                },
            ],
            subtotal: 100,
            tax: 100,
            total: 100,
        };

        calculateTransactionDiscount(discountItem, transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxInclusive amount, no taxRate, empty items', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 10,
            type: 'amount',
        };
        const serviceChargeItem = {
            itemType: 'ServiceCharge',
            unitPrice: 100,
            type: 'amount',
        };

        const transaction = {
            items: [serviceChargeItem, discountItem],
            subtotal: 100,
            tax: 10,
            total: 100,
        };

        calculateTransactionDiscount(discountItem, transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxInclusive, total is zero', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 100,
            type: 'percent',
            taxRate: 0,
        };

        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                discountItem,
            ],
            loyaltyDiscounts: [
                {
                    loyaltyType: 'cashback',
                    inputValue: 12.1,
                    taxRate: 0,
                    type: 'cashback',
                },
            ],
            subtotal: 125,
            tax: 0,
            total: 0,
        };

        calculateTransactionDiscount(discountItem, transaction, true);

        expect(transaction.total).toBe(0);
    });

    test('Func:calculateTransactionDiscountTaxExclusive', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 50,
            type: 'percent',
            taxRate: 0.12,
        };

        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                discountItem,
            ],
            subtotal: 100,
            tax: 12,
            total: 112,
        };

        calculateTransactionDiscount(discountItem, transaction, false);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxExclusive, total is zero', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 100,
            type: 'percent',
            taxRate: 0,
        };

        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 25,
                    tax: 3,
                    taxExclusiveSubtotal: 25,
                    taxInclusiveSubtotal: 28,
                    taxRate: 0.12,
                    total: 25,
                    unitPrice: 25,
                },
                discountItem,
            ],
            subtotal: 125,
            tax: 0,
            total: 0,
        };

        calculateTransactionDiscount(discountItem, transaction, false);

        expect(transaction.total).toBe(0);
        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionDiscountTaxExclusive, only with discountItem', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 100,
            type: 'percent',
            taxRate: 0,
        };

        const transaction = {
            items: [discountItem],
            subtotal: 125,
            tax: 0,
            total: 0,
        };

        calculateTransactionDiscount(discountItem, transaction, false);

        expect(transaction.total).toBe(0);
        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateItemTaxInclusive', () => {
        const purchaseItem = {
            unitPrice: 267.857143,
            selectedOptions: [
                {
                    optionValue: 89.2857143,
                    quantity: 1,
                },
                {
                    optionValue: 44.6428571,
                    quantity: 4,
                },
            ],
            quantity: 1,
            taxRate: 0.12,
        };

        calculateItem(purchaseItem, true);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateItemTaxExclusive', () => {
        const purchaseItem = {
            unitPrice: 267.857143,
            selectedOptions: [
                {
                    optionValue: 89.2857143,
                    quantity: 1,
                },
                {
                    optionValue: 44.6428571,
                    quantity: 4,
                },
            ],
            quantity: 1,
            taxRate: 0.12,
        };

        calculateItem(purchaseItem, false);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateItemDiscountTaxInclusive', () => {
        const purchaseItem = {
            unitPrice: 267.857143,
            selectedOptions: [
                {
                    optionValue: 89.2857143,
                    quantity: 1,
                },
                {
                    optionValue: 44.6428571,
                    quantity: 4,
                },
            ],
            quantity: 1,
            taxRate: 0.12,
            itemLevelDiscount: {
                inputValue: 500,
                type: 'amount',
            },
        };

        calculateItemDiscount(purchaseItem, true);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateItemDiscountTaxExclusive', () => {
        const purchaseItem = {
            unitPrice: 267.857143,
            selectedOptions: [
                {
                    optionValue: 89.2857143,
                    quantity: 1,
                },
                {
                    optionValue: 44.6428571,
                    quantity: 4,
                },
            ],
            quantity: 1,
            taxRate: 0.12,
            itemLevelDiscount: {
                inputValue: 500,
                type: 'amount',
            },
        };

        calculateItemDiscount(purchaseItem, false);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateItemPromotionTaxInclusive', () => {
        const purchaseItem = {
            adhocDiscount: 0,
            discount: 0,
            quantity: 1,
            subTotal: 25,
            tax: 3,
            taxExclusiveSubtotal: 25,
            taxInclusiveSubtotal: 28,
            taxRate: 0.12,
            total: 25,
            unitPrice: 25,
            taxInclusiveUnitPrice: 28,
            promotions: [
                {
                    inputValue: 83.3333333,
                    type: 'percentage',
                    quantity: 1,
                },
            ],
        };

        calculateItemPromotions(purchaseItem, true);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateItemPromotionTaxExclusive', () => {
        const purchaseItem = {
            adhocDiscount: 0,
            discount: 0,
            quantity: 1,
            subTotal: 25,
            tax: 3,
            taxExclusiveSubtotal: 25,
            taxInclusiveSubtotal: 28,
            taxRate: 0.12,
            total: 25,
            unitPrice: 25,
            taxExclusiveUnitPrice: 25,
            promotions: [
                {
                    inputValue: 83.3333333,
                    type: 'percentage',
                    quantity: 1,
                },
            ],
        };

        calculateItemPromotions(purchaseItem, false);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateItemPromotionTaxInclusive other', () => {
        const purchaseItem = {
            unitPrice: 267.857143,
            quantity: 1,
            taxRate: 0.12,
            promotions: [
                {
                    inputValue: 83.3333333,
                    type: 'other',
                    quantity: 1,
                },
            ],
        };

        calculateItemPromotions(purchaseItem, true);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateItemPromotionTaxExclusive other', () => {
        const purchaseItem = {
            unitPrice: 267.857143,
            quantity: 1,
            taxRate: 0.12,
            promotions: [
                {
                    inputValue: 83.3333333,
                    type: 'other',
                    quantity: 1,
                },
            ],
        };

        calculateItemPromotions(purchaseItem, false);

        expect(purchaseItem).toMatchSnapshot();
    });

    test('Func:calculateTransactionPromotionTaxInclusive other', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    inputValue: 50,
                    type: 'other',
                },
            ],
        };

        calculateTransactionPromotion(transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionPromotionTaxExclusive other', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    inputValue: 50,
                    type: 'other',
                },
            ],
        };

        calculateTransactionPromotion(transaction, false);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionPromotionTaxInclusive ,absolute, only with discountItem', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 100,
            type: 'percent',
            taxRate: 0,
        };

        const transaction = {
            items: [discountItem],
            promotions: [
                {
                    inputValue: 50,
                    type: 'absolute',
                },
            ],
        };

        calculateTransactionPromotion(transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateTransactionPromotionTaxExclusive ,absolute, only with discountItem', () => {
        const discountItem = {
            itemType: 'Discount',
            inputValue: 100,
            type: 'percent',
            taxRate: 0,
        };

        const transaction = {
            items: [discountItem],
            promotions: [
                {
                    inputValue: 50,
                    type: 'absolute',
                },
            ],
        };

        calculateTransactionPromotion(transaction, false);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateBirDiscountTaxInclusive other', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'other',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };

        calculate(transaction, true);

        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculateBirDiscountTaxExclusive other', () => {
        const item = {
            unitPrice: 535.714286,
            quantity: 1,
            taxRate: 0.12,
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };

        calculateBirFields(item, false);

        expect(item).toMatchSnapshot();
    });
});

describe('calculator:calculation:jest', () => {
    test('Func:calculate:taxInclusiveDisplay:item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item no tax', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(535.71);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(535.71);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item no tax', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(535.71);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(535.71);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item return', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                    return: {
                        quantity: 1,
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].display.total).toBe(600);
        expect(res.items[0].display.tax).toBe(64.29);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item return', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                    return: {
                        quantity: 1,
                    },
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.items[0].display.total).toBe(535.71);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item return multiple', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].display.total).toBe(400);
        expect(res.items[0].display.tax).toBe(42.86);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(357.14);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(42.86);
        expect(res.total).toBe(400);

        expect(res.display.subtotal).toBe(357.14);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(42.86);
        expect(res.display.total).toBe(400);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item return multiple', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                    },
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.items[0].display.total).toBe(357.14);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(357.14);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(42.86);
        expect(res.total).toBe(400);

        expect(res.display.subtotal).toBe(357.14);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(42.86);
        expect(res.display.total).toBe(400);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item return multiple edit total', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                        total: 300,
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].display.total).toBe(300);
        expect(res.items[0].display.tax).toBe(32.14);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(357.14);
        expect(res.discount).toBe(89.28);
        expect(res.tax).toBe(32.14);
        expect(res.total).toBe(300);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(32.14);
        expect(res.display.total).toBe(300);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item return multiple edit total', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                        total: 300,
                    },
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.items[0].display.total).toBe(300);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(357.14);
        expect(res.discount).toBe(57.14);
        expect(res.tax).toBe(36);
        expect(res.total).toBe(336);

        expect(res.display.subtotal).toBe(300);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(36);
        expect(res.display.total).toBe(336);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item return multiple edit total serviceCharge', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                        total: 300,
                    },
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 53.57,
                    tax: 6.43,
                    taxRate: 0.12,
                    total: 53.57,
                    unitPrice: 53.5714286,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].display.total).toBe(300);
        expect(res.items[0].display.tax).toBe(32.14);

        expect(res.serviceCharge).toBe(53.57);
        expect(res.serviceChargeTax).toBe(6.43);
        expect(res.subtotal).toBe(357.14);
        expect(res.discount).toBe(89.28);
        expect(res.tax).toBe(38.57);
        expect(res.total).toBe(360);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(53.57);
        expect(res.display.tax).toBe(38.57);
        expect(res.display.total).toBe(360);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item return multiple edit total serviceCharge', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                        total: 300,
                    },
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 53.57,
                    tax: 6.43,
                    taxRate: 0.12,
                    total: 53.57,
                    unitPrice: 53.5714286,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.items[0].display.total).toBe(300);

        expect(res.serviceCharge).toBe(53.57);
        expect(res.serviceChargeTax).toBe(6.43);
        expect(res.subtotal).toBe(357.14);
        expect(res.discount).toBe(57.14);
        expect(res.tax).toBe(42.43);
        expect(res.total).toBe(396);

        expect(res.display.subtotal).toBe(300);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(53.57);
        expect(res.display.tax).toBe(42.43);
        expect(res.display.total).toBe(396);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item return total is 0', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                        total: 0,
                    },
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(0);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(0);

        expect(res.items[0].total).toBe(0);
        expect(res.items[0].subTotal).toBe(0);
        expect(res.items[0].discount).toBe(0);
        expect(res.items[0].tax).toBe(0);
    });

    test('Func:calculate:taxInclusiveDisplay:item return total is 0', () => {
        const transaction = {
            calculationMode: 'Refund',
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 3,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 178.571429,
                    return: {
                        quantity: 2,
                        total: 0,
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(0);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(0);

        expect(res.items[0].total).toBe(0);
        expect(res.items[0].subTotal).toBe(0);
        expect(res.items[0].discount).toBe(0);
        expect(res.items[0].tax).toBe(0);
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount amount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(446.42);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount amount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(4.29);
        expect(res.total).toBe(40);

        expect(res.display.subtotal).toBe(35.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(4.29);
        expect(res.display.total).toBe(40);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount percent', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 83.333,
                        type: 'percent',
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(446.42);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount percent', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 83.333,
                        type: 'percent',
                    },
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(446.42);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount percent', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                    inputValue: 50,
                },
            ],
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(550);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(50);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(-50);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(50);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 0,
                    discount: 50,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            original: {
                subtotal: 600,
                tax: 0,
                total: 600,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 0,
                    discount: 500,
                    type: 'ItemManualDiscount',
                },
                {
                    deductedTax: 0,
                    discount: 50,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 0,
                total: 600,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, fullBillDiscount percent, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                    inputValue: 50,
                },
            ],
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(400);
        expect(res.tax).toBe(26);
        expect(res.total).toBe(426);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(-400);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(26);
        expect(res.display.total).toBe(426);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 8,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, fullBillDiscount amount off, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
                {
                    itemType: 'Discount',
                    type: 'amount',
                    inputValue: 426,
                },
            ],
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(400);
        expect(res.tax).toBe(26);
        expect(res.total).toBe(426);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(-400);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(26);
        expect(res.display.total).toBe(426);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 8,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, fullBillDiscount percent, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                    inputValue: 50,
                },
            ],
        };

        const res = calculate(transaction, false);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(400);
        expect(res.tax).toBe(26);
        expect(res.total).toBe(426);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(-400);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(26);
        expect(res.display.total).toBe(426);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 8,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, fullBillDiscount amount, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
                {
                    itemType: 'Discount',
                    type: 'amount',
                    inputValue: 400,
                },
            ],
        };

        const res = calculate(transaction, false);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(400);
        expect(res.tax).toBe(26);
        expect(res.total).toBe(426);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(-400);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(26);
        expect(res.display.total).toBe(426);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 8,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount, wrong discount type', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'wrong',
                    inputValue: 50,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(100);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 0,
                    discount: 0,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            original: {
                subtotal: 600,
                tax: 0,
                total: 600,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 0,
                    discount: 500,
                    type: 'ItemManualDiscount',
                },
                {
                    deductedTax: 0,
                    discount: 0,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 0,
                total: 600,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount, wrong discount type', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'wrong',
                    inputValue: 50,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(100);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 0,
                    discount: 0,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            original: {
                subtotal: 600,
                tax: 0,
                total: 600,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 0,
                    discount: 500,
                    type: 'ItemManualDiscount',
                },
                {
                    deductedTax: 0,
                    discount: 0,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 0,
                total: 600,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplayLegacy:item, selectedOptions, itemDiscount, fullBillDiscount, wrong discount type', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'wrong',
                    inputValue: 50,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplayLegacy:item, selectedOptions, itemDiscount, fullBillDiscount, wrong discount type', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'wrong',
                    inputValue: 50,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplayLegacy:item, selectedOptions, itemDiscount, fullBillDiscount percent, no tax, no inputValue', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount percent, no tax, no inputValue', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount percent, no tax, no inputValue', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(100);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(100);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscountLegacy amount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 20,
                    type: 'amount',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(520);
        expect(res.tax).toBe(1.89);
        expect(res.total).toBe(17.6);

        expect(res.display.subtotal).toBe(35.71);
        expect(res.display.discount).toBe(-20);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(1.89);
        expect(res.display.total).toBe(17.6);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount amount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'amount',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.36);
        expect(res.total).toBe(50);

        expect(res.display.subtotal).toBe(89.28);
        expect(res.display.discount).toBe(-44.64);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(5.36);
        expect(res.display.total).toBe(50);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 5.36,
                    discount: 44.64,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 53.57,
                    discount: 446.43,
                    type: 'ItemManualDiscount',
                },
                {
                    deductedTax: 5.36,
                    discount: 44.64,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            fullPrice: 535.7142857,
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount amount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 20,
                    type: 'amount',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(520);
        expect(res.tax).toBe(1.89);
        expect(res.total).toBe(17.6);

        expect(res.display.subtotal).toBe(35.71);
        expect(res.display.discount).toBe(-20);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(1.89);
        expect(res.display.total).toBe(17.6);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 2.4,
                    discount: 20,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 60,
                    discount: 500,
                    type: 'ItemManualDiscount',
                },
                {
                    deductedTax: 2.4,
                    discount: 20,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [],
            fullPrice: 535.7142857,
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount percent, special items', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    taxCode: 'da9dsu8yasdhw7dh78da787ds',
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.36);
        expect(res.total).toBe(50);

        expect(res.display.subtotal).toBe(89.28);
        expect(res.display.discount).toBe(-44.64);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(5.36);
        expect(res.display.total).toBe(50);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 5.36,
                    discount: 44.64,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 5.36,
                    taxCode: 'da9dsu8yasdhw7dh78da787ds',
                    taxRate: 0.12,
                },
            ],
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 53.57,
                    discount: 446.43,
                    type: 'ItemManualDiscount',
                },
                {
                    deductedTax: 5.36,
                    discount: 44.64,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 5.36,
                    taxCode: 'da9dsu8yasdhw7dh78da787ds',
                    taxRate: 0.12,
                },
            ],
            fullPrice: 535.7142857,
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
        });
        // expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount percent', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    taxCode: 'dh8ay8dasdh7asd77dsdy',
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(517.86);
        expect(res.tax).toBe(2.14);
        expect(res.total).toBe(19.99);

        expect(res.display.subtotal).toBe(35.71);
        expect(res.display.discount).toBe(-17.86);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(2.14);
        expect(res.display.total).toBe(19.99);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 2.14,
                    discount: 17.86,
                    type: 'TransactionManualDiscount',
                },
            ],
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 2.14,
                    taxCode: 'dh8ay8dasdh7asd77dsdy',
                    taxRate: 0.12,
                },
            ],
        });

        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 60,
                    discount: 500,
                    type: 'ItemManualDiscount',
                },
                {
                    deductedTax: 2.14,
                    discount: 17.86,
                    type: 'TransactionManualDiscount',
                },
            ],
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 2.14,
                    taxCode: 'dh8ay8dasdh7asd77dsdy',
                    taxRate: 0.12,
                },
            ],
            fullPrice: 535.7142857,
            original: {
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item multiple, selectedOptions, itemDiscount, fullBillDiscount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 250,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(491.08);
        expect(res.tax).toBe(5.36);
        expect(res.total).toBe(50);

        expect(res.display.subtotal).toBe(89.28);
        expect(res.display.discount).toBe(-44.64);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(5.36);
        expect(res.display.total).toBe(50);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item multiple, selectedOptions, itemDiscount, fullBillDiscount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 250,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(517.86);
        expect(res.tax).toBe(2.15);
        expect(res.total).toBe(20.01);

        expect(res.display.subtotal).toBe(35.72);
        expect(res.display.discount).toBe(-17.86);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(2.15);
        expect(res.display.total).toBe(20.01);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(89.28);
        expect(res.display.discount).toBe(-44.64);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount, ServiceCharge, ItemOriginalFields', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 89.28571428,
                    quantity: 1,
                    taxRate: 0.12,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                    isTakeaway: true,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                },
            ],
            takeawayCharge: 1,
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(626);
        expect(res.discount).toBe(535.72);
        expect(res.tax).toBe(11.26);
        expect(res.total).toBe(106);

        expect(res.display.subtotal).toBe(179.57);
        expect(res.display.discount).toBe(-89.29);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(11.26);
        expect(res.display.total).toBe(106);

        expect(res.items[0].calculation).toEqual({
            discounts: [
                { deductedTax: 53.57, discount: 446.43, type: 'ItemManualDiscount' },
                { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
            ],
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 5.36,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                    taxRate: 0.12,
                },
            ],
            fullPrice: 535.7142857,
            original: { subtotal: 535.71, tax: 64.29, total: 600 },
        });

        expect(res.items[1].calculation).toEqual({
            discounts: [{ deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' }],
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 5.36,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                    taxRate: 0.12,
                },
            ],
            fullPrice: 89.28571428,
            original: { subtotal: 90.29, tax: 10.71, total: 101 },
        });

        expect(res.items[2].calculation).toBeUndefined();

        expect(res.items[3].calculation).toEqual({
            discounts: [],
            fullPrice: 53.571,
            original: {
                subtotal: 53.57,
                tax: 6.43,
                total: 60,
            },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 0.54,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                    taxRate: 0.12,
                },
            ],
        });

        expect(res.calculation).toEqual({
            discounts: [{ deductedTax: 10.71, discount: 89.29, type: 'TransactionManualDiscount' }],
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 11.26,
                    taxCode: 'dw788dsfwad7f78hfaf7eh7fhe',
                    taxRate: 0.12,
                },
            ],
            original: { subtotal: 626, tax: 81.43, total: 761 },
        });

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount, ServiceCharge: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };
        calculate(transaction, true);

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(89.28);
        expect(res.display.discount).toBe(-44.64);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(1.79);
        expect(res.serviceChargeTax).toBe(0.21);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(517.86);
        expect(res.tax).toBe(2.35);
        expect(res.total).toBe(21.99);

        expect(res.display.subtotal).toBe(35.71);
        expect(res.display.discount).toBe(-17.86);
        expect(res.display.serviceCharge).toBe(1.79);
        expect(res.display.tax).toBe(2.35);
        expect(res.display.total).toBe(21.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, fullBillDiscount, ServiceCharge:idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(1.79);
        expect(res.serviceChargeTax).toBe(0.21);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(517.86);
        expect(res.tax).toBe(2.35);
        expect(res.total).toBe(21.99);

        expect(res.display.subtotal).toBe(35.71);
        expect(res.display.discount).toBe(-17.86);
        expect(res.display.serviceCharge).toBe(1.79);
        expect(res.display.tax).toBe(2.35);
        expect(res.display.total).toBe(21.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(89.28);
        expect(res.display.discount).toBe(-44.64);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(-44.65);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, overwriteUnitPricePromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 89.2857143,
                            type: 'fixedUnitPrice',
                            quantity: 1,
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(89.28);
        expect(res.display.discount).toBe(-44.64);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, overwriteUnitPricePromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 89.2857143,
                            type: 'fixedUnitPrice',
                            quantity: 1,
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(-44.65);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, buyXGetYFreePromotion, fullBillDiscountLegacy, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 1,
                            type: 'buyXFreeY',
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 91.6666667,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(1071.43);
        expect(res.discount).toBe(1026.79);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(-491.07);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, buyXGetYFreePromotion, fullBillDiscountLegacy, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 1,
                            type: 'buyXFreeY',
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 91.6666667,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(1071.43);
        expect(res.discount).toBe(1026.79);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(535.72);
        expect(res.display.discount).toBe(-491.08);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, buyXGetYFreePromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 1,
                            type: 'buyXFreeY',
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 91.6666667,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(1071.43);
        expect(res.discount).toBe(1026.79);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(-491.07);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, buyXGetYFreePromotion, fullBillDiscount, ServiceCharge:idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 1,
                            type: 'buyXFreeY',
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 91.6666667,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };
        calculate(transaction, true);

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(1071.43);
        expect(res.discount).toBe(1026.79);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(-491.07);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, buyXGetYFreePromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 1,
                            type: 'buyXFreeY',
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 91.6666667,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(1071.43);
        expect(res.discount).toBe(1026.79);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(535.72);
        expect(res.display.discount).toBe(-491.08);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, buyXGetYFreePromotion, fullBillDiscount, ServiceCharge:idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 1,
                            type: 'buyXFreeY',
                        },
                    ],
                },
                {
                    itemType: 'Discount',
                    inputValue: 91.6666667,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(1071.43);
        expect(res.discount).toBe(1026.79);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(535.72);
        expect(res.display.discount).toBe(-491.08);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item multiple, selectedOptions, itemDiscount, takeAmountOffPromotion', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 250,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 2,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(491.08);
        expect(res.tax).toBe(5.36);
        expect(res.total).toBe(50);

        expect(res.display.subtotal).toBe(44.64);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(5.36);
        expect(res.display.total).toBe(50);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item multiple, selectedOptions, itemDiscount, takeAmountOffPromotion', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 250,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 2,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 17.86,
                    type: 'absolute',
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(517.86);
        expect(res.tax).toBe(2.15);
        expect(res.total).toBe(20.01);

        expect(res.display.subtotal).toBe(17.86);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(2.15);
        expect(res.display.total).toBe(20.01);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, itemDiscount, takePercentageOffPromotion', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 17.3482143,
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                        },
                    ],
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(17.35);
        expect(res.discount).toBe(4.86);
        expect(res.tax).toBe(1.5);
        expect(res.total).toBe(13.99);

        expect(res.display.subtotal).toBe(12.49);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(1.5);
        expect(res.display.total).toBe(13.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, itemDiscount, takePercentageOffPromotion', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 17.3482143,
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                        },
                    ],
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(17.35);
        expect(res.discount).toBe(4.86);
        expect(res.tax).toBe(1.5);
        expect(res.total).toBe(13.99);

        expect(res.display.subtotal).toBe(12.49);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(1.5);
        expect(res.display.total).toBe(13.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, itemDiscount, takeAmountOffPromotion', () => {
        const transaction = {
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
                {
                    unitPrice: 94.33962264150944,
                    quantity: 1,
                    taxRate: 0.06,
                    itemLevelDiscount: {
                        inputValue: 50,
                        type: 'percent',
                    },
                    promotionAppliedQuantityMap: {
                        '5c3da2ae17da320183b3de97': 1,
                    },
                },
            ],
            promotions: [
                {
                    inputValue: 10,
                    type: 'absolute',
                    discountType: 'absolute',
                    promotionId: '5c3da2ae17da320183b3de97',
                    taxRate: 0.06,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(3.77);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(94.34);
        expect(res.discount).toBe(56.6);
        expect(res.tax).toBe(2.26);
        expect(res.total).toBe(43.77);

        expect(res.display.subtotal).toBe(37.74);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(3.77);
        expect(res.display.tax).toBe(2.26);
        expect(res.display.total).toBe(43.77);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, itemDiscount, takeAmountOffPromotion', () => {
        const transaction = {
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
                {
                    unitPrice: 94.33962264150944,
                    quantity: 1,
                    taxRate: 0.06,
                    itemLevelDiscount: {
                        inputValue: 50,
                        type: 'percent',
                    },
                    promotionAppliedQuantityMap: {
                        '5c3da2ae17da320183b3de97': 1,
                    },
                },
            ],
            promotions: [
                {
                    inputValue: 10,
                    type: 'absolute',
                    discountType: 'absolute',
                    promotionId: '5c3da2ae17da320183b3de97',
                    taxRate: 0.06,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(3.72);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(94.34);
        expect(res.discount).toBe(57.17);
        expect(res.tax).toBe(2.23);
        expect(res.total).toBe(43.12);

        expect(res.display.subtotal).toBe(37.17);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(3.72);
        expect(res.display.tax).toBe(2.23);
        expect(res.display.total).toBe(43.12);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(44.64);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, ServiceCharge: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
        };
        calculate(transaction, true);

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(4.46);
        expect(res.serviceChargeTax).toBe(0.54);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(491.07);
        expect(res.tax).toBe(5.9);
        expect(res.total).toBe(55);

        expect(res.display.subtotal).toBe(44.64);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(4.46);
        expect(res.display.tax).toBe(5.9);
        expect(res.display.total).toBe(55);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 17.86,
                    type: 'absolute',
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(1.79);
        expect(res.serviceChargeTax).toBe(0.21);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(517.86);
        expect(res.tax).toBe(2.35);
        expect(res.total).toBe(21.99);

        expect(res.display.subtotal).toBe(17.85);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(1.79);
        expect(res.display.tax).toBe(2.35);
        expect(res.display.total).toBe(21.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, ServiceCharge:idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 17.86,
                    type: 'absolute',
                },
            ],
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(1.79);
        expect(res.serviceChargeTax).toBe(0.21);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(517.86);
        expect(res.tax).toBe(2.35);
        expect(res.total).toBe(21.99);

        expect(res.display.subtotal).toBe(17.85);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(1.79);
        expect(res.display.tax).toBe(2.35);
        expect(res.display.total).toBe(21.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item multiple, selectedOptions, comboPromotion', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 89.2857143,
                    type: 'combo',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(178.58);
        expect(res.tax).toBe(42.86);
        expect(res.total).toBe(400);

        expect(res.display.subtotal).toBe(357.14);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(42.86);
        expect(res.display.total).toBe(400);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item multiple, selectedOptions, comboPromotion', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 89.29,
                    type: 'combo',
                    quantity: 1,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(178.57);
        expect(res.tax).toBe(42.86);
        expect(res.total).toBe(400.01);

        expect(res.display.subtotal).toBe(357.15);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(42.86);
        expect(res.display.total).toBe(400.01);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item multiple, selectedOptions, comboPromotion multiple', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test2: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: {
                        test1: 1,
                        test2: 1,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 89.2857143,
                    type: 'combo',
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    promotionId: 'test2',
                    inputValue: 89.2857143,
                    type: 'combo',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(357.15);
        expect(res.tax).toBe(21.43);
        expect(res.total).toBe(200);

        expect(res.display.subtotal).toBe(178.57);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(21.43);
        expect(res.display.total).toBe(200);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item multiple, selectedOptions, comboPromotion multple', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test2: 1 },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: {
                        test1: 1,
                        test2: 1,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 89.29,
                    type: 'combo',
                    quantity: 1,
                },
                {
                    promotionId: 'test2',
                    inputValue: 89.29,
                    type: 'combo',
                    quantity: 1,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(357.14);
        expect(res.tax).toBe(21.43);
        expect(res.total).toBe(200.01);

        expect(res.display.subtotal).toBe(178.58);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(21.43);
        expect(res.display.total).toBe(200.01);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, comboPromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(165);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.total).toBe(165);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, comboPromotion, fullBillDiscount, ServiceCharge: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };
        calculate(transaction, true);

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(165);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.total).toBe(165);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, comboPromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(165);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.total).toBe(165);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, comboPromotion, fullBillDiscount, ServiceCharge: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                },
            ],
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(165);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.total).toBe(165);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, comboPromotion, fullBillDiscount, ServiceCharge, shippingFee', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                },
            ],
            shippingFee: 10,
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(175);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.shippingFee).toBe(10);
        expect(res.display.total).toBe(175);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, comboPromotion, fullBillDiscount, ServiceCharge, shippingFee: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                },
            ],
            shippingFee: 10,
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(175);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.shippingFee).toBe(10);
        expect(res.display.total).toBe(175);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, comboPromotion, freeShippingPromotion, fullBillDiscount, ServiceCharge, shippingFee', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                },
                {
                    type: 'freeShipping',
                },
            ],
            shippingFee: 10,
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(165);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.shippingFee).toBe(0);
        expect(res.display.total).toBe(165);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, comboPromotion, freeShippingPromotion, fullBillDiscount, ServiceCharge, shippingFee: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 267.857143,
                    type: 'combo',
                    quantity: 1,
                },
                {
                    type: 'freeShipping',
                },
            ],
            shippingFee: 10,
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(1.61);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(401.78);
        expect(res.tax).toBe(17.68);
        expect(res.total).toBe(165);

        expect(res.display.subtotal).toBe(267.86);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(17.68);
        expect(res.display.shippingFee).toBe(0);
        expect(res.display.total).toBe(165);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item multiple, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 250,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 2,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'storeCredit',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(4.28);
        expect(res.total).toBe(40);

        expect(res.display.subtotal).toBe(44.65);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(4.28);
        expect(res.display.total).toBe(40);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item multiple, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 125,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 89.2857143,
                    selectedOptions: [
                        {
                            optionValue: 44.6428571,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 250,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 2,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 17.86,
                    type: 'absolute',
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'storeCredit',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.72);
        expect(res.discount).toBe(526.78);
        expect(res.tax).toBe(1.08);
        expect(res.total).toBe(10.02);

        expect(res.display.subtotal).toBe(17.86);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(1.08);
        expect(res.display.total).toBe(10.02);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(3.57);
        expect(res.serviceChargeTax).toBe(0.43);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(4.72);
        expect(res.total).toBe(44);

        expect(res.display.subtotal).toBe(44.64);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(3.57);
        expect(res.display.tax).toBe(4.72);
        expect(res.display.total).toBe(44);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0.12,
                },
            ],
        };
        calculate(transaction, true);

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(3.57);
        expect(res.serviceChargeTax).toBe(0.43);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(500);
        expect(res.tax).toBe(4.72);
        expect(res.total).toBe(44);

        expect(res.display.subtotal).toBe(44.64);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(3.57);
        expect(res.display.tax).toBe(4.72);
        expect(res.display.total).toBe(44);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge', () => {
        const transaction = {
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 17.86,
                    type: 'absolute',
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0.89);
        expect(res.serviceChargeTax).toBe(0.11);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(526.79);
        expect(res.tax).toBe(1.18);
        expect(res.total).toBe(10.99);

        expect(res.display.subtotal).toBe(17.85);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0.89);
        expect(res.display.tax).toBe(1.18);
        expect(res.display.total).toBe(10.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, loyaltyDiscount, total is zero', () => {
        const transaction = {
            items: [
                {
                    quantity: 1,
                    taxRate: 0,
                    unitPrice: 198,
                    promotions: [
                        {
                            inputValue: 100,
                            type: 'percentage',
                            discountType: 'percentage',
                            promotionId: '5ed876ad5292ac30fefdb9af',
                            promotionCode: 'XXXXXX',
                            promotionName: 'xxxxxx',
                            discount: 198,
                            display: {
                                discount: 198,
                            },
                            quantity: 1,
                        },
                    ],
                },
            ],
            loyaltyDiscounts: [
                {
                    loyaltyType: 'cashback',
                    inputValue: 12.1,
                    taxRate: 0,
                    type: 'cashback',
                },
            ],
            total: 0,
            subtotal: 198,
        };

        const res = calculate(transaction, true);

        expect(res.subtotal).toBe(198);
        expect(res.discount).toBe(198);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(0);
        expect(res.loyaltyDiscounts[0].spentValue).toBe(0);
    });

    test('Func:calculate:taxExclusiveDisplay:item, loyaltyDiscount', () => {
        const transaction = {
            items: [
                {
                    quantity: 1,
                    taxRate: 0.32,
                    unitPrice: 14.25,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 2.330000000000001,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.subtotal).toBe(14.25);
        expect(res.discount).toBe(1.77);
        expect(res.tax).toBe(3.99);
        expect(res.total).toBe(16.47);
        expect(res.loyaltyDiscounts[0].spentValue).toBe(2.33);

        expect(res.display.subtotal).toBe(14.25);
        expect(res.display.discount).toBe(0);
        expect(res.display.tax).toBe(3.99);
        expect(res.display.total).toBe(16.47);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, loyaltyDiscount with 3 digital inputValue, and 3rd digital > 5 ', () => {
        const transaction = {
            items: [
                {
                    quantity: 1,
                    taxRate: 0.32,
                    unitPrice: 14.25,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 2.335000000000001,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.subtotal).toBe(14.25);
        expect(res.discount).toBe(1.77);
        expect(res.tax).toBe(3.99);
        expect(res.total).toBe(16.47);
        expect(res.loyaltyDiscounts[0].spentValue).toBe(2.33);

        expect(res.display.subtotal).toBe(14.25);
        expect(res.display.discount).toBe(0);
        expect(res.display.tax).toBe(3.99);
        expect(res.display.total).toBe(16.47);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, loyaltyDiscount with 3 digital inputValue, and 3rd digital < 5 ', () => {
        const transaction = {
            items: [
                {
                    quantity: 1,
                    taxRate: 0.32,
                    unitPrice: 14.25,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 2.334000000000001,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.subtotal).toBe(14.25);
        expect(res.discount).toBe(1.77);
        expect(res.tax).toBe(3.99);
        expect(res.total).toBe(16.47);
        expect(res.loyaltyDiscounts[0].spentValue).toBe(2.33);

        expect(res.display.subtotal).toBe(14.25);
        expect(res.display.discount).toBe(0);
        expect(res.display.tax).toBe(3.99);
        expect(res.display.total).toBe(16.47);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, loyaltyDiscount, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 426,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(400);
        expect(res.tax).toBe(26);
        expect(res.total).toBe(426);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(26);
        expect(res.display.total).toBe(426);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 8,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, fullBillDiscount percent, loyaltyDiscount, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                    inputValue: 50,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 213,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(600);
        expect(res.tax).toBe(13);
        expect(res.total).toBe(213);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(-400);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(13);
        expect(res.display.total).toBe(213);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'TransactionManualDiscount',
                },
                {
                    deductedTax: 13,
                    discount: 200,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 9,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 4,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'TransactionManualDiscount',
                },
                {
                    deductedTax: 9,
                    discount: 150,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 9,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, selectedOptions, fullBillDiscount percent, loyaltyDiscount, takeAmountOffPromotion, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                    inputValue: 50,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 106.5,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 106.5,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(25);
        expect(res.serviceChargeTax).toBe(3);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(550);
        expect(res.tax).toBe(19.25);
        expect(res.total).toBe(294.25);

        expect(res.display.subtotal).toBe(700);
        expect(res.display.discount).toBe(-350);
        expect(res.display.serviceCharge).toBe(25);
        expect(res.display.tax).toBe(19.25);
        expect(res.display.total).toBe(294.25);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 6.5,
                    discount: 100,
                    promotionId: 'test1',
                    subType: 'absolute',
                    type: 'Promotion',
                },
                { deductedTax: 22.75, discount: 350, type: 'TransactionManualDiscount' },
                { deductedTax: 6.5, discount: 100, type: 'Loyalty' },
            ],
            original: { subtotal: 800, tax: 61.6, total: 941.6 },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 11.25,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                },
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 5,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                },
            ],
        });
        expect(res.items[1].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 4.5,
                    discount: 75,
                    promotionId: 'test1',
                    subType: 'absolute',
                    type: 'Promotion',
                },
                {
                    deductedTax: 15.75,
                    discount: 262.5,
                    type: 'TransactionManualDiscount',
                },
                { deductedTax: 4.5, discount: 75, type: 'Loyalty' },
            ],
            fullPrice: 600,
            original: { subtotal: 600, tax: 36, total: 636 },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 11.25,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                },
            ],
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, loyaltyDiscount, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 426,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, false);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(400);
        expect(res.tax).toBe(26);
        expect(res.total).toBe(426);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(26);
        expect(res.display.total).toBe(426);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 8,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 18,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, fullBillDiscount amount, loyaltyDiscount, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                },
                {
                    itemType: 'Discount',
                    type: 'amount',
                    inputValue: 400,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 213,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, false);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(600);
        expect(res.tax).toBe(13);
        expect(res.total).toBe(213);

        expect(res.display.subtotal).toBe(800);
        expect(res.display.discount).toBe(-400);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(13);
        expect(res.display.total).toBe(213);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 26,
                    discount: 400,
                    type: 'TransactionManualDiscount',
                },
                {
                    deductedTax: 13,
                    discount: 200,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 9,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
                {
                    tax: 4,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            original: {
                subtotal: 800,
                tax: 52,
                total: 852,
            },
        });
        expect(res.items[0].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 18,
                    discount: 300,
                    type: 'TransactionManualDiscount',
                },
                {
                    deductedTax: 9,
                    discount: 150,
                    type: 'Loyalty',
                },
            ],
            taxes: [
                {
                    tax: 9,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                    isVatExempted: false,
                    isAmusementTax: false,
                },
            ],
            fullPrice: 600,
            original: {
                subtotal: 600,
                tax: 36,
                total: 636,
            },
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, ServiceCharge, selectedOptions, fullBillDiscount percent, loyaltyDiscount, takeAmountOffPromotion, items with different taxRate', () => {
        const transaction = {
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    taxCode: 'A1213331',
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 200,
                    quantity: 1,
                    taxRate: 0.08,
                    taxCode: 'B7886464',
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'Discount',
                    type: 'percent',
                    inputValue: 50,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 100,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 106.5,
                    type: 'cashback',
                    taxRate: 0.32,
                },
            ],
        };

        const res = calculate(transaction, false);
        expect(res.serviceCharge).toBe(25);
        expect(res.serviceChargeTax).toBe(3);
        expect(res.subtotal).toBe(800);
        expect(res.discount).toBe(550);
        expect(res.tax).toBe(19.25);
        expect(res.total).toBe(294.25);

        expect(res.display.subtotal).toBe(700);
        expect(res.display.discount).toBe(-350);
        expect(res.display.serviceCharge).toBe(25);
        expect(res.display.tax).toBe(19.25);
        expect(res.display.total).toBe(294.25);
        expect(res.calculation).toEqual({
            discounts: [
                {
                    deductedTax: 6.5,
                    discount: 100,
                    promotionId: 'test1',
                    subType: 'absolute',
                    type: 'Promotion',
                },
                { deductedTax: 22.75, discount: 350, type: 'TransactionManualDiscount' },
                { deductedTax: 6.5, discount: 100, type: 'Loyalty' },
            ],
            original: { subtotal: 800, tax: 61.6, total: 941.6 },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 11.25,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                },
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 5,
                    taxCode: 'B7886464',
                    taxRate: 0.08,
                },
            ],
        });
        expect(res.items[1].calculation).toEqual({
            discounts: [
                {
                    deductedTax: 4.5,
                    discount: 75,
                    promotionId: 'test1',
                    subType: 'absolute',
                    type: 'Promotion',
                },
                {
                    deductedTax: 15.75,
                    discount: 262.5,
                    type: 'TransactionManualDiscount',
                },
                { deductedTax: 4.5, discount: 75, type: 'Loyalty' },
            ],
            fullPrice: 600,
            original: { subtotal: 600, tax: 36, total: 636 },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 11.25,
                    taxCode: 'A1213331',
                    taxRate: 0.06,
                },
            ],
        });
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 17.86,
                    type: 'absolute',
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0.12,
                },
            ],
        };
        const res = calculate(transaction, false);
        expect(res.serviceCharge).toBe(0.89);
        expect(res.serviceChargeTax).toBe(0.11);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(526.79);
        expect(res.tax).toBe(1.18);
        expect(res.total).toBe(10.99);

        expect(res.display.subtotal).toBe(17.85);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0.89);
        expect(res.display.tax).toBe(1.18);
        expect(res.display.total).toBe(10.99);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirFnb', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 8.92857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0.8);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(8.93);
        expect(res.discount).toBe(0.89);
        expect(res.pwdDiscount).toBe(0.89);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(4.46);
        expect(res.taxExemptedSales).toBe(3.57);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0.54);
        expect(res.tax).toBe(0.54);
        expect(res.total).toBe(9.37);

        expect(res.display.subtotal).toBe(8.03);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0.8);
        expect(res.display.tax).toBe(0.54);
        expect(res.display.total).toBe(9.37);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, BirFnb, source POS', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true, 'POS');

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(17.86);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(71.43);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(71.43);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.birDiscount).toBe(17.86);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(71.43);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirFnb, source POS', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true, 'POS');

        expect(res.serviceCharge).toBe(7.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(17.86);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(71.43);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(78.57);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.birDiscount).toBe(17.86);
        expect(res.display.serviceCharge).toBe(7.14);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(78.57);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail, no isBasicNecessitiesPH, no SC/PWD count', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(26.79);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(267.87);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(267.87);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(32.13);
        expect(res.total).toBe(326.79);

        expect(res.display.subtotal).toBe(267.87);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(26.79);
        expect(res.display.tax).toBe(32.13);
        expect(res.display.total).toBe(326.79);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail,seniorCount, no isBasicNecessitiesPH', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(26.79);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(267.87);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(267.87);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(32.13);
        expect(res.total).toBe(326.79);

        expect(res.display.subtotal).toBe(267.87);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(26.79);
        expect(res.display.tax).toBe(32.13);
        expect(res.display.total).toBe(326.79);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail, seniorCount, isBasicNecessitiesPH, retailItemsCount < 4 ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(26.79);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(267.87);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(267.87);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(32.13);
        expect(res.total).toBe(326.79);

        expect(res.display.subtotal).toBe(267.87);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(26.79);
        expect(res.display.tax).toBe(32.13);
        expect(res.display.total).toBe(326.79);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail, pwdCount, isBasicNecessitiesPH, retailItemsCount < 4 ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(26.79);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(267.87);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(267.87);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(32.13);
        expect(res.total).toBe(326.79);

        expect(res.display.subtotal).toBe(267.87);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(26.79);
        expect(res.display.tax).toBe(32.13);
        expect(res.display.total).toBe(326.79);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge,Full Bill Discount, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount < 4 ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(13.39);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(267.87);
        expect(res.discount).toBe(133.95);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(133.92);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(16.05);
        expect(res.tax).toBe(16.08);
        expect(res.total).toBe(163.39);

        expect(res.display.subtotal).toBe(267.85);
        expect(res.display.discount).toBe(-133.93);
        expect(res.display.serviceCharge).toBe(13.39);
        expect(res.display.tax).toBe(16.08);
        expect(res.display.total).toBe(163.39);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal < 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(212.5);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(2125.02);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(257.12);
        expect(res.total).toBe(2594.64);

        expect(res.display.subtotal).toBe(2125.02);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(212.5);
        expect(res.display.tax).toBe(257.12);
        expect(res.display.total).toBe(2594.64);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal < 1300, source POS', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true, 'POS');
        expect(res.serviceCharge).toBe(212.5);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(2125.02);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(257.12);
        expect(res.total).toBe(2594.64);

        expect(res.display.subtotal).toBe(2142.88);
        expect(res.display.discount).toBe(0);
        expect(res.display.birDiscount).toBe(17.86);
        expect(res.display.serviceCharge).toBe(212.5);
        expect(res.display.tax).toBe(257.12);
        expect(res.display.total).toBe(2594.64);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail, pwdCount, isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal < 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(212.5);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(17.86);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(2125.02);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(257.12);
        expect(res.total).toBe(2594.64);

        expect(res.display.subtotal).toBe(2125.02);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(212.5);
        expect(res.display.tax).toBe(257.12);
        expect(res.display.total).toBe(2594.64);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, tax, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal < 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(2125.02);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(257.12);
        expect(res.total).toBe(2382.14);

        expect(res.display.subtotal).toBe(2125.02);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(257.12);
        expect(res.display.total).toBe(2382.14);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal < 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(2125.02);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(2125.02);

        expect(res.display.subtotal).toBe(2125.02);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(2125.02);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, part items with tax, BirRetail,seniorCount,isBasicNecessitiesPH,isVatExempted, retailItemsCount >= 4 , retailItemsTotal < 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                    isVatExempted: true,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                    isVatExempted: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(212.5);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(1147.34);
        expect(res.taxExemptedSales).toBe(982.15);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(139.27);
        expect(res.total).toBe(2476.79);

        expect(res.display.subtotal).toBe(2125.02);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(212.5);
        expect(res.display.tax).toBe(139.27);
        expect(res.display.total).toBe(2476.79);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, Full Bill Discount, no tax, BirRetail,seniorCount,isBasicNecessitiesPH,isVatExempted, retailItemsCount >= 4 , retailItemsTotal < 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                    isVatExempted: true,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                    isVatExempted: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(212.5);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(982.15);
        expect(res.zeroRatedSales).toBe(1147.34);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(2337.52);

        expect(res.display.subtotal).toBe(2125.02);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(212.5);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(2337.52);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item,tax, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal >= 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(65);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(65);
        expect(res.taxableSales).toBe(2077.88);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(257.12);
        expect(res.total).toBe(2335);

        expect(res.display.subtotal).toBe(2077.88);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(257.12);
        expect(res.display.total).toBe(2335);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item,tax, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal >= 1300, source POS', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true, 'POS');
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(65);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(65);
        expect(res.taxableSales).toBe(2077.88);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(257.12);
        expect(res.total).toBe(2335);

        expect(res.display.subtotal).toBe(2142.88);
        expect(res.display.discount).toBe(0);
        expect(res.display.birDiscount).toBe(65);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(257.12);
        expect(res.display.total).toBe(2335);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, tax, ServiceCharge, BirRetail,seniorCount,isBasicNecessitiesPH, retailItemsCount >= 4 , retailItemsTotal >= 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(207.79);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(65);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(65);
        expect(res.taxableSales).toBe(2077.88);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(257.12);
        expect(res.total).toBe(2542.79);

        expect(res.display.subtotal).toBe(2077.88);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(207.79);
        expect(res.display.tax).toBe(257.12);
        expect(res.display.total).toBe(2542.79);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, part items with tax, BirRetail,seniorCount,isBasicNecessitiesPH,isVatExempted, retailItemsCount >= 4 , retailItemsTotal >= 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                    isVatExempted: true,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                    isVatExempted: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(207.79);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(65);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(65);
        expect(res.taxableSales).toBe(1125.28);
        expect(res.taxExemptedSales).toBe(982.15);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(139.27);
        expect(res.total).toBe(2424.94);

        expect(res.display.subtotal).toBe(2077.88);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(207.79);
        expect(res.display.tax).toBe(139.27);
        expect(res.display.total).toBe(2424.94);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, Full Bill Discount,no tax, BirRetail,seniorCount,isBasicNecessitiesPH,isVatExempted, retailItemsCount >= 4 , retailItemsSubtotalTotal >= 1300, retailItemsTotalTotal < 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                    isVatExempted: true,
                },
                {
                    unitPrice: 892.857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                    isVatExempted: true,
                },
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0,
                    isBasicNecessitiesPH: false,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(207.79);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2142.88);
        expect(res.discount).toBe(65);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(65);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(982.15);
        expect(res.zeroRatedSales).toBe(1125.28);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(2285.67);

        expect(res.display.subtotal).toBe(2077.88);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(207.79);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(2285.67);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, Full Bill Discount,no tax, BirRetail,seniorCount,isBasicNecessitiesPH,isVatExempted, retailItemsCount >= 4 , retailItemsSubtotalTotal >= 1300, retailItemsTotalTotal >= 1300', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 1000,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 1000,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 1000,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 1000,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: true,
                },
                {
                    unitPrice: 1000,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    unitPrice: 1000,
                    quantity: 1,
                    taxRate: 0.12,
                    isBasicNecessitiesPH: false,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 1,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(593.5);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(6000);
        expect(res.discount).toBe(65);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(65);
        expect(res.taxableSales).toBe(5935);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(720);
        expect(res.total).toBe(7248.5);

        expect(res.display.subtotal).toBe(5935);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(593.5);
        expect(res.display.tax).toBe(720);
        expect(res.display.total).toBe(7248.5);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, tax, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge, BirFnb', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(48.21);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(53.57);
        expect(res.pwdDiscount).toBe(53.57);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(267.86);
        expect(res.taxExemptedSales).toBe(214.29);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(32.14);
        expect(res.tax).toBe(32.14);
        expect(res.total).toBe(562.5);

        expect(res.display.subtotal).toBe(482.15);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(48.21);
        expect(res.display.tax).toBe(32.14);
        expect(res.display.total).toBe(562.5);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge, BirFnb', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0,
                    isVatExempted: true,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(96.43);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(1071.42);
        expect(res.discount).toBe(107.14);
        expect(res.pwdDiscount).toBe(107.14);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(964.28);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(1060.71);

        expect(res.display.subtotal).toBe(964.28);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(96.43);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(1060.71);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge, BirFnb: idempotence, BIR-SC/PWD', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(48.21);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(53.57);
        expect(res.pwdDiscount).toBe(53.57);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(267.86);
        expect(res.taxExemptedSales).toBe(214.29);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(32.14);
        expect(res.tax).toBe(32.14);
        expect(res.total).toBe(562.5);

        expect(res.display.subtotal).toBe(482.15);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(48.21);
        expect(res.display.tax).toBe(32.14);
        expect(res.display.total).toBe(562.5);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-SC/PWD, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(26.04);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(28.93);
        expect(res.pwdDiscount).toBe(28.93);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(44.64);
        expect(res.taxExemptedSales).toBe(125.71);
        expect(res.zeroRatedSales).toBe(90);
        expect(res.totalDeductedTax).toBe(5.36);
        expect(res.tax).toBe(5.36);
        expect(res.total).toBe(291.75);

        expect(res.display.subtotal).toBe(260.35);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(26.04);
        expect(res.display.tax).toBe(5.36);
        expect(res.display.total).toBe(291.75);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-ATHLETE_AND_COACH ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'ATHLETE_AND_COACH',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    pnstmId: 'h787h28yhf7yfs8813hd8',
                },
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(7.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(17.86);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(89.28);

        expect(res.display.subtotal).toBe(71.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(7.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(89.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,Bir-ATHLETE_AND_COACH ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'ATHLETE_AND_COACH',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    pnstmId: 'h787h28yhf7yfs8813hd8',
                },
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(7.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(17.86);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(89.28);

        expect(res.display.subtotal).toBe(71.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(7.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(89.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,Bir-ATHLETE_AND_COACH, source POS', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'ATHLETE_AND_COACH',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    pnstmId: 'h787h28yhf7yfs8813hd8',
                },
            },
        };
        const res = calculate(transaction, true, 'POS');

        expect(res.serviceCharge).toBe(7.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(17.86);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(89.28);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.birDiscount).toBe(17.86);
        expect(res.display.serviceCharge).toBe(7.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(89.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-ATHLETE_AND_COACH, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'ATHLETE_AND_COACH',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(23.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(57.86);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(57.86);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(80);
        expect(res.zeroRatedSales).toBe(80);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(265.28);

        expect(res.display.subtotal).toBe(231.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(23.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(265.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,Bir-ATHLETE_AND_COACH, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'ATHLETE_AND_COACH',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(23.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(57.86);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(57.86);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(80);
        expect(res.zeroRatedSales).toBe(80);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(265.28);

        expect(res.display.subtotal).toBe(231.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(23.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(265.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-MEDAL_OF_VALOR ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'MEDAL_OF_VALOR',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    movId: 'h787h28yhf7yfs8813hd8',
                },
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(7.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(89.28);

        expect(res.display.subtotal).toBe(71.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(7.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(89.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-MEDAL_OF_VALOR, source POS', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'MEDAL_OF_VALOR',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    movId: 'h787h28yhf7yfs8813hd8',
                },
            },
        };
        const res = calculate(transaction, true, 'POS');

        expect(res.serviceCharge).toBe(7.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(89.28);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.birDiscount).toBe(17.86);
        expect(res.display.serviceCharge).toBe(7.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(89.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,Bir-MEDAL_OF_VALOR ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'MEDAL_OF_VALOR',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    movId: 'h787h28yhf7yfs8813hd8',
                },
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(7.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(17.86);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(17.86);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(89.28);

        expect(res.display.subtotal).toBe(71.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(7.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(89.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-MEDAL_OF_VALOR, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'MEDAL_OF_VALOR',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(23.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(57.86);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(57.86);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(80);
        expect(res.zeroRatedSales).toBe(80);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(265.28);

        expect(res.display.subtotal).toBe(231.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(23.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(265.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,Bir-MEDAL_OF_VALOR, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'MEDAL_OF_VALOR',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(23.14);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(57.86);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(57.86);
        expect(res.taxableSales).toBe(71.43);
        expect(res.taxExemptedSales).toBe(80);
        expect(res.zeroRatedSales).toBe(80);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(265.28);

        expect(res.display.subtotal).toBe(231.43);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(23.14);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(265.28);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-DIPLOMAT ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'DIPLOMAT',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    dfaOrVicId: 'h787h28yhf7yfs8813hd8',
                    address: 'MY',
                },
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(8.93);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(89.29);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(98.22);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(8.93);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(98.22);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,Bir-DIPLOMAT ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'DIPLOMAT',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
                collectedInfo: {
                    name: 'John',
                    dfaOrVicId: 'h787h28yhf7yfs8813hd8',
                    address: 'MY',
                },
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(8.93);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(89.29);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(89.29);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(98.22);

        expect(res.display.subtotal).toBe(89.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(8.93);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(98.22);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:items, selectedOptions, ServiceCharge, BirFnb: idempotence ,Bir-DIPLOMAT, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'DIPLOMAT',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(28.93);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(100);
        expect(res.zeroRatedSales).toBe(189.29);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(318.22);

        expect(res.display.subtotal).toBe(289.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(28.93);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(318.22);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,Bir-DIPLOMAT, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'DIPLOMAT',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(28.93);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(0);
        expect(res.addonBirCompliance.athleteAndCoachDiscount).toBe(0);
        expect(res.addonBirCompliance.medalOfValorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(100);
        expect(res.zeroRatedSales).toBe(189.29);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(318.22);

        expect(res.display.subtotal).toBe(289.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(28.93);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(318.22);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,No BirDiscount ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: '',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(17.86);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(178.58);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance).toBe(undefined);
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(89.29);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(207.15);

        expect(res.display.subtotal).toBe(178.58);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(17.86);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(207.15);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,No BirDiscount ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: '',
                discountRate: 0.05,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(17.86);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(178.58);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.addonBirCompliance).toBe(undefined);
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(89.29);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(207.15);

        expect(res.display.subtotal).toBe(178.58);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(17.86);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(207.15);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirFnb: idempotence ,No BirDiscount, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: '',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(28.93);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(0);
        expect(res.addonBirCompliance).toBe(undefined);
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(100);
        expect(res.zeroRatedSales).toBe(100);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(328.93);

        expect(res.display.subtotal).toBe(289.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(28.93);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(328.93);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: idempotence ,No BirDiscount, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: '',
                discountRate: 0.05,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(28.93);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(0);
        expect(res.addonBirCompliance).toBe(undefined);
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(100);
        expect(res.zeroRatedSales).toBe(100);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(328.93);

        expect(res.display.subtotal).toBe(289.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(28.93);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(328.93);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, Full Bill Discount, ServiceCharge, BirRetail: idempotence ,No BirDiscount, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: '',
                discountRate: 0.05,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(14.46);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(144.65);
        expect(res.addonBirCompliance).toBe(undefined);
        expect(res.taxableSales).toBe(44.64);
        expect(res.taxExemptedSales).toBe(50);
        expect(res.zeroRatedSales).toBe(50);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(5.36);
        expect(res.total).toBe(164.46);

        expect(res.display.subtotal).toBe(289.28);
        expect(res.display.discount).toBe(-144.64);
        expect(res.display.serviceCharge).toBe(14.46);
        expect(res.display.tax).toBe(5.36);
        expect(res.display.total).toBe(164.46);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge, BirFnb, ZeroRated', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(54);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(60);
        expect(res.pwdDiscount).toBe(60);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(540);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(594);

        expect(res.display.subtotal).toBe(540);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(54);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(594);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge, BirFnb, ZeroRated: idempotence', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    selectedOptions: [
                        {
                            optionValue: 100,
                            quantity: 1,
                        },
                        {
                            optionValue: 50,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 50,
                    type: 'absolute',
                    taxRate: 0,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SC/PWD',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(54);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(600);
        expect(res.discount).toBe(60);
        expect(res.pwdDiscount).toBe(60);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(540);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(594);

        expect(res.display.subtotal).toBe(540);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(54);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(594);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:salesTakeawayCharges:Takeaway transaction', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    quantity: 2,
                    taxRate: 0,
                    isTakeaway: true,
                },
            ],
            salesChannel: 2,
            takeawayCharge: 1,
        };

        const res = calculate(transaction, true);

        expect(res.subtotal).toBe(602);
        expect(res.total).toBe(602);

        expect(res.display.subtotal).toBe(602);
        expect(res.display.total).toBe(602);
    });

    test('Func:calculate:salesTakeawayCharges:Takeaway transaction, lose takeawayCharge', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    quantity: 2,
                    taxRate: 0,
                    isTakeaway: true,
                },
            ],
            salesChannel: 2,
        };

        const res = calculate(transaction, true);

        expect(res.subtotal).toBe(600);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(600);
        expect(res.display.total).toBe(600);
    });

    test('Func:calculate:salesTakeawayCharges:Takeaway item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 300,
                    quantity: 1,
                    taxRate: 0,
                    itemChannel: 2,
                    isTakeaway: true,
                },
                {
                    unitPrice: 300,
                    quantity: 1,
                    taxRate: 0,
                    itemChannel: 0,
                    isTakeaway: false,
                },
            ],
            salesChannel: 0,
            takeawayCharge: 1,
        };

        const res = calculate(transaction, true);

        expect(res.subtotal).toBe(601);
        expect(res.total).toBe(601);

        expect(res.display.subtotal).toBe(601);
        expect(res.display.total).toBe(601);
    });

    test('Func:calculate:taxExclusiveDisplay:item, 100% discount, loyaltyDiscount', () => {
        const transaction = {
            channel: 3,
            userId: '2cf2ccc5-61f4-487a-9fe5-5dfe5bc90a75',
            sessionId: 'uhy45nuaV8xOf2Fe4Tm_dYKRr2oZ60xT',
            business: 'ck',
            total: 0,
            subtotal: 198,
            count: 1,
            tax: 0,
            items: [
                {
                    product: {
                        category: '厨房',
                        _id: '5a42240edd025b980bb4934b',
                        tags: ['CXG', 'Specialty'],
                    },
                    id: 'b033ef1e80baf854d16a63dea09bac04',
                    business: 'ck',
                    productId: '5a42240edd025b980bb4934b',
                    quantity: 1,
                    variations: [],
                    channel: 3,
                    tax: 0,
                    image: '',
                    total: 0,
                    taxCode: '5d08c8430055d90017ec7ad3',
                    taxRate: 0,
                    subtotal: 198,
                    unitPrice: 198,
                    displayPrice: 198,
                    variationTexts: [],
                    title: '1014 清蒸老虎班（时价)',
                    components: [],
                    inventoryType: '',
                    trackInventory: false,
                    isUnavailable: false,
                    markedSoldOut: false,
                    discount: 198,
                    subTotal: 198,
                    promotions: [
                        {
                            inputValue: 100,
                            type: 'percentage',
                            discountType: 'percentage',
                            promotionId: '5ed876ad5292ac30fefdb9af',
                            promotionCode: 'XXXXXX',
                            promotionName: 'xxxxxx',
                            discount: 198,
                            display: {
                                discount: 198,
                            },
                            quantity: 1,
                        },
                    ],
                    promotionAppliedQuantityMap: {
                        '5ed876ad5292ac30fefdb9af': 1,
                    },
                    adhocDiscount: 0,
                    display: {
                        total: 0,
                        subtotal: 198,
                        tax: 0,
                    },
                },
                {
                    itemType: 'ServiceCharge',
                    taxRate: 0,
                    taxCode: '',
                    rate: 0.1,
                    unitPrice: 0,
                    quantity: 1,
                    total: 0,
                    tax: 0,
                    discount: 0,
                    subTotal: 0,
                    adhocDiscount: 0,
                    display: {
                        total: 0,
                        subtotal: 0,
                        tax: 0,
                    },
                },
            ],
            unavailableItems: [],
            shippingFee: 0,
            shippingFeeDiscount: 0,
            businessSettings: {
                name: 'ck',
                timezone: 'Asia/Kuala_Lumpur',
                country: 'MY',
                includingTaxInDisplay: true,
            },
            shippingInfo: null,
            discount: 198,
            serviceCharge: 0,
            serviceChargeTax: 0,
            seniorDiscount: 0,
            pwdDiscount: 0,
            taxableSales: 0,
            taxExemptedSales: 0,
            zeroRatedSales: 0,
            totalDeductedTax: 0,
            loyaltyDiscounts: [
                {
                    loyaltyType: 'cashback',
                    inputValue: 12.1,
                    taxRate: 0,
                    type: 'cashback',
                    spentValue: 0,
                    display: {
                        discount: 0,
                        discountedTax: 0,
                    },
                    displayDiscount: 0,
                },
            ],
            display: {
                subtotal: 0,
                discount: 0,
                serviceCharge: 0,
                tax: 0,
                total: 0,
            },
        };
        const res = calculate(transaction, true);
        expect(res.total).toBe(0);
        expect(res.subtotal).toBe(198);

        expect(res.items).not.toEqual(
            expect.arrayContaining([
                expect.objectContaining({
                    loyaltyDiscount: expect.any(Number),
                }),
            ]),
        );
        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxExclusiveDisplay:item, freeShippingPromotion with maxDiscountAmount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    type: 'freeShipping',
                    maxDiscountAmount: 8,
                },
            ],
            shippingFee: 10,
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.subtotal).toBe(100);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(12);
        expect(res.total).toBe(114);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.tax).toBe(12);
        expect(res.display.shippingFee).toBe(2);
        expect(res.display.total).toBe(114);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, freeShippingPromotion with maxDiscountAmount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    type: 'freeShipping',
                    maxDiscountAmount: 8,
                },
            ],
            shippingFee: 10,
        };
        calculate(transaction, true);

        const res = calculate(transaction, true);

        expect(res.subtotal).toBe(100);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(12);
        expect(res.total).toBe(114);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.tax).toBe(12);
        expect(res.display.shippingFee).toBe(2);
        expect(res.display.total).toBe(114);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:salesFixedFee', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            fixedFee: 0.2,
        };
        calculate(transaction, false);

        const res = calculate(transaction, false);

        expect(res.subtotal).toBe(100);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(12);
        expect(res.fixedFee).toBe(0.2);
        expect(res.total).toBe(112.2);

        expect(res.display.subtotal).toBe(100);
        expect(res.display.discount).toBe(0);
        expect(res.display.tax).toBe(12);
        expect(res.display.fixedFee).toBe(0.2);
        expect(res.display.total).toBe(112.2);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:calculateLoyaltyDiscount, taxInclusive', () => {
        const transaction = {
            items: [],
            loyaltyDiscounts: [
                {
                    loyaltyType: 'cashback',
                    inputValue: 12.1,
                    taxRate: 0,
                    type: 'cashback',
                },
            ],
            subtotal: 125,
            tax: 0,
            total: 12.9,
        };

        calculateLoyaltyDiscount(transaction, true);

        expect(transaction.total).toBe(12.9);
        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculate:calculateLoyaltyDiscount, taxExclusive, taxExclusiveTransactionTotal is 0', () => {
        const transaction = {
            items: [],
            loyaltyDiscounts: [
                {
                    loyaltyType: 'cashback',
                    inputValue: 12.1,
                    taxRate: 0,
                    type: 'cashback',
                },
            ],
            subtotal: 125,
            tax: 0,
            total: 0,
        };

        calculateLoyaltyDiscount(transaction, false);

        expect(transaction.total).toBe(0);
        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculate:calculateLoyaltyDiscount, taxExclusive, without items', () => {
        const transaction = {
            items: [],
            loyaltyDiscounts: [
                {
                    loyaltyType: 'cashback',
                    inputValue: 12.1,
                    taxRate: 0,
                    type: 'cashback',
                },
            ],
            subtotal: 125,
            tax: 0,
            total: 12.9,
        };

        calculateLoyaltyDiscount(transaction, false);

        expect(transaction.total).toBe(12.9);
        expect(transaction).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirF&B: Solo Parent BirDiscount, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SOLO_PARENT',
                discountRate: 0.1,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(37.86);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(378.58);
        expect(res.discount).toBe(0);
        expect(res.addonBirCompliance).toEqual({
            athleteAndCoachDiscount: 0,
            discountType: 'SOLO_PARENT',
            medalOfValorDiscount: 0,
            soloParentDiscount: 0,
        });
        expect(res.taxableSales).toBe(178.58);
        expect(res.taxExemptedSales).toBe(100);
        expect(res.zeroRatedSales).toBe(100);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(21.42);
        expect(res.total).toBe(437.86);

        expect(res.display.subtotal).toBe(378.58);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(37.86);
        expect(res.display.tax).toBe(21.42);
        expect(res.display.total).toBe(437.86);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge with tax, BirF&B: Solo Parent BirDiscount, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SOLO_PARENT',
                discountRate: 0.1,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(37.86);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(378.58);
        expect(res.discount).toBe(0);
        expect(res.addonBirCompliance).toEqual({
            athleteAndCoachDiscount: 0,
            discountType: 'SOLO_PARENT',
            medalOfValorDiscount: 0,
            soloParentDiscount: 0,
        });
        expect(res.taxableSales).toBe(178.58);
        expect(res.taxExemptedSales).toBe(100);
        expect(res.zeroRatedSales).toBe(100);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(21.42);
        expect(res.total).toBe(437.86);

        expect(res.display.subtotal).toBe(378.58);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(37.86);
        expect(res.display.tax).toBe(21.42);
        expect(res.display.total).toBe(437.86);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, FullBillDiscount, BirF&B: Solo Parent BirDiscount, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    itemType: 'Discount',
                    inputValue: 100,
                    type: 'amount',
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SOLO_PARENT',
                discountRate: 0.1,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(378.58);
        expect(res.discount).toBe(0);
        expect(res.addonBirCompliance).toEqual({
            athleteAndCoachDiscount: 0,
            discountType: 'SOLO_PARENT',
            medalOfValorDiscount: 0,
            soloParentDiscount: 0,
        });
        expect(res.taxableSales).toBe(178.58);
        expect(res.taxExemptedSales).toBe(100);
        expect(res.zeroRatedSales).toBe(100);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(21.42);
        expect(res.total).toBe(400);

        expect(res.display.subtotal).toBe(378.58);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(21.42);
        expect(res.display.total).toBe(400);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: Solo Parent BirDiscount, Books & Donation ', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SOLO_PARENT',
                discountRate: 0.1,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(34.97);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(378.58);
        expect(res.discount).toBe(28.93);
        expect(res.addonBirCompliance).toEqual({
            athleteAndCoachDiscount: 0,
            discountType: 'SOLO_PARENT',
            medalOfValorDiscount: 0,
            soloParentDiscount: 28.93,
        });
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(170.36);
        expect(res.zeroRatedSales).toBe(90);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(395.33);

        expect(res.display.subtotal).toBe(349.65);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(34.97);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(395.33);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: Solo Parent BirDiscount, Books & Donation, source POS', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SOLO_PARENT',
                discountRate: 0.1,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true, 'POS');

        expect(res.serviceCharge).toBe(34.97);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(378.58);
        expect(res.discount).toBe(28.93);
        expect(res.addonBirCompliance).toEqual({
            athleteAndCoachDiscount: 0,
            discountType: 'SOLO_PARENT',
            medalOfValorDiscount: 0,
            soloParentDiscount: 28.93,
        });
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(170.36);
        expect(res.zeroRatedSales).toBe(90);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(395.33);

        expect(res.display.subtotal).toBe(378.58);
        expect(res.display.discount).toBe(0);
        expect(res.display.birDiscount).toBe(28.93);
        expect(res.display.serviceCharge).toBe(34.97);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(395.33);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, ServiceCharge, BirRetail: Solo Parent BirDiscount, Books & Donation , taxRate == 0', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    unitPrice: 44.64285716,
                    selectedOptions: [
                        {
                            optionValue: 14.8809524,
                            quantity: 1,
                        },
                        {
                            optionValue: 7.44047618,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.12,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                },
                {
                    unitPrice: 70,
                    selectedOptions: [
                        {
                            optionValue: 10,
                            quantity: 1,
                        },
                        {
                            optionValue: 5,
                            quantity: 4,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0,
                    isVatExempted: true,
                    isSoloParentDiscountApplicable: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SOLO_PARENT',
                discountRate: 0.1,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(54.97);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(578.58);
        expect(res.discount).toBe(28.93);
        expect(res.addonBirCompliance).toEqual({
            athleteAndCoachDiscount: 0,
            discountType: 'SOLO_PARENT',
            medalOfValorDiscount: 0,
            soloParentDiscount: 28.93,
        });
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(270.36);
        expect(res.zeroRatedSales).toBe(190);
        expect(res.totalDeductedTax).toBe(10.71);
        expect(res.tax).toBe(10.71);
        expect(res.total).toBe(615.33);

        expect(res.display.subtotal).toBe(549.65);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(54.97);
        expect(res.display.tax).toBe(10.71);
        expect(res.display.total).toBe(615.33);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: enable, conditionsMatch ALL, conditions', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                        tags: ['fast food', 'drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                        category: 'c2',
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c1',
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: true,
                        conditionsMatch: 'All',
                        conditions: [
                            {
                                type: 'productIds',
                                operator: 'in',
                                operand: ['pid1'],
                            },
                            {
                                type: 'categories',
                                operator: 'in',
                                operand: ['c1'],
                            },
                            {
                                type: 'tags',
                                operator: 'in',
                                operand: ['fast food', 'others'],
                            },
                        ],
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeTruthy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(14.02);
        expect(res.serviceChargeTax).toBe(1.68);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.23);
        expect(res.total).toBe(272.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(14.02);
        expect(res.display.tax).toBe(29.23);
        expect(res.display.total).toBe(272.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: enable, conditionsMatch Any, conditions , one condition with wrong type', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: true,
                        conditionsMatch: 'Any',
                        conditions: [
                            {
                                type: 'productIds',
                                operator: 'in',
                                operand: ['pid1'],
                            },
                            {
                                type: 'categories',
                                operator: 'in',
                                operand: ['c1'],
                            },
                            {
                                type: 'tags',
                                operator: 'in',
                                operand: ['fast food', 'others'],
                            },
                            {
                                type: 'other',
                                operator: 'in',
                                operand: ['fast food', 'others'],
                            },
                        ],
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeTruthy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeTruthy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeTruthy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(5.09);
        expect(res.serviceChargeTax).toBe(0.61);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(28.16);
        expect(res.total).toBe(262.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(5.09);
        expect(res.display.tax).toBe(28.16);
        expect(res.display.total).toBe(262.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: All, disale', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: false,
                        conditionsMatch: 'All',
                        conditions: [
                            {
                                type: 'productIds',
                                operator: 'in',
                                operand: ['pid1'],
                            },
                            {
                                type: 'categories',
                                operator: 'in',
                                operand: ['c1'],
                            },
                            {
                                type: 'tags',
                                operator: 'in',
                                operand: ['fast food', 'others'],
                            },
                        ],
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(18.48);
        expect(res.serviceChargeTax).toBe(2.22);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.77);
        expect(res.total).toBe(277.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(18.48);
        expect(res.display.tax).toBe(29.77);
        expect(res.display.total).toBe(277.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: Any, disale', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: false,
                        conditionsMatch: 'Any',
                        conditions: [
                            {
                                type: 'productIds',
                                operator: 'in',
                                operand: ['pid1'],
                            },
                            {
                                type: 'categories',
                                operator: 'in',
                                operand: ['c1'],
                            },
                            {
                                type: 'tags',
                                operator: 'in',
                                operand: ['fast food', 'others'],
                            },
                        ],
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(18.48);
        expect(res.serviceChargeTax).toBe(2.22);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.77);
        expect(res.total).toBe(277.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(18.48);
        expect(res.display.tax).toBe(29.77);
        expect(res.display.total).toBe(277.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition is undefined', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(18.48);
        expect(res.serviceChargeTax).toBe(2.22);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.77);
        expect(res.total).toBe(277.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(18.48);
        expect(res.display.tax).toBe(29.77);
        expect(res.display.total).toBe(277.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: Any, enable, without conditions', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: true,
                        conditionsMatch: 'Any',
                        conditions: null,
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(18.48);
        expect(res.serviceChargeTax).toBe(2.22);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.77);
        expect(res.total).toBe(277.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(18.48);
        expect(res.display.tax).toBe(29.77);
        expect(res.display.total).toBe(277.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: All, enable, without conditions', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: true,
                        conditionsMatch: 'All',
                        conditions: null,
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(18.48);
        expect(res.serviceChargeTax).toBe(2.22);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.77);
        expect(res.total).toBe(277.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(18.48);
        expect(res.display.tax).toBe(29.77);
        expect(res.display.total).toBe(277.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: wrong conditionsMatch', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: true,
                        conditionsMatch: 'Other',
                        conditions: [
                            {
                                type: 'productIds',
                                operator: 'in',
                                operand: ['pid1'],
                            },
                            {
                                type: 'categories',
                                operator: 'in',
                                operand: ['c1'],
                            },
                            {
                                type: 'tags',
                                operator: 'in',
                                operand: ['fast food', 'others'],
                            },
                        ],
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(18.48);
        expect(res.serviceChargeTax).toBe(2.22);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.77);
        expect(res.total).toBe(277.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(18.48);
        expect(res.display.tax).toBe(29.77);
        expect(res.display.total).toBe(277.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge,  excludeServiceChargeProductCondition: All, conditions, one condition with wrong type', () => {
        const transaction = {
            items: [
                // 0 service charge Not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        _id: 'pid1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 1 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        category: 'c1',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 2 service charge not Applicable
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    product: {
                        tags: ['fast food'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 83.3333333,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy8asd89a8989w',
                        },
                    ],
                },
                // 3 service charge Applicable
                {
                    unitPrice: 17.3482143,
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                        tags: ['drink'],
                    },
                    quantity: 1,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 20,
                        type: 'percent',
                    },
                    promotions: [
                        {
                            inputValue: 10,
                            type: 'percentage',
                            quantity: 1,
                            promotionId: '7dd77sdy95sd89a8989w',
                        },
                    ],
                },
                // 4 service charge Applicable
                {
                    unitPrice: 89.2857142857,
                    product: {
                        _id: 'pid3',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                // 5 service charge not Applicable
                {
                    unitPrice: 89.2857142857,
                    isTakeaway: true,
                    product: {
                        _id: 'pid4',
                    },
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                    excludeServiceChargeProductCondition: {
                        isEnabled: true,
                        conditionsMatch: 'All',
                        conditions: [
                            {
                                type: 'productIds',
                                operator: 'in',
                                operand: ['pid1'],
                            },
                            {
                                type: 'categories',
                                operator: 'in',
                                operand: ['c1'],
                            },
                            {
                                type: 'other',
                                operator: 'in',
                                operand: ['fast food', 'others'],
                            },
                        ],
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.items[0].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[1].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[2].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[3].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[4].isServiceChargeNotApplicable).toBeFalsy();
        expect(res.items[5].isServiceChargeNotApplicable).toBeTruthy();

        expect(res.serviceCharge).toBe(18.48);
        expect(res.serviceChargeTax).toBe(2.22);
        expect(res.subtotal).toBe(1803.06);
        expect(res.discount).toBe(1573.62);
        expect(res.taxableSales).toBe(0);
        expect(res.taxExemptedSales).toBe(0);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(29.77);
        expect(res.total).toBe(277.69);

        expect(res.display.subtotal).toBe(458.9);
        expect(res.display.discount).toBe(-229.46);
        expect(res.display.serviceCharge).toBe(18.48);
        expect(res.display.tax).toBe(29.77);
        expect(res.display.total).toBe(277.69);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, ServiceCharge, BirRetail, no isBasicNecessitiesPH, no SC/PWD count, amusementTax', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 89.2857142,
                    quantity: 1,
                    taxRate: 0.12,
                    taxCode: 'anud89d8asd78ydh7dy',
                },
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0,
                    taxCode: 'anud89d8asd78ydh7dy88',
                    isVatExempted: true,
                },
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0.1,
                    taxCode: 'anud89d8asd78ydh766',
                    isVatExempted: true,
                    isAmusementTax: true,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxCode: '89adysd78asydasd7d3',
                    taxRate: 0,
                },
            ],
            birInfo: {
                type: 'retail',
                discountType: 'SC/PWD',
                discountRate: 0.05,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };

        const res = calculate(transaction, true);
        expect(res.serviceCharge).toBe(28.93);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(289.29);
        expect(res.discount).toBe(0);
        expect(res.pwdDiscount).toBe(0);
        expect(res.seniorDiscount).toBe(0);
        expect(res.taxableSales).toBe(89.29);
        expect(res.taxExemptedSales).toBe(210);
        expect(res.zeroRatedSales).toBe(0);
        expect(res.totalDeductedTax).toBe(0);
        expect(res.tax).toBe(20.71);
        expect(res.total).toBe(338.93);

        expect(res.display.subtotal).toBe(289.29);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(28.93);
        expect(res.display.tax).toBe(20.71);
        expect(res.display.total).toBe(338.93);

        expect(res.calculation).toEqual({
            discounts: [],
            original: {
                subtotal: 289.29,
                tax: 20.71,
                total: 338.93,
            },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 10.71,
                    taxCode: 'anud89d8asd78ydh7dy',
                    taxRate: 0.12,
                },
                {
                    isAmusementTax: false,
                    isVatExempted: true,
                    tax: 0,
                    taxCode: 'anud89d8asd78ydh7dy88',
                    taxRate: 0,
                },
                {
                    isAmusementTax: true,
                    isVatExempted: true,
                    tax: 10,
                    taxCode: 'anud89d8asd78ydh766',
                    taxRate: 0.1,
                },
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 0,
                    taxCode: '89adysd78asydasd7d3',
                    taxRate: 0,
                },
            ],
        });

        expect(res.items[0].calculation).toEqual({
            discounts: [],
            fullPrice: 89.2857142,
            original: {
                subtotal: 89.29,
                tax: 10.71,
                total: 100,
            },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 10.71,
                    taxCode: 'anud89d8asd78ydh7dy',
                    taxRate: 0.12,
                },
            ],
        });

        expect(res.items[1].calculation).toEqual({
            discounts: [],
            fullPrice: 100,
            original: {
                subtotal: 100,
                tax: 0,
                total: 100,
            },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: true,
                    tax: 0,
                    taxCode: 'anud89d8asd78ydh7dy88',
                    taxRate: 0,
                },
            ],
        });

        expect(res.items[2].calculation).toEqual({
            discounts: [],
            fullPrice: 100,
            original: {
                subtotal: 100,
                tax: 10,
                total: 110,
            },
            taxes: [
                {
                    isAmusementTax: true,
                    isVatExempted: true,
                    tax: 10,
                    taxCode: 'anud89d8asd78ydh766',
                    taxRate: 0.1,
                },
            ],
        });

        expect(res.items[3].calculation).toEqual({
            discounts: [],
            fullPrice: 28.929,
            original: {
                subtotal: 28.93,
                tax: 0,
                total: 28.93,
            },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 0,
                    taxCode: '89adysd78asydasd7d3',
                    taxRate: 0,
                },
            ],
        });

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, fullbillDiscount, no tax, test the rounding issue', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 850,
                    quantity: 1,
                    taxRate: 0,
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 1250,
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    itemType: 'Discount',
                    inputValue: 100,
                    type: 'amount',
                    taxRate: 0,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2100);
        expect(res.discount).toBe(600);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(1500);

        expect(res.display.subtotal).toBe(1600);
        expect(res.display.discount).toBe(-100);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(1500);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item, selectedOptions, itemDiscount, cashback discount, no tax, test the rounding issue', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 850,
                    quantity: 1,
                    taxRate: 0,
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
                {
                    unitPrice: 1250,
                    quantity: 1,
                    taxRate: 0,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
            ],
            loyaltyDiscounts: [
                {
                    loyaltyType: 'cashback',
                    inputValue: 100,
                    taxRate: 0,
                    type: 'cashback',
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(2100);
        expect(res.discount).toBe(600);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(1500);

        expect(res.display.subtotal).toBe(1600);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(1500);

        expect(res).toMatchSnapshot();
    });

    test('Func:calculate:taxInclusiveDisplay:item multiple, selectedOptions, comboPromotion, store credit', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 30,
                    quantity: 1,
                    taxRate: 0,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 30,
                    quantity: 1,
                    taxRate: 0,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
                {
                    unitPrice: 30,
                    quantity: 1,
                    taxRate: 0,
                    promotionAppliedQuantityMap: { test1: 1 },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 85,
                    type: 'combo',
                    quantity: 1,
                    taxRate: 0,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 1,
                    type: 'storeCredit',
                    taxRate: 0,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.loyaltyDiscounts[0].display.discount).toBe(1);
        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(90);
        expect(res.discount).toBe(6);
        expect(res.tax).toBe(0);
        expect(res.total).toBe(84);
        expect(res.display.subtotal).toBe(85);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(0);
        expect(res.display.total).toBe(84);

        expect(res).toMatchSnapshot();
    });
});
