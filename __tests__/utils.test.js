import * as utils from '../src/utils';

describe('utils:jest', () => {
    test('Func:sortConditions:sorted', () => {
        const promotion = {
            _id: '60c18c1c12fd8001ba2c36fa',
            validDays: null,
            appliedStores: ['All'],
            appliedSources: [8, 7, 6, 5],
            storehubPaidPercentage: 0,
            type: 'universal',
            appliedClientTypes: ['web'],
            business: 'storehub-universal-promo-code',
            name: 'Test_code',
            ordering: 0,
            isEnabled: true,
            isDeleted: false,
            discountType: 'absolute',
            promotionCode: 'A12345',
            enableClaimLimit: false,
            enablePerCustomerClaimLimit: false,
            costPerSMS: 0.1,
            totalSMSSent: 100,
            conditions: [
                {
                    operand: ['MY'],
                    _id: '60c18c1c12fd80709c2c36fb',
                    entity: 'business',
                    propertyName: 'country',
                    operator: 'in',
                },
                {
                    operand: ['200'],
                    _id: '60c18c1c12fd80e22f2c36fc',
                    entity: 'transaction',
                    propertyName: 'subtotal',
                    operator: 'gte',
                },
                {
                    operand: ['BEEP'],
                    _id: '60c18c1c12fd80eb872c36fd',
                    entity: 'business',
                    propertyName: 'qrOrderingSettings.marketingTags',
                    operator: 'contains',
                },
            ],
            enableBeepTagAndBanner: false,
            maxDiscountAmount: null,
            discountValue: 5,
            createdBy: '<EMAIL>',
            requiredProducts: [],
            createdTime: '2021-06-10T03:50:52.288Z',
            modifiedTime: '2021-06-10T03:50:52.288Z',
            __v: 0,
        };
        const sortedConditions = utils.sortConditions(promotion.conditions);
        const orderingList = sortedConditions.map(c => `${c.entity}.${c.propertyName}`);
        for (let i = 0; i < utils.conditionsOrdering.length - 1; i += 1) {
            expect(orderingList.indexOf(utils.conditionsOrdering[i])).toBeLessThan(
                orderingList.indexOf(utils.conditionsOrdering[i + 1]),
            );
        }
        expect(sortedConditions.length).toEqual(promotion.conditions.length);
        for (const item of promotion.conditions) {
            const tmp = sortedConditions.find(c => c._id === item._id);
            expect(tmp).toBeTruthy();
        }
    });
    test('Func:sortConditions:condition is an empty array', () => {
        const promotion = {
            _id: '60c18c1c12fd8001ba2c36fa',
            validDays: null,
            appliedStores: ['All'],
            appliedSources: [8, 7, 6, 5],
            storehubPaidPercentage: 0,
            type: 'universal',
            appliedClientTypes: ['web'],
            business: 'storehub-universal-promo-code',
            name: 'Test_code',
            ordering: 0,
            isEnabled: true,
            isDeleted: false,
            discountType: 'absolute',
            promotionCode: 'A12345',
            enableClaimLimit: false,
            enablePerCustomerClaimLimit: false,
            costPerSMS: 0.1,
            totalSMSSent: 100,
            conditions: [],
            enableBeepTagAndBanner: false,
            maxDiscountAmount: null,
            discountValue: 5,
            createdBy: '<EMAIL>',
            requiredProducts: [],
            createdTime: '2021-06-10T03:50:52.288Z',
            modifiedTime: '2021-06-10T03:50:52.288Z',
            __v: 0,
        };
        const sortedConditions = utils.sortConditions(promotion.conditions);
        expect(sortedConditions.length).toEqual(promotion.conditions.length);
        expect(sortedConditions.length).toEqual(0);
    });

    test('Func:sortConditions:condition random array', () => {
        const promotion = {
            _id: '60c18c1c12fd8001ba2c36fa',
            validDays: null,
            appliedStores: ['All'],
            appliedSources: [8, 7, 6, 5],
            storehubPaidPercentage: 0,
            type: 'universal',
            appliedClientTypes: ['web'],
            business: 'storehub-universal-promo-code',
            name: 'Test_code',
            ordering: 0,
            isEnabled: true,
            isDeleted: false,
            discountType: 'absolute',
            promotionCode: 'A12345',
            enableClaimLimit: false,
            enablePerCustomerClaimLimit: false,
            costPerSMS: 0.1,
            totalSMSSent: 100,
            conditions: [
                {
                    operand: ['MY'],
                    _id: '60c18c1c12fd80709c2c36fb',
                    entity: 'business',
                    propertyName: 'country',
                    operator: 'in',
                },
            ],
            enableBeepTagAndBanner: false,
            maxDiscountAmount: null,
            discountValue: 5,
            createdBy: '<EMAIL>',
            requiredProducts: [],
            createdTime: '2021-06-10T03:50:52.288Z',
            modifiedTime: '2021-06-10T03:50:52.288Z',
            __v: 0,
        };
        const sortedConditions = utils.sortConditions(promotion.conditions);
        expect(sortedConditions.length).toEqual(promotion.conditions.length);
        expect(sortedConditions.length).toEqual(1);
    });

    test('Func:div', () => {
        const result = utils.div(3.5, 1.12);
        expect(result).toEqual(3.125);
    });

    test('Func:div2Fixed', () => {
        const result = utils.div2Fixed(3.5, 1.12);
        expect(result).toEqual(3.13);
    });

    test('Func:div2Fixed with invalid Number', () => {
        const result = utils.div2Fixed(3.5, null);
        expect(result).toEqual(3.5);
    });

    test('Func:mul', () => {
        const result = utils.mul(100, 100);
        expect(result).toEqual(10000);
    });

    test('Func:mul2Fixed', () => {
        const result = utils.mul2Fixed(1.222222, 1.333333);
        expect(result).toEqual(1.63);
    });

    test('Func:mul2Fixed with invalid Number', () => {
        const result = utils.mul2Fixed(1.222222, 1.333333, null);
        expect(result).toEqual(0);
    });

    test('Func:mul2Fixed with none params', () => {
        const result = utils.mul2Fixed();
        expect(result).toEqual(0);
    });

    test('Func:add', () => {
        const result = utils.add(1.222222, 1.333333);
        expect(result).toEqual(2.555555);
    });

    test('Func:add2Fixed', () => {
        const result = utils.add2Fixed(1.222222, 1.333333);
        expect(result).toEqual(2.56);
    });

    test('Func:add2Fixed with invalid Number', () => {
        const result = utils.add2Fixed(1.222222, 1.333333, null);
        expect(result).toEqual(2.56);
    });

    test('Func:add2Fixed with none params', () => {
        const result = utils.add2Fixed();
        expect(result).toEqual(0);
    });

    test('Func:sub', () => {
        const result = utils.sub(2.333333, 1.222222);
        expect(result).toEqual(1.111111);
    });

    test('Func:sub2Fixed', () => {
        const result = utils.sub2Fixed(2.333333, 1.222222);
        expect(result).toEqual(1.11);
    });

    test('Func:sub2Fixed with invalid Number', () => {
        const result = utils.sub2Fixed(2.333333, 1.222222, null);
        expect(result).toEqual(1.11);
    });

    test('Func:sub2Fixed with none params', () => {
        const result = utils.sub2Fixed();
        expect(result).toEqual(0);
    });

    test('Func:roundTo2DecimalPlaces <=4', () => {
        const result = utils.roundTo2DecimalPlaces(1.14486);
        expect(result).toEqual(1.14);
    });

    test('Func:roundTo2DecimalPlaces >=5', () => {
        const result = utils.roundTo2DecimalPlaces(1.15523);
        expect(result).toEqual(1.16);
    });
});
