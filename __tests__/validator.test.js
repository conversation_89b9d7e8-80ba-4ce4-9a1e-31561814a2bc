import validate from '../src/validator';

describe('validator:jest', () => {
    test('Func:validate:Pass Validation', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                    promotionAppliedQuantityMap: {
                        test1: 1,
                    },
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 100,
                    type: 'combo',
                    quantity: 1,
                },
            ],
        };
        const result = validate(transaction);
        expect(result).toBe(null);
    });

    test('Func:validate:Transaction must include "items" field', () => {
        const transaction = {};
        const error = validate(transaction);
        expect(error.message).toBe('Transaction must include "items" field');
    });

    test('Func:validate:Transaction field "items" should be Array', () => {
        const transaction = {
            items: 'string',
        };
        const error = validate(transaction);
        expect(error.message).toBe('Transaction field "items" should be Array');
    });

    test('Func:validate:The items.itemType === "other" cannot be accept, only can be undefined, "Discount" and "ServiceCharge"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'other',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The items.itemType === "other" cannot be accept, only can be undefined, "Discount" and "ServiceCharge"',
        );
    });

    test('Func:validate:Transaction field "items" should not only include "Discount" or "ServiceCharge" without product items', () => {
        const transaction = {
            items: [
                {
                    itemType: 'Discount',
                },
                {
                    itemType: 'ServiceCharge',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'Transaction field "items" should not only include "Discount" or "ServiceCharge" without product items',
        );
    });

    test('Func:validate:Discount item field "inputValue" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 'string',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'Discount item field "inputValue" is required and should be Number',
        );
    });

    test('Func:validate:validateDiscountItem no error', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 10,
                    taxRate: 0.12,
                    type: 'amount',
                    inputValue: 0,
                },
            ],
        };
        const error = validate(transaction);
        expect(error).toBeNull();
    });

    test('Func:validate:Discount item field "type" is required', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 10,
                    taxRate: 0.12,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('Discount item field "type" is required');
    });

    test('Func:validate:The discountItem.type === "other" cannot be accept, only can be "amount" and "percent"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 10,
                    taxRate: 0.12,
                    type: 'other',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The discountItem.type === "other" cannot be accept, only can be "amount" and "percent"',
        );
    });

    test('Func:validate:Only able to have ONE discount item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'Discount',
                    inputValue: 10,
                    taxRate: 0.12,
                    type: 'amount',
                },
                {
                    itemType: 'Discount',
                    inputValue: 10,
                    taxRate: 0.12,
                    type: 'amount',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('Only able to have ONE discount item');
    });

    test('Func:validate:Service Charge item field "rate" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 'string',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'Service Charge item field "rate" is required and should be Number',
        );
    });

    test('Func:validate:Service Charge item field "taxRate" should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 'string',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('Service Charge item field "taxRate" should be Number');
    });

    test('Func:validate:Service Charge item field "taxRate" should be 0 in PH', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const error = validate(transaction);
        expect(error.message).toBe('Service Charge item field "taxRate" should be 0 in PH');
    });

    test('Func:validate:Only able to have ONE Service Charge item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('Only able to have ONE Service Charge item');
    });

    test('Func:validate:Product item field "unitPrice" is required and should be Number', () => {
        const transaction = {
            items: [{}],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'Product item field "unitPrice" is required and should be Number',
        );
    });

    test('Func:validate:Product item field "quantity" is required and should be Number > 0', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: -2,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'Product item field "quantity" is required and should be Number > 0',
        );
    });

    test('Func:validate:Product item field "taxRate" should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 2,
                    taxRate: 'string',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('Product item field "taxRate" should be Number');
    });

    test('Func:validate:"isAmusementTax" must be based on "isVatExempted"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 2,
                    taxRate: 0.12,
                    isAmusementTax: true,
                    isVatExempted: false,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('"isAmusementTax" must be based on "isVatExempted"');
    });

    test('Func:validate:The item.selectedOptions field "optionValue" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 'string',
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.selectedOptions field "optionValue" is required and should be Number',
        );
    });

    test('Func:validate:The item.selectedOptions field "quantity" is required and should be Number >= 1', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.selectedOptions field "quantity" is required and should be Number >= 1',
        );
    });

    test('Func:validate: until selectedOptions correct', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                    ],
                    quantity: 2,
                    taxRate: 0.12,
                },
            ],
        };
        const error = validate(transaction);
        expect(error).toBeNull();
    });

    test('Func:validate:The item.itemLevelDiscount field "inputValue" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 'string',
                    },
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.itemLevelDiscount field "inputValue" is required and should be Number',
        );
    });

    test('Func:validate:The item.itemLevelDiscount field "type" is required', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                    },
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('The item.itemLevelDiscount field "type" is required');
    });

    test('Func:validate:The item.itemLevelDiscount.type === "other" cannot be accept, only can be "amount" and "percent"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'other',
                    },
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.itemLevelDiscount.type === "other" cannot be accept, only can be "amount" and "percent"',
        );
    });

    test('Func:validate: until itemLevelDiscount correct', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 2,
                    taxRate: 0.12,
                    itemLevelDiscount: {
                        inputValue: 500,
                        type: 'amount',
                    },
                },
            ],
        };
        const error = validate(transaction);
        expect(error).toBeNull();
    });

    test('Func:validate:The item.promotions field "inputValue" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 'string',
                            type: 'fixedUnitPrice',
                        },
                    ],
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.promotions field "inputValue" is required and should be Number',
        );
    });

    test('Func:validate:The item.promotions must include "type" field', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 89.2857143,
                        },
                    ],
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('The item.promotions must include "type" field');
    });

    test('Func:validate:The item.promotion.type === "other" cannot be accept, only can be "percentage", "fixedUnitPrice" and "buyXFreeY"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 1,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 89.2857143,
                            type: 'other',
                        },
                    ],
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.promotion.type === "other" cannot be accept, only can be "percentage", "fixedUnitPrice" and "buyXFreeY"',
        );
    });

    test('Func:validate: until item promotions correct', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 267.857143,
                    quantity: 2,
                    taxRate: 0.12,
                    promotions: [
                        {
                            inputValue: 89.2857143,
                            type: 'percentage',
                        },
                    ],
                },
            ],
        };
        const error = validate(transaction);
        expect(error).toBeNull();
    });

    test('Func:validate:The transaction.promotions field "inputValue" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [{}],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The transaction.promotions field "inputValue" is required and should be Number',
        );
    });

    test('Func:validate: until item return correct', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 100,
                    quantity: 2,
                    taxRate: 0,
                    return: {
                        total: 200,
                        quantity: 2,
                    },
                },
            ],
        };
        const error = validate(transaction);
        expect(error).toBeNull();
    });

    test('Func:validate:The transaction.promotions must include "type" field', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    inputValue: 100,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('The transaction.promotions must include "type" field');
    });

    test('Func:validate:The transaction.promotion.type === "other" cannot be accept, only can be "absolute", "combo" and "freeShipping"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    inputValue: 100,
                    type: 'other',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The transaction.promotion.type === "other" cannot be accept, only can be "absolute", "combo" and "freeShipping"',
        );
    });

    test('Func:validate:The transaction.promotions field "quantity" is required and should be Number when combo', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    inputValue: 100,
                    type: 'combo',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The transaction.promotions field "quantity" is required and should be Number when combo',
        );
    });

    test('Func:validate:The transaction.promotions field "promotionId" is required', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    inputValue: 100,
                    type: 'combo',
                    quantity: 1,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('The transaction.promotions field "promotionId" is required');
    });

    test('Func:validate:At least one item in field "promotionAppliedQuantityMap" is required and should be Number when combo', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    promotionId: 'test1',
                    inputValue: 100,
                    type: 'combo',
                    quantity: 1,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'At least one item in field "promotionAppliedQuantityMap" is required and should be Number when combo',
        );
    });

    test('Func:validate:freeShipping promotion without inputValue', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            promotions: [
                {
                    type: 'freeShipping',
                },
            ],
        };
        expect(validate(transaction)).toBe(null);
    });

    test('Func:validate:The transaction.loyaltyDiscounts field "inputValue" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [{}],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The transaction.loyaltyDiscounts field "inputValue" is required and should be Number',
        );
    });

    test('Func:validate: validateLoyaltyDiscount no erroe', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 10,
                    type: 'cashback',
                    taxRate: 0.12,
                },
            ],
        };
        const error = validate(transaction);
        expect(error).toBeNull();
    });

    test('Func:validate:The transaction.loyaltyDiscounts must include "type" field', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 100,
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('The transaction.loyaltyDiscounts must include "type" field');
    });

    test('Func:validate:The transaction.loyaltyDiscounts.type === "other" cannot be accept, only can be "storeCredit" and "cashback"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            loyaltyDiscounts: [
                {
                    inputValue: 100,
                    type: 'other',
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The transaction.loyaltyDiscounts.type === "other" cannot be accept, only can be "storeCredit" and "cashback"',
        );
    });

    test('Func:validate:The item.return field "quantity" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                    return: {},
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.return field "quantity" is required and should be Number',
        );
    });

    test('Func:validate:The item.return field "quantity" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                    return: {},
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe(
            'The item.return field "quantity" is required and should be Number',
        );
    });

    test('Func:validate:The item.return field "quantity" cannot be 0', () => {
        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                    return: {
                        quantity: 0,
                    },
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('The item.return field "quantity" cannot be 0');
    });

    test('Func:validate:The item.return field "total" should be Number', () => {
        const transaction = {
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                    return: {
                        quantity: 1,
                        total: 'test',
                    },
                },
            ],
        };
        const error = validate(transaction);
        expect(error.message).toBe('The item.return field "total" should be Number');
    });

    test('Func:validate:The transaction.birInfo only works with tax inclusive display', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const error = validate(transaction, false);
        expect(error.message).toBe('The transaction.birInfo only works with tax inclusive display');
    });

    test('Func:validate:The transaction.birInfo must include "type" field', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe('The transaction.birInfo must include "type" field');
    });

    test('Func:validate:The transaction.birInfo.type === "other" cannot be accept, only can be "retail" and "f&b"', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'other',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo.type === "other" cannot be accept, only can be "retail" and "f&b"',
        );
    });

    test('Func:validate:The transaction.birInfo field "discountRate" is required and should be Number', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "discountRate" is required and should be Number',
        );
    });

    test('Func:validate: wrong type', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'null',
                seniorsCount: 0,
                discountRate: 0.2,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo.type === "null" cannot be accept, only can be "retail" and "f&b"',
        );
    });

    test('Func:validate:The transaction.birInfo field "headCount" is required and should be a Number > 0', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                seniorsCount: 0,
                discountRate: 0.2,
                pwdCount: 1,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "headCount" is required and should be a Number > 0',
        );
    });

    test('Func:validate:The transaction.birInfo field "pwdCount" is required and should be a Number > 0', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: -1,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "pwdCount" is required and should be a Number > 0',
        );
    });

    test('Func:validate:The transaction.birInfo field "seniorsCount" is required and should be a Number > 0', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: -1,
                pwdCount: 0,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "seniorsCount" is required and should be a Number > 0',
        );
    });

    test('Func:validate:The transaction.birInfo field "seniorsCount" + "pwdCount" should be equal or less than headCount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 2,
                pwdCount: 1,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "seniorsCount" + "pwdCount" should be equal or less than headCount',
        );
    });

    test('Func:validate:The transaction.birInfo field "pwdCount" is required and should be a Number > 0 and <= 1', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'retail',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 2,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "pwdCount" is required and should be a Number > 0 and <= 1',
        );
    });

    test('The transaction.birInfo field "seniorsCount" is required and should be a Number > 0 and <= 1', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'retail',
                discountRate: 0.2,
                seniorsCount: 2,
                pwdCount: 0,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "seniorsCount" is required and should be a Number > 0 and <= 1',
        );
    });

    test('The transaction.birInfo field "headCount" is required and should be a Number > 0  and <= 1', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'retail',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 2,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "headCount" is required and should be a Number > 0  and <= 1',
        );
    });

    test('The solo parent discount only support Retail', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountType: 'SOLO_PARENT',
                discountRate: 0.1,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe('The solo parent discount only support Retail');
    });

    test('The transaction.birInfo field "seniorsCount" and "pwdCount" should not exist simultaneously', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
            birInfo: {
                type: 'retail',
                discountRate: 0.2,
                seniorsCount: 1,
                pwdCount: 1,
                headCount: 1,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.birInfo field "seniorsCount" and "pwdCount" should not exist simultaneously',
        );
    });

    test('Func:validate:The transaction.purchaseItem field "taxRate" is required and only can be 0 or 0.12 when calculate BIR', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.06,
                },
            ],
            birInfo: {
                type: 'retail',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 1,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.purchaseItem field "taxRate" is required and only can be 0 or 0.12 when calculate BIR',
        );
    });

    test('Func:validate:The transaction.purchaseItem field "taxRate" is required and only can be 0 or 0.12 when calculate BIR', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.06,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 2,
                pwdCount: 1,
                headCount: 3,
            },
        };
        const error = validate(transaction, true);
        expect(error.message).toBe(
            'The transaction.purchaseItem field "taxRate" is required and only can be 0 or 0.12 when calculate BIR',
        );
    });

    test('Func:validate:The transaction.purchaseItem field "taxRate" is not required in ServiceCharge when calculate BIR', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 2,
                pwdCount: 1,
                headCount: 3,
            },
        };
        const result = validate(transaction, true);
        expect(result).toBe(null);
    });
});
