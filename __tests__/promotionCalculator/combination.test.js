import combination from '../../src/promotionCalculator/combination';

describe('promotion:combination', () => {
    describe('getBestComboItems()', () => {
        test('one condition', () => {
            const promotion = {
                conditions: [
                    {
                        operand: ['c1'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: null,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 2,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
                {
                    id: 'id3',
                    availableQuantity: 2,
                    unitPrice: 3,
                    productId: 'pid3',
                    product: {
                        _id: 'pid3',
                        category: 'c1',
                    },
                },

                {
                    id: 'id4',
                    availableQuantity: 2,
                    unitPrice: 4,
                    productId: 'pid4',
                    product: {
                        _id: 'pid4',
                        category: 'c2',
                    },
                },
            ];

            const bestCombo = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestCombo).toEqual({ id1: 1, id3: 2 });
        });

        test('multiple conditions', () => {
            const promotion = {
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        _id: 'cond1',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 3,
                        maxQuantity: 3,
                    },
                    {
                        operand: ['c3'],
                        _id: 'cond2',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: null,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    availableQuantity: 2,
                    unitPrice: 2,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
                {
                    id: 'id3',
                    availableQuantity: 3,
                    unitPrice: 3,
                    productId: 'pid3',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
            ];
            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({ id3: 3, id1: 1, id2: 2 });
        });

        test('missing items satisfied all conditions', () => {
            const promotion = {
                minQuantity: 2,
                maxQuantity: 4,
                conditions: [
                    {
                        operand: ['c1', 'c3'],
                        _id: '5ce27b8ae1ee6624f1c34a2a',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: null,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 10,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 10,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ];
            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({});
        });

        test('promotion quantity config invalid', () => {
            const promotion = {
                minQuantity: 0,
                conditions: [
                    {
                        operand: ['c1', 'c3'],
                        _id: '5ce27b8ae1ee6624f1c34a2a',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 10,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 10,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ];
            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({});
        });

        test('get min amount combo', () => {
            const promotion = {
                discountType: 'buyXFreeY',
                discountValue: 1,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        _id: '5ce27b8ae1ee6624f1c34a2a',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 3,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 2,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
                {
                    id: 'id3',
                    availableQuantity: 1,
                    unitPrice: 3,
                    productId: 'pid3',
                    product: {
                        _id: 'pid3',
                        category: 'c1',
                    },
                },

                {
                    id: 'id4',
                    availableQuantity: 1,
                    unitPrice: 4,
                    productId: 'pid4',
                    product: {
                        _id: 'pid4',
                        category: 'c2',
                    },
                },
            ];
            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({
                id1: 1,
                placeholderItemIds: {
                    id2: 1,
                    id3: 1,
                },
            });
        });

        test('get min amount combo with variation', () => {
            const promotion = {
                discountType: 'buyXFreeY',
                discountValue: 1,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        _id: '5ce27b8ae1ee6624f1c34a2a',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 3,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                    selectedOptions: [
                        {
                            optionValue: 2,
                            quantity: 1,
                        },
                    ],
                },

                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 2,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                    selectedOptions: [
                        {
                            quantity: 1,
                        },
                    ],
                },
                {
                    id: 'id3',
                    availableQuantity: 1,
                    unitPrice: 3,
                    productId: 'pid3',
                    product: {
                        _id: 'pid3',
                        category: 'c1',
                    },
                },

                {
                    id: 'id4',
                    availableQuantity: 1,
                    unitPrice: 4,
                    productId: 'pid4',
                    product: {
                        _id: 'pid4',
                        category: 'c2',
                    },
                },
            ];
            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({
                id2: 1,
                placeholderItemIds: {
                    id1: 1,
                    id3: 1,
                },
            });
        });

        test('no items', () => {
            const promotion = {
                minQuantity: 1,
                maxQuantity: 2,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        _id: '5ce27b8ae1ee6624f1c34a2a',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: null,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [];

            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({});
        });

        test('have none-purchase items', () => {
            const promotion = {
                minQuantity: 1,
                maxQuantity: 2,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        _id: '5ce27b8ae1ee6624f1c34a2a',
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: null,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    itemType: 'Discount',
                    inputValue: 50,
                    type: 'percent',
                    taxRate: 0.12,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.12,
                },
            ];

            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({});
        });

        test('multiple items satisfied one condition', () => {
            const promotion = {
                conditions: [
                    {
                        operand: ['c1'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 3,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id3',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ];

            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({ id1: 1, id2: 1, id3: 1 });
        });

        test('condition quantity not satisfied', () => {
            const promotion = {
                conditions: [
                    {
                        operand: ['c1'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 3,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 1,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ];

            const bestComboItems = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestComboItems).toEqual({});
        });

        test('second condition not enough item to satisfy', () => {
            const promotion = {
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 2,
                        maxQuantity: null,
                    },
                    {
                        operand: ['c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 1,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    availableQuantity: 1,
                    unitPrice: 10,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id2',
                    availableQuantity: 1,
                    unitPrice: 10,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ];
            const bestCombo = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestCombo).toEqual({});
        });

        test('items quantity is exceeded than promotion max quantity ', () => {
            const promotion = {
                maxQuantity: 2,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 1,
                        maxQuantity: null,
                    },
                ],
            };

            const items = [
                {
                    id: 'id1',
                    quantity: 3,
                    availableQuantity: 3,
                    unitPrice: 10,
                    productId: 'pid1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ];
            const bestCombo = combination.getBestCombo(items, promotion).bestCombo;
            expect(bestCombo).toEqual({});
        });
    });
});
