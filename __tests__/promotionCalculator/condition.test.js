import conditionCalculator from '../../src/promotionCalculator/conditionCalculator';

describe('promotion:conditionCalculator', () => {
    describe('operator:gte', () => {
        const getCondition = (entity, propertyName, operand) => {
            return {
                operand,
                entity,
                propertyName,
                operator: 'gte',
            };
        };
        const getTransaction = total => {
            return { total };
        };

        test('invalid operand', () => {
            const condition = getCondition('product', 'tags', 'dsadsa');
            const result1 = conditionCalculator.isSatisfied(condition, { tags: 't1' });
            expect(result1).toBe(false);
        });

        test('check transaction total', () => {
            const condition = getCondition('transaction', 'total', [12]);
            const transaction1 = getTransaction(12);
            const result1 = conditionCalculator.isSatisfied(condition, transaction1);
            expect(result1).toBe(true);

            const transaction2 = getTransaction(13);
            const result2 = conditionCalculator.isSatisfied(condition, transaction2);
            expect(result2).toBe(true);

            const transaction3 = getTransaction(11);
            const result3 = conditionCalculator.isSatisfied(condition, transaction3);
            expect(result3).toBe(false);
        });

        test('check transaction subtotal', () => {
            const condition = getCondition('transaction', 'subtotal', [12]);
            const transaction1 = getTransaction(12);
            const result1 = conditionCalculator.isSatisfied(condition, transaction1);
            expect(result1).toBe(false);

            const transaction2 = getTransaction(13);
            const result2 = conditionCalculator.isSatisfied(condition, transaction2);
            expect(result2).toBe(false);

            const transaction3 = getTransaction(11);
            const result3 = conditionCalculator.isSatisfied(condition, transaction3);
            expect(result3).toBe(false);
        });

        test('check empty transaction', () => {
            const condition = getCondition('transaction', 'subtotal', [12]);
            const result1 = conditionCalculator.isSatisfied(condition);
            expect(result1).toBe(false);
        });

        const getTransaction2 = subtotal => {
            return { subtotal };
        };

        test('check transaction subtotal', () => {
            const condition = getCondition('transaction', 'subtotal', [12]);
            const transaction1 = getTransaction2(12);
            const result1 = conditionCalculator.isSatisfied(condition, transaction1);
            expect(result1).toBe(true);

            const transaction2 = getTransaction2(13);
            const result2 = conditionCalculator.isSatisfied(condition, transaction2);
            expect(result2).toBe(true);

            const transaction3 = getTransaction2(11);
            const result3 = conditionCalculator.isSatisfied(condition, transaction3);
            expect(result3).toBe(false);
        });

        test('check transaction is null', () => {
            const condition = getCondition('transaction', 'subtotal', [12]);
            const result1 = conditionCalculator.isSatisfied(condition);
            expect(result1).toBe(false);
        });

        const Business1 = {
            country: 'MY',
            fulfillmentOptions: 'test',
            planId: '123456',
            addonIds: ['12', '34'],
            enableCashback: false,
            haveBeepFreeShippingZone: true,
            qrOrderingSettings: { searchingTags: ['tag1'], marketingTags: ['tag2'] },
        };

        test('check business', () => {
            const condition1 = getCondition('business', 'country', [11]);
            const result1 = conditionCalculator.isSatisfied(condition1, Business1);
            expect(result1).toBe(false);

            const condition2 = getCondition('business', 'fulfillmentOptions', [11]);
            const result2 = conditionCalculator.isSatisfied(condition2, Business1);
            expect(result2).toBe(false);

            const condition4 = getCondition('business', 'planId', [11]);
            const result4 = conditionCalculator.isSatisfied(condition4, Business1);
            expect(result4).toBe(false);

            const condition5 = getCondition('business', 'addonIds', [11]);
            const result5 = conditionCalculator.isSatisfied(condition5, Business1);
            expect(result5).toBe(false);

            const condition6 = getCondition('business', 'enableCashback', [11]);
            const result6 = conditionCalculator.isSatisfied(condition6, Business1);
            expect(result6).toBe(false);

            const condition7 = getCondition('business', 'haveBeepFreeShippingZone', [11]);
            const result7 = conditionCalculator.isSatisfied(condition7, Business1);
            expect(result7).toBe(false);

            const condition8 = getCondition('business', 'qrOrderingSettings.searchingTags', [11]);
            const result8 = conditionCalculator.isSatisfied(condition8, Business1);
            expect(result8).toBe(false);

            const condition9 = getCondition('business', 'qrOrderingSettings.marketingTags', [11]);
            const result9 = conditionCalculator.isSatisfied(condition9, Business1);
            expect(result9).toBe(false);
        });

        test('check business null', () => {
            const condition1 = getCondition('business', 'country', [11]);
            const result1 = conditionCalculator.isSatisfied(condition1);
            expect(result1).toBe(false);

            const condition2 = getCondition('business', 'fulfillmentOptions', [11]);
            const result2 = conditionCalculator.isSatisfied(condition2);
            expect(result2).toBe(false);

            const condition3 = getCondition('business', '***', [11]);
            const result3 = conditionCalculator.isSatisfied(condition3);
            expect(result3).toBe(false);

            const condition4 = getCondition('business', 'planId', [11]);
            const result4 = conditionCalculator.isSatisfied(condition4);
            expect(result4).toBe(false);

            const condition5 = getCondition('business', 'addonIds', [11]);
            const result5 = conditionCalculator.isSatisfied(condition5);
            expect(result5).toBe(false);

            const condition6 = getCondition('business', 'enableCashback', [11]);
            const result6 = conditionCalculator.isSatisfied(condition6);
            expect(result6).toBe(false);

            const condition7 = getCondition('business', 'haveBeepFreeShippingZone', [11]);
            const result7 = conditionCalculator.isSatisfied(condition7);
            expect(result7).toBe(false);

            const condition8 = getCondition('business', 'qrOrderingSettings.searchingTags', [11]);
            const result8 = conditionCalculator.isSatisfied(condition8);
            expect(result8).toBe(false);

            const condition9 = getCondition('business', 'qrOrderingSettings.marketingTags', [11]);
            const result9 = conditionCalculator.isSatisfied(condition9);
            expect(result9).toBe(false);
        });
    });

    describe('operator:in', () => {
        const getCondition = (entity, propertyName, operand) => {
            return {
                operand,
                entity,
                propertyName,
                operator: 'in',
            };
        };

        test('check product id', () => {
            const condition = getCondition('product', 'id', ['id1', 'id2']);
            const result1 = conditionCalculator.isSatisfied(condition, { _id: 'id1' });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { _id: 'id3' });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check product category', () => {
            const condition = getCondition('product', 'category', ['c1', 'c2']);
            const result1 = conditionCalculator.isSatisfied(condition, { category: 'c1' });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { category: 'c3' });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });
    });

    describe('operator:contains', () => {
        const getCondition = (entity, propertyName, operand) => {
            return {
                operand,
                entity,
                propertyName,
                operator: 'contains',
            };
        };
        test('invalid operand', () => {
            const condition = getCondition('product', 'tags', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, { tags: 't1' });
            expect(result1).toBe(false);
        });

        test('check product tags', () => {
            const condition = getCondition('product', 'tags', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, { tags: ['t1', 't2'] });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { tags: ['t3'] });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check customer tags', () => {
            const condition = getCondition('customer', 'tags', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, { tags: ['t1', 't2'] });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { tags: ['t3'] });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);

            const result4 = conditionCalculator.isSatisfied(condition);
            expect(result4).toBe(false);
        });

        test('check business country', () => {
            const condition = getCondition('business', 'country', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, { country: ['t1', 't2'] });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { country: ['t3'] });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business fulfillmentOptions', () => {
            const condition = getCondition('business', 'fulfillmentOptions', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                fulfillmentOptions: ['t1', 't2'],
            });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, {
                fulfillmentOptions: ['t3'],
            });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business planId', () => {
            const condition = getCondition('business', 'planId', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, { planId: ['t1', 't2'] });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { planId: ['t3'] });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business addonIds', () => {
            const condition = getCondition('business', 'addonIds', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, { addonIds: ['t1', 't2'] });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { addonIds: ['t3'] });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business enableCashback', () => {
            const condition = getCondition('business', 'enableCashback', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                enableCashback: ['t1', 't2'],
            });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, { enableCashback: ['t3'] });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business haveBeepFreeShippingZone', () => {
            const condition = getCondition('business', 'haveBeepFreeShippingZone', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                haveBeepFreeShippingZone: ['t1', 't2'],
            });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, {
                haveBeepFreeShippingZone: ['t3'],
            });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business qrOrderingSettings.searchingTags', () => {
            const condition = getCondition('business', 'qrOrderingSettings.searchingTags', [
                't1',
                't2',
            ]);
            const result1 = conditionCalculator.isSatisfied(condition, {
                qrOrderingSettings: { searchingTags: ['t1', 't2'] },
            });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, {
                qrOrderingSettings: { searchingTags: ['t3'] },
            });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business qrOrderingSettings.marketingTags', () => {
            const condition = getCondition('business', 'qrOrderingSettings.marketingTags', [
                't1',
                't2',
            ]);
            const result1 = conditionCalculator.isSatisfied(condition, {
                qrOrderingSettings: { marketingTags: ['t1', 't2'] },
            });
            expect(result1).toBe(true);

            const result2 = conditionCalculator.isSatisfied(condition, {
                qrOrderingSettings: { marketingTags: ['t3'] },
            });
            expect(result2).toBe(false);

            const result3 = conditionCalculator.isSatisfied(condition, {});
            expect(result3).toBe(false);
        });

        test('check business empty', () => {
            const condition = getCondition('business', '', ['t1', 't2']);
            const result1 = conditionCalculator.isSatisfied(condition);
            expect(result1).toBe(false);
        });
    });

    describe('operator:eq', () => {
        const getCondition = (entity, propertyName, operand) => {
            return {
                operand,
                entity,
                propertyName,
                operator: 'eq',
            };
        };

        test('invalid operand', () => {
            const condition = getCondition('customer', 'hasFirstPurchasePOS', [0]);
            const result1 = conditionCalculator.isSatisfied(condition, { hasFirstPurchasePOS: 3 });
            expect(result1).toBe(false);
        });

        test('check ecommerce has first purchase', () => {
            const condition = getCondition('customer', 'hasFirstPurchaseEcommerce', ['false']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                hasFirstPurchaseEommerce: 'true',
            });
            expect(result1).toBe(false);
        });

        test('check beep has first purchase', () => {
            const condition = getCondition('customer', 'hasFirstPurchaseBeep', ['false']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                hasFirstPurchaseBeep: 'true',
            });
            expect(result1).toBe(false);
        });

        test('check POS has first purchase', () => {
            const condition = getCondition('customer', 'hasFirstPurchasePOS', ['false']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                hasFirstPurchasePOS: 'true',
            });
            expect(result1).toBe(false);
        });

        test('valid operand', () => {
            const condition = getCondition('customer', 'hasFirstPurchasePOS', ['false']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                hasFirstPurchasePOS: 'false',
            });
            expect(result1).toBe(true);
        });

        test('check has universal first purchase fail', () => {
            const condition = getCondition('customer', 'hasUniversalFirstPurchase', ['false']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                hasUniversalFirstPurchase: 'true',
            });
            expect(result1).toBe(false);
        });

        test('check has universal first purchase success', () => {
            const condition = getCondition('customer', 'hasUniversalFirstPurchase', ['true']);
            const result1 = conditionCalculator.isSatisfied(condition, {
                hasUniversalFirstPurchase: 'true',
            });
            expect(result1).toBe(true);
        });
    });

    describe('invalid condition', () => {
        test('invalid propertyName', () => {
            const getCondition = (entity, propertyName, operand) => {
                return {
                    operand,
                    entity,
                    propertyName,
                    operator: 'in',
                };
            };
            let invalidCondition = getCondition('customer', 'xxx', ['t1', 't2']);
            let result = conditionCalculator.isSatisfied(invalidCondition, {});
            expect(result).toBe(false);
            invalidCondition = getCondition('product', 'xxx', ['t1', 't2']);
            result = conditionCalculator.isSatisfied(invalidCondition, {});
            expect(result).toBe(false);
            invalidCondition = getCondition('transaction', 'xxx', ['t1', 't2']);
            result = conditionCalculator.isSatisfied(invalidCondition, {});
            expect(result).toBe(false);
        });

        test('invalid operator', () => {
            const getCondition = (entity, propertyName, operand) => {
                return {
                    operand,
                    entity,
                    propertyName,
                    operator: 'xxxx',
                };
            };
            const invalidCondition = getCondition('customer', 'tags', ['t1', 't2']);
            const result = conditionCalculator.isSatisfied(invalidCondition, {});
            expect(result).toBe(false);
        });

        test('invalid entity', () => {
            const getCondition = (propertyName, operand) => {
                return {
                    operand,
                    entity: 'xxxx',
                    propertyName,
                    operator: 'in',
                };
            };
            const invalidCondition = getCondition('customer', 'tags', ['t1', 't2']);
            const result = conditionCalculator.isSatisfied(invalidCondition, {});
            expect(result).toBe(false);
        });
    });
});
