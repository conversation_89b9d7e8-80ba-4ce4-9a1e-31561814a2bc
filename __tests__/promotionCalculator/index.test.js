import promotionCalculator from '../../src/promotionCalculator';
import { ParameterError, PromotionCheckConditionError } from '../../src/promotionCalculator/error';
import { ChannelType } from '../../src/utils';

describe('calculate', () => {
    let business;
    let promotion;
    let items;
    let transaction;

    beforeEach(() => {
        business = {
            timezone: 'Asia/Kuala_Lumpur',
            taxCodes: [],
        };
        promotion = {
            _id: 'pid',

            business: 'b',
            discountType: 'absolute',
            discountValue: 1,
            minQuantity: 1,
            maxQuantity: 2,
            isEnabled: true,
            validTimeTo: 24,
            validTimeFrom: 0,
            validTo: null,
            validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
            requiredProducts: [],
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: null,
                    maxQuantity: null,
                },
            ],
        };
        items = [
            {
                taxCode: '',
                id: 'id1',
                quantity: 1,
                unitPrice: 10,
                productId: 'pid1',
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
        ];
        transaction = {
            business: 'b',
            total: 10,
            customer: { tags: ['c1'] },
            items,
            channel: ChannelType.ECOMMERCE,
        };
    });
    test('success: transaction level promotion (absolute)', () => {
        promotionCalculator.calculate(transaction, [promotion], business);
        expect(transaction.items[0].promotionAppliedQuantityMap[promotion._id]).toBe(1);
        expect(transaction.promotions.length).toBe(1);
        expect(transaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                promotionId: 'pid',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with transaction level condition', () => {
        promotion.minQuantity = 1;
        promotion.maxQuantity = 2;
        promotion.conditions = [
            {
                operand: ['10'],
                entity: 'transaction',
                propertyName: 'total',
                operator: 'gte',
            },
        ];
        promotionCalculator.calculate(transaction, [promotion], business);
        expect(transaction.promotions.length).toBe(1);
        expect(transaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                promotionId: 'pid',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with business level condition', () => {
        promotion.minQuantity = 1;
        promotion.maxQuantity = 2;
        promotion.conditions = [
            {
                operand: ['10'],
                entity: 'business',
                propertyName: 'total',
                operator: 'gte',
            },
        ];
        promotionCalculator.calculate(transaction, [promotion], business);
        expect(transaction.promotions).toBeUndefined();
        expect(transaction.promotions).toBeUndefined();
    });

    test('fail: transaction level promotion (absolute) with business level condition', () => {
        promotion.minQuantity = 1;
        promotion.maxQuantity = 2;
        promotion.conditions = [
            {
                operand: ['MY'],
                entity: 'business',
                propertyName: 'country',
                operator: 'in',
                country: 'MY',
            },
        ];
        promotionCalculator.calculate(transaction, [promotion], business);
        expect(transaction.promotions).toBeFalsy();
    });

    test('fail: transaction level promotion (absolute) with item level condition', () => {
        promotion.minQuantity = 2;
        promotion.maxQuantity = 3;
        promotionCalculator.calculate(transaction, [promotion], business);
        expect(transaction.promotions).toBeFalsy();
    });

    test('success: with none purchase item', () => {
        transaction.items = [{ itemType: 'ServiceCharge' }];
        promotionCalculator.calculate(transaction, [promotion], business);
        expect(transaction.promotions).toBeFalsy();
    });

    test('success: transaction level promotion (combo)', () => {
        const comboPromotion = {
            ...promotion,
            discountType: 'combo',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 1,
                    maxQuantity: 1,
                },
            ],
            taxCode: 'tax1',
            taxRate: 0.1,
        };

        const comboTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid2',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        promotionCalculator.calculate(comboTransaction, [comboPromotion], business);
        expect(comboTransaction.items[0].promotionAppliedQuantityMap[comboPromotion._id]).toBe(2);
        expect(comboTransaction.items[1].promotionAppliedQuantityMap[comboPromotion._id]).toBe(2);
        expect(comboTransaction.promotions.length).toBe(1);
        expect(comboTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                quantity: 4,
                taxRate: 0.1,
                type: 'combo',
                promotionId: 'pid',
            }),
        );
    });

    test('success: transaction level promotion (absolute)', () => {
        const absolutePromotion = {
            ...promotion,
            discountType: 'absolute',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(2);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                quantity: 1,
                type: 'absolute',
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    // absolute repeatable
    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), both min and max is undefined', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(2);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                quantity: 200,
                type: 'absolute',
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), min is undefined', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: undefined,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(2);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                quantity: 200,
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), max is undefined', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(absoluteTransaction.promotions).toBeUndefined();
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), totalAvailableQuantity < minQuantity', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(absoluteTransaction.promotions).toBeUndefined();
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), totalAvailableQuantity > minQuantity', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 6,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(6);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                quantity: 2,
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), maxQuantity > totalAvailableQuantity > minQuantity', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 4,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(4);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                quantity: 1,
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    // absolute is not repeatable

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), totalAvailableQuantity > maxQuantity', () => {
        const absolutePromotion = {
            ...promotion,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 10,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(absoluteTransaction.items[0].promotionAppliedQuantityMap).toBeUndefined();
        expect(absoluteTransaction.promotions).toBeUndefined();
    });
    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), multiple promotion', () => {
        const absolutePromotions = [
            {
                ...promotion,
                discountType: 'absolute',
                minQuantity: 3,
                maxQuantity: 3,
                discountValue: 10,
                _id: 'pid1',
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                    },
                ],
                taxCode: 'tax1',
            },
            {
                ...promotion,
                discountType: 'absolute',
                minQuantity: 5,
                maxQuantity: 5,
                _id: 'pid2',
                discountValue: 20,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                    },
                ],
                taxCode: 'tax1',
            },
            {
                ...promotion,
                discountType: 'absolute',
                minQuantity: 10,
                maxQuantity: 10,
                _id: 'pid3',
                discountValue: 50,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                    },
                ],
                taxCode: 'tax1',
            },
        ];
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 10,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, absolutePromotions, business);
        expect(absoluteTransaction.items[0].promotionAppliedQuantityMap['pid3']).toBe(10);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 50,
                type: 'absolute',
                quantity: 1,
                promotionId: 'pid3',
                taxCode: 'tax1',
            }),
        );
    });

    // percentage repeatable

    test('success: item level promotion (percentage) with item level condition(isRepeatable), min is undefined', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: undefined,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(2);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 2,
            }),
        );
    });

    test('success: item level promotion (percentage) with item level condition(isRepeatable), max is undefined', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(percentageTransaction.items[0].promotions).toBeUndefined();
    });

    test('success: item level promotion (percentage) with item level condition(isRepeatable), totalAvailableQuantity < minQuantity', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(percentageTransaction.items[0].promotions).toBeUndefined();
    });

    test('success: item level promotion (percentage) with item level condition(isRepeatable), totalAvailableQuantity > minQuantity', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 6,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(6);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 6,
            }),
        );
    });

    test('success: item level promotion (percentage) with item level condition(isRepeatable), maxQuantity > totalAvailableQuantity > minQuantity', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 4,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(4);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 4,
            }),
        );
    });

    // percentage not repeatable
    test('success: Item level promotion (percentage) with item level condition(is not Repeatable), minQuantity is undefined', () => {
        const percentagePromotion = {
            ...promotion,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(percentageTransaction.items[0].promotions).toBeUndefined();
    });

    test('success: item level promotion (percentage) with item level condition(is not Repeatable), maxQuantity > totalAvailableQuantity > minQuantity', () => {
        const percentagePromotion = {
            ...promotion,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 4,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(4);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 4,
            }),
        );
    });

    test('success: item level promotion (percentage) with item level condition(is not Repeatable),  totalAvailableQuantity > maxQuantity', () => {
        const percentagePromotion = {
            ...promotion,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 20,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(percentageTransaction.items[0].promotionAppliedQuantityMap).toBeUndefined();
        expect(percentageTransaction.items[0].promotions).toBeUndefined();
    });

    test('success: promotion with transaction level condition (percentage)', () => {
        const testPromotion = { ...promotion, discountType: 'percentage' };
        testPromotion.conditions.push({
            operand: [0],
            entity: 'transaction',
            propertyName: 'total',
            operator: 'gte',
        });

        testPromotion.conditions.push({
            operand: ['c1'],
            entity: 'customer',
            propertyName: 'tags',
            operator: 'contains',
        });

        promotionCalculator.calculate(transaction, [testPromotion], business);
        expect(transaction.items[0].promotionAppliedQuantityMap[testPromotion._id]).toBe(1);
        expect(transaction.items[0].promotions.length).toBe(1);
        expect(transaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                promotionId: 'pid',
                type: 'percentage',
            }),
        );
    });

    test('success: item level promotion', () => {
        const itemLevelPromotion = { ...promotion, discountType: 'percentage' };
        promotionCalculator.calculate(transaction, [itemLevelPromotion], business);
        expect(transaction.items[0].promotionAppliedQuantityMap[itemLevelPromotion._id]).toBe(1);
        expect(transaction.items[0].promotions.length).toBe(1);
        expect(transaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                promotionId: 'pid',
                type: 'percentage',
            }),
        );
    });

    test('success: no conditions no requiredProducts', () => {
        const noConditionPromotion = {
            ...promotion,
            discountType: 'percentage',
            conditions: [],
            requiredProducts: [],
        };
        promotionCalculator.calculate(transaction, [noConditionPromotion], business);
        expect(transaction.items[0].promotions.length).toBe(1);
        expect(transaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                promotionId: 'pid',
                type: 'percentage',
            }),
        );
    });

    test('success: apply multiple promotions include fail and success cases', () => {
        const promotions = [
            {
                _id: '5c08c390b7390f0183294def',
                promotionId: '5c08c390b7390f0183294def',
                appliedStores: null,
                createdTime: '2018-12-06T06:37:04.541Z',
                discountType: 'absolute',
                discountValue: 50,
                isEnabled: false,
                maxQuantity: 10,
                minQuantity: 1,
                modifiedTime: '2018-12-10T04:02:52.513Z',
                name: 'Bardot',
                ordering: 18,
                taxCode: null,
                validDays: null,
                validFrom: '2018-12-09T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: null,
                        objectName: null,
                        entity: 'product',
                        operand: [
                            '5b641ae7278bb801617052b0',
                            '5b641ae7278bb801617052b2',
                            '5b641ae7278bb801617052b4',
                        ],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c08c40eb7390f0183294df2',
                promotionId: '5c08c40eb7390f0183294df2',
                appliedStores: null,
                createdTime: '2018-12-06T06:39:10.632Z',
                discountType: 'fixedUnitPrice',
                discountValue: 2,
                isEnabled: true,
                maxQuantity: 2,
                minQuantity: 1,
                modifiedTime: '2019-07-09T11:35:44.678Z',
                name: 'overrideprice',
                ordering: 19,
                taxCode: '',
                validDays: null,
                validFrom: '2019-07-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: null,
                        objectName: null,
                        entity: 'product',
                        operand: ['5c90bec59851bf018440b232'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c11dea4e41aa70183c574d0',
                promotionId: '5c11dea4e41aa70183c574d0',
                appliedStores: null,
                createdTime: '2018-12-13T04:23:00.376Z',
                discountType: 'percentage',
                discountValue: 5,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-07-11T06:37:59.415Z',
                name: 'Discount',
                ordering: 20,
                taxCode: '',
                validDays: null,
                validFrom: '2019-07-10T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: null,
                        objectName: null,
                        entity: 'transaction',
                        operand: ['20'],
                        operator: 'gte',
                        propertyName: 'total',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c29e5e801375d0183fb4631',

                promotionId: '5c29e5e801375d0183fb4631',
                appliedStores: null,
                createdTime: '2018-12-31T09:48:24.697Z',
                discountType: 'fixedUnitPrice',
                discountValue: 4,
                isEnabled: false,
                maxQuantity: 2,
                minQuantity: 1,
                modifiedTime: '2019-01-15T02:42:04.264Z',
                name: 'Skirt Promo',
                ordering: 21,
                taxCode: '',
                validDays: null,
                validFrom: '2018-12-30T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: null,
                        objectName: null,
                        entity: 'product',
                        operand: ['skirt'],
                        operator: 'contains',
                        propertyName: 'tags',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c3d48c7ba8b0401fb0a8751',
                promotionId: '5c3d48c7ba8b0401fb0a8751',
                appliedStores: null,
                createdTime: '2019-01-15T02:43:19.272Z',
                discountType: 'buyXFreeY',
                discountValue: 5,
                isEnabled: false,
                maxQuantity: 5,
                minQuantity: 3,
                modifiedTime: '2019-02-22T08:41:12.030Z',
                name: 'Buy8free5',
                ordering: 22,
                taxCode: '',
                validDays: null,
                validFrom: '2019-02-21T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 3,
                        objectName: null,
                        entity: 'product',
                        operand: ['Non-Food Materials'],
                        operator: 'in',
                        propertyName: 'category',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c3d4d6bba8b0401fb0a8770',
                promotionId: '5c3d4d6bba8b0401fb0a8770',
                appliedStores: null,
                createdTime: '2019-01-15T03:03:07.847Z',
                discountType: 'absolute',
                discountValue: 20,
                isEnabled: false,
                maxQuantity: 4294967295,
                minQuantity: 1,
                modifiedTime: '2019-01-29T03:11:58.714Z',
                name: 'Bundle Promo',
                ordering: 23,
                taxCode: '',
                validDays: null,
                validFrom: '2019-01-24T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: null,
                        objectName: null,
                        entity: 'product',
                        operand: ["Women's Apparel"],
                        operator: 'in',
                        propertyName: 'category',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c500dae71de53018e0929e7',
                promotionId: '5c500dae71de53018e0929e7',
                appliedStores: null,
                createdTime: '2019-01-29T08:24:14.854Z',
                discountType: 'percentage',
                discountValue: 40,
                isEnabled: false,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-04-01T06:35:24.132Z',
                name: 'Take % Off',
                ordering: 24,
                taxCode: '',
                validDays: null,
                validFrom: '2019-03-03T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c6f84a9b3c4a40190cf363a',
                promotionId: '5c6f84a9b3c4a40190cf363a',
                appliedStores: null,
                createdTime: '2019-02-22T05:12:09.810Z',
                discountType: 'absolute',
                discountValue: 10,
                isEnabled: false,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-06-18T11:31:48.899Z',
                name: 'alphabet',
                ordering: 25,
                taxCode: '',
                validDays: null,
                validFrom: '2019-06-17T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5c79044c98aeb00184b7bd1a',
                promotionId: '5c79044c98aeb00184b7bd1a',
                appliedStores: null,
                createdTime: '2019-03-01T10:07:08.023Z',
                discountType: 'percentage',
                discountValue: 25,
                isEnabled: false,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-06-18T11:32:20.082Z',
                name: 'Promo',
                ordering: 26,
                taxCode: null,
                validDays: null,
                validFrom: '2019-06-17T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
            {
                _id: '5d24754c97d2ca001737f659',
                promotionId: '5d24754c97d2ca001737f659',
                appliedStores: null,
                createdTime: '2019-07-09T11:06:52.376Z',
                discountType: 'buyXFreeY',
                discountValue: 1,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-07-11T06:46:44.018Z',
                name: 'buy3free1',
                ordering: 27,
                taxCode: null,
                validDays: null,
                validFrom: '2019-07-10T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 3,
                        objectName: null,
                        entity: 'product',
                        operand: ['5c90522d9ed23b01858159ce'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'ck',
                owner: null,
            },
        ];
        const customTransaction = {
            business: 'ck',
            channel: ChannelType.ECOMMERCE,
            items: [
                {
                    rate: 0.1,
                    discount: null,
                    subTotal: 2.78,
                    total: null,
                    unitPrice: 2.7750000000000004,
                    quantity: 1,
                    taxRate: 0,
                    tax: null,
                    product: {},
                },
                {
                    discount: 17.25,
                    display: {
                        total: 29.14,
                        subtotal: 47.25,
                        tax: 1.39,
                    },
                    priceType: 'fixed',
                    selectedOptions: [],
                    taxInclusiveUnitPrice: 15.75,
                    subTotal: 45,
                    total: 27.75,
                    unitPrice: 15,
                    taxInclusiveSubtotal: 47.25,
                    productId: '5c90522d9ed23b01858159ce',
                    quantity: 5,
                    taxRate: 0.05,
                    tax: 1.39,
                    product: {
                        category: null,
                        tags: [],
                        _id: '5c90522d9ed23b01858159ce',
                    },
                    taxCode: '5cf0cce73397a800161a3971',
                    title: 'pizza',
                    id: '5c90522d9ed23b01858159ce',
                },
                {
                    id: '5c90bec59851bf018440b232',
                    productId: '5c90bec59851bf018440b232',
                    unitPrice: 15,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.05,
                    title: 'noms',
                    total: 0,
                    taxCode: '5cf0cce73397a800161a3971',
                    product: {
                        category: null,
                        tags: [],
                        _id: '5c90bec59851bf018440b232',
                    },
                },
            ],
            discount: 17.25,
            display: {
                subtotal: 27.75,
                discount: null,
                serviceCharge: null,
                tax: 1.39,
                total: 29.14,
            },
            serviceChargeTax: null,
            subtotal: 47.78,
            total: 29.14,
            serviceCharge: null,
            tax: 1.39,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            calculationMode: 'Sale',
            transactionId: '2e5cba33-ad70-46b3-8661-4c7bf289f1e6',
            storeId: '5b62d783081ccd016185cf08',
        };
        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        promotionCalculator.calculate(customTransaction, promotions, businessSettings);
        expect(customTransaction.items[0].promotions.length).toBe(1);
        expect(customTransaction.items[1].promotions.length).toBe(2);
        expect(customTransaction.items[2].promotions.length).toBe(2);
    });

    test('success: buyXFreeY promotion multiple combo', () => {
        const buyXFreeYPromotion = {
            _id: 'pid',
            appliedStores: null,
            createdTime: '2019-07-09T11:06:52.376Z',
            discountType: 'buyXFreeY',
            discountValue: 1,
            isEnabled: true,
            maxQuantity: 4294967295,
            minQuantity: 0.01,
            modifiedTime: '2019-07-11T06:46:44.018Z',
            name: 'buy3free1',
            ordering: 27,
            taxCode: null,
            validDays: null,
            validFrom: '2019-07-10T16:00:00.000Z',
            validTimeFrom: 0,
            validTimeTo: 24,
            conditions: [
                {
                    maxQuantity: null,
                    minQuantity: 3,
                    objectName: null,
                    entity: 'product',
                    operand: ['c1', 'c2'],
                    operator: 'in',
                    propertyName: 'category',
                    owner: null,
                    requiredProductOwner: [],
                },
            ],
            requiredProducts: [],
            business: 'b',
            owner: null,
        };

        transaction.items[0].quantity = 6;
        promotionCalculator.calculate(transaction, [buyXFreeYPromotion], business);
        expect(transaction.items[0].promotions.length).toBe(1);
        expect(transaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                promotionId: 'pid',
                type: 'buyXFreeY',
            }),
        );
    });

    test('fail: check fail', () => {
        const disabledPromotion = { ...promotion, isEnabled: false };
        const errors = promotionCalculator.calculate(transaction, [disabledPromotion], business);
        expect(errors.length).toBe(1);
    });

    test('fail: no combo', () => {
        const insufficientTransaction = { ...transaction, items: [] };
        const errors = promotionCalculator.calculate(
            insufficientTransaction,
            [promotion],
            business,
        );
        expect(errors.length).toBe(1);
    });

    test('fail: combo quantity bigger than limit', () => {
        transaction.items.forEach(item => {
            item.quantity = 3;
        });
        const errors = promotionCalculator.calculate(transaction, [promotion], business);
        expect(errors.length).toBe(1);
    });

    test('fail: condition not satisfied', () => {
        const customerConditionPromotion = {
            ...promotion,
            conditions: [
                {
                    operand: ['c2'],
                    entity: 'customer',
                    propertyName: 'tags',
                    operator: 'contains',
                },
            ],
        };

        const errors1 = promotionCalculator.calculate(
            transaction,
            [customerConditionPromotion],
            business,
        );
        expect(errors1.length).toBe(1);

        const transactionConditionPromotion = {
            ...promotion,
            conditions: [
                {
                    operand: [12],
                    entity: 'transaction',
                    propertyName: 'total',
                    operator: 'gte',
                },
            ],
        };

        const errors2 = promotionCalculator.calculate(
            transaction,
            [transactionConditionPromotion],
            business,
        );
        expect(errors2.length).toBe(1);

        const transaction1 = {
            channel: ChannelType.ECOMMERCE,
            business: 'b',
            total: 10,
            items,
            userId: '123',
            customer: {
                customerClaimCount: 2,
            },
        };
        const customerFirstTimePurchaseConditionPromotion = {
            ...promotion,
            conditions: [
                {
                    operand: ['false'],
                    entity: 'customer',
                    propertyName: 'hasFirstPurchaseBeep',
                    operator: 'eq',
                },
            ],
        };

        const error3 = promotionCalculator.calculate(
            transaction1,
            [customerFirstTimePurchaseConditionPromotion],
            business,
        );
        expect(error3.length).toBe(1);
        expect(error3[0].code).toBe('4414');
    });

    test('fail: missing operand for condition', () => {
        const customerConditionPromotion = {
            ...promotion,
            conditions: [
                {
                    operand: ['c2'],
                    entity: 'customer',
                    propertyName: 'tags',
                    operator: 'contains',
                },
            ],
        };
        delete transaction.customer;
        const errors1 = promotionCalculator.calculate(
            transaction,
            [customerConditionPromotion],
            business,
        );
        expect(errors1.length).toBe(1);
        delete transaction.items[0].product;
        const errors2 = promotionCalculator.calculate(transaction, [promotion], business);
        expect(errors2.length).toBe(1);
    });

    test('fail: invalid items.taxRate', () => {
        const comboPromotion = {
            ...promotion,
            discountType: 'combo',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 1,
                    maxQuantity: 1,
                },
            ],
            taxCode: 'tax1',
            taxRate: 0.1,
        };

        const comboTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid2',
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const errors = promotionCalculator.calculate(comboTransaction, [comboPromotion], business);
        const expectError = new ParameterError('items.taxRate');
        expect(errors.length).toBe(1);
        expect(errors[0].code).toBe(expectError.code);
    });

    test('fail: invalid items.taxCode', () => {
        const comboPromotion = {
            ...promotion,
            discountType: 'combo',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 1,
                    maxQuantity: 1,
                },
            ],
            taxCode: 'tax1',
            taxRate: 0.1,
        };

        const comboTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    taxRate: 0.1,
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const errors = promotionCalculator.calculate(comboTransaction, [comboPromotion], business);
        const expectError = new PromotionCheckConditionError(null, 'taxcode');
        expect(errors.length).toBe(1);
        expect(errors[0].code).toBe(expectError.code);
    });

    test('fail: different taxCode', () => {
        const comboPromotion = {
            ...promotion,
            discountType: 'combo',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 1,
                    maxQuantity: 1,
                },
            ],
            taxCode: 'tax1',
            taxRate: 0.1,
        };

        const comboTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    taxRate: 0.2,
                    taxCode: 'tax2',
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const errors = promotionCalculator.calculate(comboTransaction, [comboPromotion], business);
        const expectError = new PromotionCheckConditionError();
        expect(errors.length).toBe(1);
        expect(errors[0].code).toBe(expectError.code);
    });

    test('success: different taxCode for converted absolute promotion', () => {
        const absolutePromotion = {
            ...promotion,
            type: 'universal',
            originalDiscountType: 'percentage',
            discountType: 'absolute',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [],
        };

        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    taxRate: 0.2,
                    taxCode: 'tax2',
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const errors = promotionCalculator.calculate(
            absoluteTransaction,
            [absolutePromotion],
            business,
        );
        expect(errors.length).toBe(0);
    });

    test('failed: different taxCode for universal absolute promotion', () => {
        const absolutePromotion = {
            ...promotion,
            type: 'universal',
            discountType: 'absolute',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [],
        };

        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    taxRate: 0.2,
                    taxCode: 'tax2',
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const errors = promotionCalculator.calculate(
            absoluteTransaction,
            [absolutePromotion],
            business,
        );
        expect(errors.length).toBe(0);
    });

    test('failed: different taxCode for merchant absolute promotion', () => {
        const absolutePromotion = {
            ...promotion,
            type: 'merchant',
            discountType: 'absolute',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [],
        };

        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    taxRate: 0.2,
                    taxCode: 'tax2',
                    productId: 'pid2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const errors = promotionCalculator.calculate(
            absoluteTransaction,
            [absolutePromotion],
            business,
        );
        expect(errors.length).toBe(0);
    });

    test('fail: invalid promotion.taxCode', () => {
        const comboPromotion = {
            ...promotion,
            discountType: 'combo',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 1,
                    maxQuantity: 1,
                },
            ],
            taxRate: 0.1,
        };

        const comboTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid2',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const expectError = new ParameterError('promotion.taxCode');
        const errors = promotionCalculator.calculate(comboTransaction, [comboPromotion], business);
        expect(errors.length).toBe(1);
        expect(errors[0].code).toBe(expectError.code);
    });

    test('fail: combo items is not share same taxCode set in promotion', () => {
        const comboPromotion = {
            ...promotion,
            discountType: 'combo',
            minQuantity: undefined,
            maxQuantity: undefined,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 1,
                    maxQuantity: 1,
                },
            ],
            taxRate: 0.1,
            taxCode: 'tax1',
        };

        const comboTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax2',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },

                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid2',
                    taxRate: 0.1,
                    taxCode: 'tax2',
                    product: {
                        _id: 'pid2',
                        category: 'c2',
                    },
                },
            ],
        };

        const expectError = new PromotionCheckConditionError();
        const errors = promotionCalculator.calculate(comboTransaction, [comboPromotion], business);
        expect(errors.length).toBe(1);
        expect(errors[0].code).toBe(expectError.code);
    });

    test('success: with unexpect maxQuantity/minQuantity setting', () => {
        const comboPromotion = {
            ...promotion,
            taxCode: '',
            maxQuantity: 1,
            discountType: 'combo',
        };
        const testTransaction = {
            ...transaction,
        };
        testTransaction.items[0].quantity = 2;
        const errors = promotionCalculator.calculate(testTransaction, [comboPromotion], business);
        expect(errors.length).toBe(0);
        expect(testTransaction.promotions.length).toBe(1);
    });

    test('fail: no availableQuantity for no condition promotion', () => {
        const testPromotion = {
            ...promotion,
            taxCode: '',
            maxQuantity: 1,
            conditions: [],
            discountType: 'percentage',
        };
        const testTransaction = {
            ...transaction,
        };
        for (const item of testTransaction.items) {
            item.quantity = 0;
        }

        const errors = promotionCalculator.calculate(testTransaction, [testPromotion], business);
        expect(errors.length).toBe(1);
    });

    describe('Beep AppliedStores: transaction level promotion (absolute)', () => {
        let absolutePromotion;
        let absoluteTransaction;
        beforeEach(() => {
            absolutePromotion = {
                ...promotion,
                discountType: 'absolute',
                minQuantity: undefined,
                maxQuantity: undefined,
                conditions: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                    },
                ],
                taxCode: 'tax1',
            };
            absoluteTransaction = {
                ...transaction,
                items: [
                    {
                        id: 'id1',
                        quantity: 2,
                        unitPrice: 10,
                        productId: 'pid1',
                        taxRate: 0.1,
                        taxCode: 'tax1',
                        product: {
                            _id: 'pid1',
                            category: 'c1',
                        },
                    },
                ],
            };
        });

        test('failed: store not satisfied', () => {
            const errors = promotionCalculator.calculate(
                {
                    ...absoluteTransaction,
                    channel: ChannelType.BEEP,
                },
                [
                    {
                        ...absolutePromotion,
                        appliedStores: ['sid'],
                    },
                ],
                business,
            );
            expect(errors.length).toBe(1);
        });

        test('success: channel not beep ', () => {
            const errors = promotionCalculator.calculate(
                JSON.parse(JSON.stringify(absoluteTransaction)),
                [
                    Object.assign({}, absolutePromotion, {
                        appliedStores: ['sid'],
                        applyToOnlineStore: true,
                    }),
                ],
                business,
                true,
                ChannelType.ECOMMERCE,
            );
            expect(errors.length).toBe(0);
        });

        test('success: All', () => {
            const tempTransaction = JSON.parse(JSON.stringify(absoluteTransaction));
            const tempPromotion = Object.assign(JSON.parse(JSON.stringify(absolutePromotion)), {
                appliedStores: ['All'],
                applyToOnlineStore: true,
            });
            const errors = promotionCalculator.calculate(
                tempTransaction,
                [tempPromotion],
                business,
                true,
                ChannelType.BEEP,
            );
            expect(errors.length).toBe(0);
            expect(tempTransaction.items[0].promotionAppliedQuantityMap[tempPromotion._id]).toBe(2);
            expect(tempTransaction.promotions.length).toBe(1);
            expect(tempTransaction.promotions[0]).toEqual(
                expect.objectContaining({
                    inputValue: 1,
                    type: 'absolute',
                    promotionId: 'pid',
                    taxCode: 'tax1',
                }),
            );
        });

        test('success: store satisfied', () => {
            const storeId = 'xxxxx';
            const tempTransaction = Object.assign(JSON.parse(JSON.stringify(absoluteTransaction)), {
                storeId,
            });
            const tempPromotion = Object.assign(JSON.parse(JSON.stringify(absolutePromotion)), {
                appliedStores: [storeId],
                applyToOnlineStore: true,
            });
            const errors = promotionCalculator.calculate(
                tempTransaction,
                [tempPromotion],
                business,
                true,
                ChannelType.BEEP,
            );
            expect(errors.length).toBe(0);
            expect(tempTransaction.items[0].promotionAppliedQuantityMap[tempPromotion._id]).toBe(2);
            expect(tempTransaction.promotions.length).toBe(1);
            expect(tempTransaction.promotions[0]).toEqual(
                expect.objectContaining({
                    inputValue: 1,
                    type: 'absolute',
                    promotionId: 'pid',
                    taxCode: 'tax1',
                }),
            );
        });
    });
});
