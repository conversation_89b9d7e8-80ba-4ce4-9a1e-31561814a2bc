import calculator from '../../index';
import { ChannelType, PromotionType } from '../../src/utils';

describe('single promotion', () => {
    test('success', () => {
        const business = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const items = [
            {
                id: 'id1',
                quantity: 1,
                unitPrice: 10,
                productId: 'pid1',
                taxRate: 0,
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
        ];
        const promotion = {
            _id: 'pid',

            business: 'b',
            discountType: 'absolute',
            discountValue: 1,
            minQuantity: 1,
            maxQuantity: 2,
            isEnabled: true,
            validTimeTo: 24,
            validTimeFrom: 0,
            validTo: null,
            validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
            requiredProducts: [],
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: null,
                    maxQuantity: null,
                },
            ],
            type: PromotionType.MERCHANT,
        };
        const transaction = {
            business: 'b',
            total: 10,
            customer: { tags: ['c1'] },
            items,
            channel: ChannelType.ECOMMERCE,
        };
        calculator.applyPromotion(transaction, [promotion], business);
        calculator.calculate(transaction);
        expect(transaction.total).toBe(9);
    });

    test('fail', () => {
        const business = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const items = [
            {
                id: 'id1',
                quantity: 1,
                unitPrice: 10,
                productId: 'pid1',
                taxRate: 0,
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
        ];
        const promotion = {
            discountType: 'absolute',
            discountValue: 1,
            minQuantity: 1,
            maxQuantity: 2,
            isEnabled: false,
            validTimeTo: 24,
            validTimeFrom: 0,
            validTo: null,
            validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
            requiredProducts: [],
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: null,
                    maxQuantity: null,
                },
            ],
            type: PromotionType.MERCHANT,
        };
        const transaction = {
            total: 10,
            customer: { tags: ['c1'] },
            items,
            channel: ChannelType.ECOMMERCE,
        };
        const errors = calculator.applyPromotion(transaction, [promotion], business);
        expect(errors.length).toBe(1);
    });

    test('apply same transaction level promotion multiple times', () => {
        const business = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const items = [
            {
                id: 'id1',
                quantity: 1,
                unitPrice: 10,
                productId: 'pid1',
                taxRate: 0,
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
        ];
        const promotion = {
            _id: 'pid',
            type: PromotionType.MERCHANT,
            business: 'b',
            discountType: 'absolute',
            discountValue: 1,
            minQuantity: 1,
            maxQuantity: 2,
            isEnabled: true,
            validTimeTo: 24,
            validTimeFrom: 0,
            validTo: null,
            validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
            requiredProducts: [],
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: null,
                    maxQuantity: null,
                },
            ],
        };
        const transaction = {
            business: 'b',
            total: 10,
            customer: { tags: ['c1'] },
            items,
            channel: ChannelType.ECOMMERCE,
        };
        calculator.applyPromotion(transaction, [promotion], business);
        calculator.applyPromotion(transaction, [promotion], business);
        expect(transaction.promotions.length).toBe(1);
    });

    test('apply same item level promotion multiple times', () => {
        const business = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const items = [
            {
                id: 'id1',
                quantity: 1,
                unitPrice: 10,
                productId: 'pid1',
                taxRate: 0,
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
        ];
        const promotion = {
            _id: 'pid',
            type: PromotionType.MERCHANT,
            business: 'b',
            discountType: 'percentage',
            discountValue: 1,
            minQuantity: 1,
            maxQuantity: 2,
            isEnabled: true,
            validTimeTo: 24,
            validTimeFrom: 0,
            validTo: null,
            validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
            requiredProducts: [],
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: null,
                    maxQuantity: null,
                },
            ],
        };
        const transaction = {
            business: 'b',
            total: 10,
            customer: { tags: ['c1'] },
            items,
            channel: ChannelType.ECOMMERCE,
        };
        calculator.applyPromotion(transaction, [promotion], business);
        calculator.applyPromotion(transaction, [promotion], business);
        expect(transaction.items[0].promotions.length).toBe(1);
    });
});

test('multiple promotion', () => {
    const business = {
        timezone: 'Asia/Kuala_Lumpur',
    };

    const comboPromotion1 = {
        _id: 'pid1',
        business: 'b',
        discountType: 'combo',
        discountValue: 2,
        isEnabled: true,
        validTimeTo: 24,
        validTimeFrom: 0,
        taxCode: 'code1',
        validTo: null,
        validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
        requiredProducts: [],
        type: PromotionType.MERCHANT,
        conditions: [
            {
                _id: 'cond1',
                operand: ['c1'],
                entity: 'product',
                propertyName: 'category',
                operator: 'in',
                minQuantity: 2,
                maxQuantity: 2,
            },
        ],
    };

    const comboPromotion2 = {
        _id: 'pid2',
        business: 'b',
        type: PromotionType.MERCHANT,
        discountType: 'combo',
        discountValue: 2,
        isEnabled: true,
        validTimeTo: 24,
        validTimeFrom: 0,
        taxCode: 'code1',
        validTo: null,
        validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
        requiredProducts: [],
        conditions: [
            {
                _id: 'cond2',
                operand: ['c2'],
                entity: 'product',
                propertyName: 'category',
                operator: 'in',
                minQuantity: 2,
                maxQuantity: 2,
            },
        ],
    };

    const items = [
        {
            id: 'id1',
            quantity: 2,
            unitPrice: 10,
            productId: 'pid1',
            taxRate: 0,
            taxCode: 'code1',
            product: {
                _id: 'pid1',
                category: 'c1',
            },
        },

        {
            id: 'id2',
            quantity: 2,
            unitPrice: 10,
            productId: 'pid2',
            taxRate: 0,
            taxCode: 'code1',
            product: {
                _id: 'pid2',
                category: 'c2',
            },
        },
    ];

    const transaction = {
        business: 'b',
        total: 10,
        customer: { tags: ['c1'] },
        items,
        channel: ChannelType.ECOMMERCE,
    };

    calculator.applyPromotion(transaction, [comboPromotion1, comboPromotion2], business);
    expect(transaction.promotions.length).toBe(2);
});

test('check condition total', () => {
    const condition = {
        entity: 'transaction',
        propertyName: 'total',
        operand: ['10'],
        operator: 'gte',
    };
    const transaction = {
        total: 12,
    };
    const result = calculator.checkConditionSatisfied(condition, transaction);
    expect(result).toBe(true);
});

test('check condition subtotal', () => {
    const condition = {
        entity: 'transaction',
        propertyName: 'subtotal',
        operand: ['100'],
        operator: 'gte',
    };
    const transaction = {
        subtotal: 100,
    };
    const result = calculator.checkConditionSatisfied(condition, transaction);
    expect(result).toBe(true);
});

describe('Business condition calculator', () => {
    test('check condition country, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'country',
            operand: ['MY'],
            operator: 'in',
        };
        const businessSettings = {
            country: 'MY',
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition country, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'country',
            operand: ['MY'],
            operator: 'in',
        };
        const businessSettings = {
            country: 'TH',
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
    test('check condition fulfillmentOptions, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'fulfillmentOptions',
            operand: ['Delivery'],
            operator: 'contains',
        };
        const businessSettings = {
            fulfillmentOptions: ['Delivery', 'Pickup'],
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition fulfillmentOptions, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'fulfillmentOptions',
            operand: ['Delivery'],
            operator: 'contains',
        };
        const businessSettings = {
            country: 'TH',
            fulfillmentOptions: [],
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
    test('check condition planId, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'planId',
            operand: ['planId'],
            operator: 'in',
        };
        const businessSettings = {
            planId: 'planId',
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition planId, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'planId',
            operand: ['planId'],
            operator: 'in',
        };
        const businessSettings = {
            planId: 'planId2',
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
    test('check condition addonIds, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'addonIds',
            operand: ['addonIds1', 'addonIds2', 'addonIds3'],
            operator: 'contains',
        };
        const businessSettings = {
            addonIds: ['addonIds1', 'addonIds4', 'addonIds2'],
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition addonIds, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'addonIds',
            operand: ['addonIds1', 'addonIds2', 'addonIds3'],
            operator: 'contains',
        };
        const businessSettings = {
            addonIds: ['addonIds5', 'addonIds4', 'addonIds6'],
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
    test('check condition enableCashback, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'enableCashback',
            operand: ['true'],
            operator: 'eq',
        };
        const businessSettings = {
            enableCashback: true,
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition enableCashback, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'enableCashback',
            operand: ['true'],
            operator: 'eq',
        };
        const businessSettings = {
            enableCashback: false,
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
    test('check condition haveBeepFreeShippingZone, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'haveBeepFreeShippingZone',
            operand: ['true'],
            operator: 'eq',
        };
        const businessSettings = {
            haveBeepFreeShippingZone: true,
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition haveBeepFreeShippingZone, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'haveBeepFreeShippingZone',
            operand: ['true'],
            operator: 'eq',
        };
        const businessSettings = {
            haveBeepFreeShippingZone: false,
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
    test('check condition qrOrderingSettings.searchingTags, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'qrOrderingSettings.searchingTags',
            operand: ['a', 'b', 'c'],
            operator: 'contains',
        };
        const businessSettings = {
            qrOrderingSettings: {
                searchingTags: ['c', 'd'],
            }
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition qrOrderingSettings.searchingTags, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'qrOrderingSettings.searchingTags',
            operand: ['a', 'b', 'c'],
            operator: 'contains',
        };
        const businessSettings = {
            qrOrderingSettings: {
                searchingTags: ['d', 'f'],
            },
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
    test('check condition qrOrderingSettings.marketingTags, true', () => {
        const condition = {
            entity: 'business',
            propertyName: 'qrOrderingSettings.marketingTags',
            operand: ['d', 'f'],
            operator: 'contains',
        };
        const businessSettings = {
            qrOrderingSettings: {
                marketingTags: ['d', 'f'],
            },
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(true);
    });
    test('check condition qrOrderingSettings.marketingTags, false', () => {
        const condition = {
            entity: 'business',
            propertyName: 'qrOrderingSettings.marketingTags',
            operand: ['d', 'f'],
            operator: 'contains',
        };
        const businessSettings = {
            qrOrderingSettings: {
                marketingTags: [],
            },
        };
        const result = calculator.checkConditionSatisfied(condition, businessSettings);
        expect(result).toBe(false);
    });
});
