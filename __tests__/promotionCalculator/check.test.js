import moment from 'moment-timezone';
import check, {
    checkPromotionValid,
    checkRequiredProducts,
} from '../../src/promotionCalculator/check';
import {
    SourceType,
    ChannelType,
    getBeepOrderSource,
    getOrderSource,
    ShippingType,
    PromotionType,
    ClientType,
} from '../../src/utils';
import combination from '../../src/promotionCalculator/combination';
import { PromotionError, ParameterError } from '../../src/promotionCalculator/error';

describe('promotion:check', () => {
    const promotion = {
        _id: 'pid',
        business: 'b',
        isEnabled: true,
        validFrom: moment()
            .tz('Asia/Kuala_Lumpur')
            .add(-1, 'day'),
        validTo: moment()
            .tz('Asia/Kuala_Lumpur')
            .add(1, 'day'),
        validTimeFrom: moment()
            .tz('Asia/Kuala_Lumpur')
            .add(-1, 'hour')
            .hour(),
        validTimeTo: moment()
            .tz('Asia/Kuala_Lumpur')
            .add(1, 'hour')
            .hour(),
        validDays: [1, 2, 3, 4, 5, 6, 7],
        requiredProducts: [{}],
        type: PromotionType.MERCHANT,
    };

    const transaction = { items: [{}], business: 'b', channel: ChannelType.ECOMMERCE };
    const business = { timezone: 'Asia/Kuala_Lumpur' };

    test('success', () => {
        jest.spyOn(combination, 'generatePossibleUnitCombos').mockReturnValueOnce([
            { id1: 1, id2: 2 },
        ]);
        expect(() => check(promotion, transaction, business)).not.toThrow();
    });

    test('fail', () => {
        jest.spyOn(combination, 'generatePossibleUnitCombos').mockReturnValueOnce([]);
        expect(() => check(promotion, transaction, business)).toThrow();
    });

    describe('checkPromotionValid', () => {
        test('check success', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, appliedStores: ['All'], appliedClientTypes: [ClientType.APP] },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.POS,
                        clientType: ClientType.APP,
                    },
                ),
            ).not.toThrow();
        });
        test('no promotion id', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, _id: undefined },
                    { timezone: 'Asia/Kuala_Lumpur', businessName: 'b', source: SourceType.POS },
                ),
            ).toThrowError(ParameterError);
        });
        test('not enable', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, isEnabled: false },
                    { timezone: 'Asia/Kuala_Lumpur', businessName: 'b', source: SourceType.POS },
                ),
            ).toThrowError(new PromotionError.NOT_ENABLE());
        });

        test('require source', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, applyToOnlineStore: true },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                    },
                ),
            ).toThrowError(new PromotionError.REQUIRE_SOURCE());
        });

        test('not enable for online store', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        applyToOnlineStore: false,
                        appliedSources: [SourceType.POS, SourceType.BEEP],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).toThrowError(new PromotionError.SOURCE_NOT_SATISFIED());
        });

        test('not enable for beep', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, appliedSources: [SourceType.POS, SourceType.ECOMMERCE] },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.BEEP_DELIVERY,
                    },
                ),
            ).toThrowError(new PromotionError.SOURCE_NOT_SATISFIED());
        });

        test('not enable for pos', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, appliedSources: [SourceType.BEEP] },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.POS,
                    },
                ),
            ).toThrowError(new PromotionError.SOURCE_NOT_SATISFIED());
        });

        test('not in date range', () => {
            const validFrom = moment('2019-01-01').tz('Asia/Kuala_Lumpur');
            const validTo = moment('2019-01-03').tz('Asia/Kuala_Lumpur');

            expect(() =>
                checkPromotionValid(
                    { ...promotion, validFrom, validTo },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).toThrow(new PromotionError.DATE_NOT_SATISFIED());
        });
        test('not in weekday range', () => {
            const tomorrow =
                moment()
                    .add(1, 'day')
                    .weekday() + 1;
            const validDays = [tomorrow];

            expect(() =>
                checkPromotionValid(
                    { ...promotion, validDays },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).toThrowError(new PromotionError.WEEKDAY_NOT_SATISFIED());
        });

        test('empty array for weekday condition', () => {
            const validDays = [];

            expect(() =>
                checkPromotionValid(
                    { ...promotion, validDays },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).not.toThrow();
        });
        test('not in time range', () => {
            const validTimeFrom = moment()
                .add(1, 'hour')
                .hour();
            const validTimeTo = moment()
                .add(2, 'hour')
                .hour();

            expect(() =>
                checkPromotionValid(
                    { ...promotion, validTimeTo, validTimeFrom },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: true,
                        businessName: 'b',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).toThrowError(new PromotionError.TIME_NOT_SATISFIED());
        });

        test('promotion is deleted', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, isDeleted: true },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: true,
                        businessName: 'b',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).toThrowError(new PromotionError.DELETED_PROMOTION());
        });

        test('store is not applicable', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        appliedStores: ['sid2'],
                        appliedSources: [SourceType.BEEP_DELIVERY, SourceType.ECOMMERCE],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        storeId: 'sid',
                        source: SourceType.BEEP_DELIVERY,
                    },
                ),
            ).toThrowError(new PromotionError.STORE_NOT_SATISFIED());

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        appliedStores: ['sid'],
                        appliedSources: [SourceType.ECOMMERCE],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        storeId: 'sid',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).not.toThrowError(new PromotionError.STORE_NOT_SATISFIED());

            expect(() =>
                checkPromotionValid(
                    { ...promotion, appliedStores: ['sid'] },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        source: SourceType.POS,
                        businessName: 'b',
                        storeId: 'sid',
                    },
                ),
            ).not.toThrow();

            expect(() =>
                checkPromotionValid(
                    { ...promotion },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        source: SourceType.ECOMMERCE,
                        businessName: 'b',
                        storeId: 'sid',
                    },
                ),
            ).not.toThrow();
        });

        test('transaction business is not the same as promotion business', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, business: 'a' },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        storeId: 'sid',
                    },
                ),
            ).toThrowError(new PromotionError.REQUIRE_SAME_BUSINESS());
        });

        test('use universal promotion', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, business: 'a', type: PromotionType.UNIVERSAL },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        storeId: 'sid',
                    },
                ),
            ).toBeTruthy();
        });

        test('use universal promotion which requireFirstPurchase', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: 'a',
                        type: PromotionType.UNIVERSAL,
                        requireFirstPurchase: true,
                        appliedStores: ['All'],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        hasUniversalFirstPurchase: true,
                        storeId: 'sid',
                        source: SourceType.POS,
                    },
                ),
            ).toThrowError(new PromotionError.REQUIRE_FIRST_TIME_PURCHASE());
        });

        test('use isBindCampaign promotion', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: 'a',
                        type: PromotionType.UNIVERSAL,
                        appliedStores: ['All'],
                        requireFirstPurchase: false,
                        isBindCampaign: true,
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        hasUniversalFirstPurchase: true,
                        storeId: 'sid',
                        source: SourceType.POS,
                    },
                ),
            ).toThrowError(new PromotionError.PROMOTION_CUSTOMER());
        });

        test('use isBindCampaign promotion to checkPromotionCustomerValid', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: 'a',
                        type: PromotionType.UNIVERSAL,
                        appliedStores: ['All'],
                        requireFirstPurchase: false,
                        isBindCampaign: true,
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        hasUniversalFirstPurchase: true,
                        storeId: 'sid',
                        source: SourceType.POS,
                        customerId: '666',
                        promotionCustomers: [
                            {
                                validFrom: moment()
                                    .tz('Asia/Kuala_Lumpur')
                                    .add(-1, 'day'),
                                validTo: moment()
                                    .tz('Asia/Kuala_Lumpur')
                                    .add(1, 'day'),
                                restClaimCount: 2,
                                customerId: '666',
                            },
                        ],
                    },
                ),
            ).not.toThrowError();
        });

        test('use isBindCampaign promotion to checkPromotionCustomerValid with out customerId', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: 'a',
                        type: PromotionType.UNIVERSAL,
                        appliedStores: ['All'],
                        requireFirstPurchase: false,
                        isBindCampaign: true,
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        hasUniversalFirstPurchase: true,
                        storeId: 'sid',
                        source: SourceType.POS,
                        promotionCustomers: [
                            {
                                validFrom: moment(),
                                validTo: moment(),
                                restClaimCount: -1,
                                customerId: '666',
                            },
                        ],
                    },
                ),
            ).toThrowError(new PromotionError.PROMOTION_CUSTOMER());
        });

        test('reach max claim count', () => {
            const option = {
                timezone: 'Asia/Kuala_Lumpur',
                businessName: 'b',
                storeId: 'sid',
                isOnlineTransaction: false,
                source: SourceType.ECOMMERCE,
            };
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enableClaimLimit: true,
                        claimedCount: 1,
                        maxClaimCount: 2,
                    },
                    option,
                ),
            ).not.toThrow();

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enableClaimLimit: false,
                        claimedCount: 2,
                        maxClaimCount: 2,
                    },
                    option,
                ),
            ).not.toThrow();

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enableClaimLimit: true,
                        claimedCount: 2,
                        maxClaimCount: 2,
                    },
                    option,
                ),
            ).toThrowError(new PromotionError.REACH_MAX_CLAIM_COUNT());

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enableClaimLimit: true,
                        maxClaimCount: 0,
                    },
                    option,
                ),
            ).toThrowError(new PromotionError.REACH_MAX_CLAIM_COUNT());

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enableClaimLimit: true,
                    },
                    option,
                ),
            ).toThrowError(new PromotionError.REACH_MAX_CLAIM_COUNT());
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enableClaimLimit: true,
                    },
                    {
                        ...option,
                        source: SourceType.POS,
                    },
                ),
            ).not.toThrowError(new PromotionError.REACH_MAX_CLAIM_COUNT());
        });

        test('invalid userId + enablePerCustomerClaimLimit = true', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, business: 'b', enablePerCustomerClaimLimit: true },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        storeId: 'sid',
                    },
                ),
            ).toThrowError(new PromotionError.REQUIRE_CUSTOMER());
        });

        test('valid userId + enablePerCustomerClaimLimit = true', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, business: 'b', enablePerCustomerClaimLimit: true },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        storeId: 'sid',
                        userId: 'xxx',
                        source: SourceType.BEEP_QR,
                    },
                ),
            ).not.toThrowError(new PromotionError.REQUIRE_CUSTOMER());
        });

        test('valid userId + enablePerCustomerClaimLimit = true + channel beep + invalid customer claim count', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, business: 'b', enablePerCustomerClaimLimit: true },
                    {
                        channel: ChannelType.BEEP,
                        timezone: 'Asia/Kuala_Lumpur',
                        isOnlineTransaction: false,
                        businessName: 'b',
                        storeId: 'sid',
                        userId: 'xxx',
                    },
                ),
            ).toThrowError(new ParameterError('customerClaimCount'));
        });

        test('valid userId + enablePerCustomerClaimLimit = true + channel beep + invalid customer claim count', () => {
            expect(() =>
                checkPromotionValid(
                    { ...promotion, business: 'b', enablePerCustomerClaimLimit: true },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        storeId: 'sid',
                        userId: 'xxx',
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).not.toThrowError();
        });

        test('valid userId + enablePerCustomerClaimLimit = true + channel beep + not reach customer claim count', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: 'b',
                        enablePerCustomerClaimLimit: true,
                        perCustomerClaimLimit: 1,
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        storeId: 'sid',
                        userId: 'xxx',
                        customerClaimCount: 0,
                        source: SourceType.ECOMMERCE,
                    },
                ),
            ).not.toThrowError();
        });

        test('valid userId + enablePerCustomerClaimLimit = true + channel beep + reach customer claim count', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: 'a',
                        enablePerCustomerClaimLimit: true,
                        perCustomerClaimLimit: 1,
                    },
                    {
                        source: SourceType.ECOMMERCE,
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        storeId: 'sid',
                        userId: 'xxx',
                        customerClaimCount: 1,
                    },
                ),
            ).toThrowError(new PromotionError.REACH_CUSTOMER_CLAIM_COUNT_LIMIT());
        });

        test('client type not satisfied', () => {
            const checkFailedPromotion = {
                ...promotion,
                appliedClientTypes: [ClientType.APP],
            };
            expect(() =>
                checkPromotionValid(checkFailedPromotion, {
                    source: SourceType.ECOMMERCE,
                    timezone: 'Asia/Kuala_Lumpur',
                    businessName: 'b',
                    storeId: 'sid',
                    userId: 'xxx',
                    clientType: ClientType.WEB,
                }),
            ).toThrowError(new PromotionError.CLIENT_NOT_SATISFIED());
        });

        test('reach bussiness max claim count', () => {
            const option = {
                timezone: 'Asia/Kuala_Lumpur',
                businessName: 'b',
                storeId: 'sid',
                isOnlineTransaction: true,
                source: SourceType.ECOMMERCE,
                businessClaimedCount: 1,
            };
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enablePerBusinessClaimLimit: true,
                        perBusinessClaimLimit: 2,
                    },
                    option,
                ),
            ).not.toThrow();

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        business: option.businessName,
                        enablePerBusinessClaimLimit: true,
                        perBusinessClaimLimit: 1,
                    },
                    option,
                ),
            ).toThrowError(new PromotionError.REACH_MAX_BUSINESS_CLAIM_COUNT());
        });

        test('check hasUniversalFirstPurchase', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        type: PromotionType.UNIVERSAL,
                        requireFirstPurchase: true,
                        appliedStores: ['All'],
                        appliedClientTypes: [ClientType.APP],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.POS,
                        clientType: ClientType.APP,
                        hasUniversalFirstPurchase: true,
                    },
                ),
            ).toThrowError(new PromotionError.REQUIRE_FIRST_TIME_PURCHASE());
        });

        test('check checkPromotionCustomerValid without promotionCustomers', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        type: PromotionType.UNIVERSAL,
                        requireFirstPurchase: true,
                        isBindCampaign: true,
                        appliedStores: ['All'],
                        appliedClientTypes: [ClientType.APP],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.POS,
                        clientType: ClientType.APP,
                    },
                ),
            ).toThrowError(new PromotionError.PROMOTION_CUSTOMER());
        });

        test('check checkPromotionCustomerValid with empty promotionCustomers', () => {
            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        type: PromotionType.UNIVERSAL,
                        requireFirstPurchase: true,
                        isBindCampaign: true,
                        appliedStores: ['All'],
                        appliedClientTypes: [ClientType.APP],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.POS,
                        clientType: ClientType.APP,
                        promotionCustomers: [],
                    },
                ),
            ).toThrowError(new PromotionError.PROMOTION_CUSTOMER());
        });

        test('check checkPromotionCustomerValid with promotionCustomers but without customerId', () => {
            const validFrom = moment('2019-01-01').tz('Asia/Kuala_Lumpur');
            const validTo = moment('2039-01-03').tz('Asia/Kuala_Lumpur');

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        type: PromotionType.UNIVERSAL,
                        requireFirstPurchase: true,
                        isBindCampaign: true,
                        appliedStores: ['All'],
                        appliedClientTypes: [ClientType.APP],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.POS,
                        customerId: '123456',
                        clientType: ClientType.APP,
                        promotionCustomers: [{ validFrom, validTo, restClaimCount: 10 }],
                    },
                ),
            ).toThrowError(new PromotionError.PROMOTION_CUSTOMER());
        });

        test('check checkPromotionCustomerValid with promotionCustomers true', () => {
            const validFrom = moment('2019-01-01').tz('Asia/Kuala_Lumpur');
            const validTo = moment('2039-01-03').tz('Asia/Kuala_Lumpur');

            expect(() =>
                checkPromotionValid(
                    {
                        ...promotion,
                        type: PromotionType.UNIVERSAL,
                        requireFirstPurchase: true,
                        isBindCampaign: true,
                        appliedStores: ['All'],
                        appliedClientTypes: [ClientType.APP],
                    },
                    {
                        timezone: 'Asia/Kuala_Lumpur',
                        businessName: 'b',
                        source: SourceType.POS,
                        customerId: '123456',
                        clientType: ClientType.APP,
                        promotionCustomers: [
                            { validFrom, validTo, restClaimCount: 10, customerId: '123456' },
                        ],
                    },
                ),
            ).not.toThrow();
        });
    });

    describe('checkRequiredProductsResult', () => {
        test('able to find product combos', () => {
            const checkSuccessPromotion = {
                ...promotion,
                requiredProducts: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                    },
                ],
            };
            expect(() =>
                checkRequiredProducts(checkSuccessPromotion, {
                    items: [
                        {
                            id: 'id',
                            availableQuantity: 1,
                            productId: 'pid',
                            product: { _id: 'pid', category: 'c1' },
                        },
                    ],
                }),
            ).not.toThrow();
        });
        test('not able to find product combos', () => {
            const checkFailedPromotion = {
                ...promotion,
                requiredProducts: [
                    {
                        operand: ['c1', 'c2'],
                        entity: 'product',
                        propertyName: 'category',
                        operator: 'in',
                        minQuantity: 3,
                        maxQuantity: 3,
                    },
                ],
            };
            expect(() => checkRequiredProducts(checkFailedPromotion, { items: [{}] })).toThrowError(
                new PromotionError.REQUIRED_PRODUCTS_NOT_SATISFIED(),
            );
        });
    });

    describe('getBeepOrderSource', () => {
        test.each([
            [{ shippingType: ShippingType.DINE_IN }, SourceType.BEEP_DINE_IN],
            [{ shippingType: ShippingType.TAKEAWAY }, SourceType.BEEP_TAKEAWAY],
            [{ shippingType: ShippingType.PICKUP }, SourceType.BEEP_PICKUP],
            [{ shippingType: ShippingType.DELIVERY }, SourceType.BEEP_DELIVERY],
        ])('getOrderType (%j :%d)', (order, expected) => {
            expect(getBeepOrderSource(order)).toBe(expected);
        });
    });
    describe('getOrderSource', () => {
        test.each([
            [{ channel: ChannelType.POS }, SourceType.POS],
            [{ channel: ChannelType.BEEP }, SourceType.UNKNOWN],
            [
                { channel: ChannelType.BEEP, shippingType: ShippingType.TAKEAWAY },
                SourceType.BEEP_TAKEAWAY,
            ],
            [
                { channel: ChannelType.BEEP, shippingType: ShippingType.DINE_IN },
                SourceType.BEEP_DINE_IN,
            ],
            [
                { channel: ChannelType.BEEP, shippingType: ShippingType.DELIVERY },
                SourceType.BEEP_DELIVERY,
            ],
            [
                { channel: ChannelType.BEEP, shippingType: ShippingType.PICKUP },
                SourceType.BEEP_PICKUP,
            ],
            [{ channel: ChannelType.ECOMMERCE }, SourceType.ECOMMERCE],
            [{ channel: -1 }, SourceType.UNKNOWN],
        ])('getOrderType (%j :%d)', (order, expected) => {
            expect(getOrderSource(order)).toBe(expected);
        });
    });
});
