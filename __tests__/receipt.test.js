import receipt from '../src/receipt';
import calculate from '../src/calculator';
import promotionCalculator from '../src/promotionCalculator';
import { ChannelType } from '../src/utils';

describe('receipt:jest', () => {
    test('calculator:receipt:MY Func:receipt:taxExclusiveDisplay:item', () => {
        const transaction = {
            discount: 0,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    display: {
                        subtotal: 535.71,
                        tax: 64.29,
                        total: 535.71,
                    },
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                },
            ],
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        };
        const result = receipt(transaction, false, 'MY');
        expect(result.items[0].receipt).toEqual({
            total: 535.71,
            a4Total: 535.71,
            price: 535.71,
            qty: 1,
            discount: 0,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            discount: 0,
            receiptTotalExcTax: 535.71,
            serviceCharge: 0,
            tax: 64.29,
            total: 600,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxExclusiveDisplay:item, hasDiscountColumn', () => {
        const transaction = {
            discount: 0,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    display: {
                        subtotal: 535.71,
                        tax: 64.29,
                        total: 535.71,
                    },
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                },
            ],
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        };
        const result = receipt(transaction, false, 'MY', true);
        expect(result.items[0].receipt).toEqual({
            total: 535.71,
            a4Total: 535.71,
            price: 535.71,
            qty: 1,
            discount: 0,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            discount: 0,
            receiptTotalExcTax: 535.71,
            serviceCharge: 0,
            tax: 64.29,
            total: 600,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxInclusiveDisplay:item', () => {
        const transaction = {
            discount: 0,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    display: {
                        subtotal: 600,
                        tax: 64.29,
                        total: 600,
                    },
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                },
            ],
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        };
        const result = receipt(transaction, true, 'MY');
        expect(result.items[0].receipt).toEqual({
            total: 600,
            a4Total: 600,
            price: 600,
            qty: 1,
            discount: 0,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            discount: 0,
            receiptTotalExcTax: 535.71,
            serviceCharge: 0,
            tax: 64.29,
            total: 600,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxInclusiveDisplay:item, hasDiscountColumn', () => {
        const transaction = {
            discount: 0,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    display: {
                        subtotal: 600,
                        tax: 64.29,
                        total: 600,
                    },
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                },
            ],
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        };
        const result = receipt(transaction, true, 'MY', true);
        expect(result.items[0].receipt).toEqual({
            total: 600,
            a4Total: 600,
            price: 600,
            qty: 1,
            discount: 0,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            discount: 0,
            receiptTotalExcTax: 535.71,
            serviceCharge: 0,
            tax: 64.29,
            total: 600,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            discount: 491.07,
            display: {
                discount: -44.65,
                serviceCharge: 4.46,
                subtotal: 89.29,
                tax: 5.9,
                total: 55,
            },
            items: [
                {
                    adhocDiscount: 44.65,
                    discount: 491.07,
                    display: {
                        subtotal: 535.71,
                        tax: 10.71,
                        total: 89.29,
                    },
                    fullBillDiscount: 44.65,
                    promotions: [
                        {
                            discount: 446.42,
                            inputValue: 83.3333333,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    subTotal: 535.71,
                    tax: 5.36,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 44.64,
                    unitPrice: 267.857143,
                },
                {
                    inputValue: 50,
                    itemType: 'Discount',
                    taxRate: 0.12,
                    type: 'percent',
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 4.46,
                    tax: 0.54,
                    taxRate: 0.12,
                    total: 4.46,
                    unitPrice: 4.464000000000005,
                },
            ],
            serviceCharge: 4.46,
            serviceChargeTax: 0.54,
            subtotal: 535.71,
            tax: 5.9,
            total: 55,
        };
        const result = receipt(transaction, false, 'MY');
        expect(result.items[0].receipt).toEqual({
            total: 535.71,
            a4Total: 44.64,
            price: 535.71,
            qty: 1,
            discount: 491.07,
            discountWithoutPromo: 44.65,
        });
        expect(result.receipt).toEqual({
            discount: 491.07,
            receiptTotalExcTax: 535.71,
            serviceCharge: 4.46,
            tax: 5.9,
            total: 55,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge, hasDiscountColumn', () => {
        const transaction = {
            discount: 491.07,
            display: {
                discount: -44.65,
                serviceCharge: 4.46,
                subtotal: 89.29,
                tax: 5.9,
                total: 55,
            },
            items: [
                {
                    adhocDiscount: 44.65,
                    discount: 491.07,
                    display: {
                        subtotal: 535.71,
                        tax: 10.71,
                        total: 89.29,
                    },
                    fullBillDiscount: 44.65,
                    promotions: [
                        {
                            discount: 446.42,
                            inputValue: 83.3333333,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    subTotal: 535.71,
                    tax: 5.36,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 44.64,
                    unitPrice: 267.857143,
                },
                {
                    inputValue: 50,
                    itemType: 'Discount',
                    taxRate: 0.12,
                    type: 'percent',
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 4.46,
                    tax: 0.54,
                    taxRate: 0.12,
                    total: 4.46,
                    unitPrice: 4.464000000000005,
                },
            ],
            serviceCharge: 4.46,
            serviceChargeTax: 0.54,
            subtotal: 535.71,
            tax: 5.9,
            total: 55,
        };
        const result = receipt(transaction, false, 'MY', true);
        expect(result.items[0].receipt).toEqual({
            total: 44.64,
            a4Total: 44.64,
            price: 535.71,
            qty: 1,
            discount: 491.07,
            discountWithoutPromo: 44.65,
        });
        expect(result.receipt).toEqual({
            discount: 491.07,
            receiptTotalExcTax: 535.71,
            serviceCharge: 4.46,
            tax: 5.9,
            total: 55,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
        const transaction = {
            discount: 491.07,
            display: {
                discount: -44.65,
                serviceCharge: 4.46,
                subtotal: 89.29,
                tax: 5.9,
                total: 55,
            },
            items: [
                {
                    adhocDiscount: 44.64285714285714,
                    discount: 491.07,
                    display: {
                        subtotal: 600,
                        tax: 10.71,
                        total: 100,
                    },
                    fullBillDiscount: 50,
                    promotions: [
                        {
                            discount: 446.4285714285714,
                            inputValue: 83.3333333,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    subTotal: 535.71,
                    tax: 5.36,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 44.64,
                    unitPrice: 267.857143,
                },
                {
                    inputValue: 50,
                    itemType: 'Discount',
                    taxRate: 0.12,
                    type: 'percent',
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 4.46,
                    tax: 0.54,
                    taxRate: 0.12,
                    total: 4.46,
                    unitPrice: 4.464000000000005,
                },
            ],
            serviceCharge: 4.46,
            serviceChargeTax: 0.54,
            subtotal: 535.71,
            tax: 5.9,
            total: 55,
        };
        const result = receipt(transaction, true, 'MY');
        expect(result.items[0].receipt).toEqual({
            total: 600,
            a4Total: 108.93,
            price: 600,
            qty: 1,
            discount: 491.07,
            discountWithoutPromo: 44.64,
        });
        expect(result.receipt).toEqual({
            discount: 491.07,
            receiptTotalExcTax: 535.71,
            serviceCharge: 4.46,
            tax: 5.9,
            total: 55,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge, hasDiscountColumn', () => {
        const transaction = {
            discount: 491.07,
            display: {
                discount: -44.65,
                serviceCharge: 4.46,
                subtotal: 89.29,
                tax: 5.9,
                total: 55,
            },
            items: [
                {
                    adhocDiscount: 44.64285714285714,
                    discount: 491.07,
                    display: {
                        subtotal: 600,
                        tax: 10.71,
                        total: 100,
                    },
                    fullBillDiscount: 50,
                    promotions: [
                        {
                            discount: 446.4285714285714,
                            inputValue: 83.3333333,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    subTotal: 535.71,
                    tax: 5.36,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 44.64,
                    unitPrice: 267.857143,
                },
                {
                    inputValue: 50,
                    itemType: 'Discount',
                    taxRate: 0.12,
                    type: 'percent',
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 4.46,
                    tax: 0.54,
                    taxRate: 0.12,
                    total: 4.46,
                    unitPrice: 4.464000000000005,
                },
            ],
            serviceCharge: 4.46,
            serviceChargeTax: 0.54,
            subtotal: 535.71,
            tax: 5.9,
            total: 55,
        };
        const result = receipt(transaction, true, 'MY', true);
        expect(result.items[0].receipt).toEqual({
            total: 108.93,
            a4Total: 108.93,
            price: 600,
            qty: 1,
            discount: 491.07,
            discountWithoutPromo: 44.64,
        });
        expect(result.receipt).toEqual({
            discount: 491.07,
            receiptTotalExcTax: 535.71,
            serviceCharge: 4.46,
            tax: 5.9,
            total: 55,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:MY Func:receipt:taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge:Reprint', () => {
        const transaction = {
            discount: 491.07,
            items: [
                {
                    adhocDiscount: 44.64285714285714,
                    discount: 491.07,
                    promotions: [
                        {
                            discount: 446.4285714285714,
                            inputValue: 83.3333333,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    subTotal: 535.71,
                    tax: 5.36,
                    taxRate: 0.12,
                    total: 44.64,
                    unitPrice: 267.857143,
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 4.46,
                    tax: 0.54,
                    taxRate: 0.12,
                    total: 4.46,
                    unitPrice: 4.464000000000005,
                },
            ],
            serviceCharge: 4.46,
            serviceChargeTax: 0.54,
            subtotal: 535.71,
            tax: 5.9,
            total: 55,
        };
        const result = receipt(transaction, true, 'MY');
        expect(result.items[0].receipt).toEqual({
            total: 600.03,
            a4Total: 108.96,
            price: 600.03,
            qty: 1,
            discount: 491.07,
            discountWithoutPromo: 44.64,
        });
        expect(result.receipt).toEqual({
            discount: 491.07,
            receiptTotalExcTax: 535.71,
            serviceCharge: 4.46,
            tax: 5.9,
            total: 55,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:TH Func:receipt:taxExclusiveDisplay:item', () => {
        const transaction = {
            discount: 0,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    display: {
                        subtotal: 535.71,
                        tax: 64.29,
                        total: 535.71,
                    },
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                },
            ],
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        };
        const result = receipt(transaction, false, 'TH');
        expect(result.items[0].receipt).toEqual({
            total: 535.71,
            a4Total: 535.71,
            price: 535.71,
            qty: 1,
            discount: 0,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            discount: 0,
            receiptTotalExcTax: 535.71,
            serviceCharge: 0,
            tax: 64.29,
            total: 600,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxInclusiveDisplay:item, ServiceCharge, BirFnb', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 8.92857142,
                    quantity: 1,
                    taxRate: 0.12,
                    adhocDiscount: 0,
                    total: 8.03,
                    tax: 0.54,
                    discount: 0.89,
                    subTotal: 8.93,
                    seniorDiscount: 0,
                    pwdDiscount: 0.89,
                    taxableAmount: 4.46,
                    taxExemptAmount: 4.46,
                    totalDeductedTax: 0.54,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                    unitPrice: 0.893,
                    quantity: 1,
                    adhocDiscount: 0,
                    total: 0.81,
                    tax: 0.05,
                    discount: 0.09,
                    subTotal: 0.89,
                    seniorDiscount: 0,
                    pwdDiscount: 0.09,
                    taxableAmount: 0.45,
                    taxExemptAmount: 0.45,
                    totalDeductedTax: 0.05,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
            subtotal: 8.93,
            discount: 0.98,
            tax: 0.59,
            total: 9.43,
            serviceCharge: 0.81,
            serviceChargeTax: 0.05,
            seniorDiscount: 0,
            pwdDiscount: 0.89,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            totalDeductedTax: 0.54,
            display: {
                subtotal: 8.03,
                discount: 0,
                serviceCharge: 0.81,
                tax: 0.59,
                total: 9.43,
            },
        };
        const result = receipt(transaction, true, 'PH');
        expect(result.items[0].receipt).toEqual({
            total: 10,
            a4Total: 9.11,
            price: 10,
            qty: 1,
            discount: 0.89,
            discountWithoutPromo: 0.89,
        });
        expect(result.receipt).toEqual({
            subtotal: 10.09,
            lessVAT: 1.13,
            discountable: 8.93,
            adhocDiscount: 0.09,
            seniorDiscount: 0,
            receiptTotalExcTax: 8.93,
            pwdDiscount: 0.89,
            vatAmount: 0.54,
            serviceCharge: 0.86,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            medalOfValorDiscount: 0,
            total: 9.43,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxInclusiveDisplay:item, ServiceCharge, BirFnb, hasDiscountColumn', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 8.92857142,
                    quantity: 1,
                    taxRate: 0.12,
                    adhocDiscount: 0,
                    total: 8.03,
                    tax: 0.54,
                    discount: 0.89,
                    subTotal: 8.93,
                    seniorDiscount: 0,
                    pwdDiscount: 0.89,
                    taxableAmount: 4.46,
                    taxExemptAmount: 4.46,
                    totalDeductedTax: 0.54,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                    unitPrice: 0.893,
                    quantity: 1,
                    adhocDiscount: 0,
                    total: 0.81,
                    tax: 0.05,
                    discount: 0.09,
                    subTotal: 0.89,
                    seniorDiscount: 0,
                    pwdDiscount: 0.09,
                    taxableAmount: 0.45,
                    taxExemptAmount: 0.45,
                    totalDeductedTax: 0.05,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
            subtotal: 8.93,
            discount: 0.98,
            tax: 0.59,
            total: 9.43,
            serviceCharge: 0.81,
            serviceChargeTax: 0.05,
            seniorDiscount: 0,
            pwdDiscount: 0.89,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            totalDeductedTax: 0.54,
            display: {
                subtotal: 8.03,
                discount: 0,
                serviceCharge: 0.81,
                tax: 0.59,
                total: 9.43,
            },
        };
        const result = receipt(transaction, true, 'PH', true);
        expect(result.items[0].receipt).toEqual({
            total: 9.11,
            a4Total: 9.11,
            price: 10,
            qty: 1,
            discount: 0.89,
            discountWithoutPromo: 0.89,
        });
        expect(result.receipt).toEqual({
            subtotal: 10.09,
            lessVAT: 1.13,
            discountable: 8.93,
            adhocDiscount: 0.09,
            seniorDiscount: 0,
            pwdDiscount: 0.89,
            vatAmount: 0.54,
            serviceCharge: 0.86,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            receiptTotalExcTax: 8.93,
            medalOfValorDiscount: 0,
            total: 9.43,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, ServiceCharge, BirFnb', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 8.92857142,
                    quantity: 1,
                    taxRate: 0.12,
                    adhocDiscount: 0,
                    total: 8.03,
                    tax: 0.54,
                    discount: 0.89,
                    subTotal: 8.93,
                    seniorDiscount: 0,
                    pwdDiscount: 0.89,
                    taxableAmount: 4.46,
                    taxExemptAmount: 4.46,
                    totalDeductedTax: 0.54,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                    unitPrice: 0.893,
                    quantity: 1,
                    adhocDiscount: 0,
                    total: 0.81,
                    tax: 0.05,
                    discount: 0.09,
                    subTotal: 0.89,
                    seniorDiscount: 0,
                    pwdDiscount: 0.09,
                    taxableAmount: 0.45,
                    taxExemptAmount: 0.45,
                    totalDeductedTax: 0.05,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
            addonBirCompliance: {
                discountType: 'SC/PWD',
                athleteAndCoachDiscount: 0.03,
                medalOfValorDiscount: 0.03,
                soloParentDiscount: 0,
            },
            subtotal: 8.93,
            discount: 0.98,
            tax: 0.59,
            total: 9.43,
            serviceCharge: 0.81,
            serviceChargeTax: 0.05,
            seniorDiscount: 0,
            pwdDiscount: 0.89,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            totalDeductedTax: 0.54,
            display: {
                subtotal: 8.03,
                discount: 0,
                serviceCharge: 0.81,
                tax: 0.59,
                total: 9.43,
            },
        };
        const result = receipt(transaction, false, 'PH');
        expect(result.items[0].receipt).toEqual({
            total: 8.93,
            a4Total: 8.04,
            price: 8.93,
            qty: 1,
            discount: 0.89,
            discountWithoutPromo: 0.89,
        });
        expect(result.receipt).toEqual({
            subtotal: 10.09,
            lessVAT: 1.13,
            discountable: 8.93,
            adhocDiscount: 0.03,
            receiptTotalExcTax: 8.93,
            seniorDiscount: 0,
            soloParentDiscount: 0,
            athleteAndCoachDiscount: 0.03,
            medalOfValorDiscount: 0.03,
            pwdDiscount: 0.89,
            vatAmount: 0.54,
            serviceCharge: 0.86,
            total: 9.43,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, ServiceCharge, BirFnb, hasDiscountColumn', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 8.92857142,
                    quantity: 1,
                    taxRate: 0.12,
                    adhocDiscount: 0,
                    total: 8.03,
                    tax: 0.54,
                    discount: 0.89,
                    subTotal: 8.93,
                    seniorDiscount: 0,
                    pwdDiscount: 0.89,
                    taxableAmount: 4.46,
                    taxExemptAmount: 4.46,
                    totalDeductedTax: 0.54,
                },
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                    unitPrice: 0.893,
                    quantity: 1,
                    adhocDiscount: 0,
                    total: 0.81,
                    tax: 0.05,
                    discount: 0.09,
                    subTotal: 0.89,
                    seniorDiscount: 0,
                    pwdDiscount: 0.09,
                    taxableAmount: 0.45,
                    taxExemptAmount: 0.45,
                    totalDeductedTax: 0.05,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 1,
                headCount: 2,
            },
            addonBirCompliance: {
                discountType: 'SC/PWD',
                athleteAndCoachDiscount: 0.03,
                medalOfValorDiscount: 0.03,
                soloParentDiscount: 0,
            },
            subtotal: 8.93,
            discount: 0.98,
            tax: 0.59,
            total: 9.43,
            serviceCharge: 0.81,
            serviceChargeTax: 0.05,
            seniorDiscount: 0,
            pwdDiscount: 0.89,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            totalDeductedTax: 0.54,
            display: {
                subtotal: 8.03,
                discount: 0,
                serviceCharge: 0.81,
                tax: 0.59,
                total: 9.43,
            },
        };
        const result = receipt(transaction, false, 'PH', true);
        expect(result.items[0].receipt).toEqual({
            total: 8.04,
            a4Total: 8.04,
            price: 8.93,
            qty: 1,
            discount: 0.89,
            discountWithoutPromo: 0.89,
        });
        expect(result.receipt).toEqual({
            subtotal: 10.09,
            lessVAT: 1.13,
            discountable: 8.93,
            adhocDiscount: 0.03,
            receiptTotalExcTax: 8.93,
            seniorDiscount: 0,
            soloParentDiscount: 0,
            athleteAndCoachDiscount: 0.03,
            medalOfValorDiscount: 0.03,
            pwdDiscount: 0.89,
            vatAmount: 0.54,
            serviceCharge: 0.86,
            total: 9.43,
            taxableSales: 4.46,
            taxExemptedSales: 4.46,
            zeroRatedSales: 0,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, Non-BIR', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 1766.3551401869158,
                    quantity: 1,
                    taxRate: 0.07,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res).toEqual({
            amusementTax: 0,
            calculation: {
                discounts: [],
                original: {
                    subtotal: 1766.36,
                    tax: 123.64,
                    total: 1890,
                },
                taxes: [],
            },
            fixedFee: 0,
            isBirDiscountAvailable: false,
            items: [
                {
                    unitPrice: 1766.3551401869158,
                    quantity: 1,
                    taxRate: 0.07,
                    adhocDiscount: 0,
                    total: 1766.36,
                    tax: 123.64,
                    discount: 0,
                    subTotal: 1766.36,
                    calculation: {
                        discounts: [],
                        fullPrice: 1766.3551401869158,
                        original: {
                            subtotal: 1766.36,
                            tax: 123.64,
                            total: 1890,
                        },
                        taxes: [],
                    },
                    display: {
                        subtotal: 1890,
                        tax: 123.64,
                        total: 1890,
                    },
                    notRoundedOriginalTax: 123.6400000000001,
                },
            ],
            maximumDiscountInputValue: 1890,
            subtotal: 1766.36,
            discount: 0,
            takeawayCharges: 0,
            tax: 123.64,
            total: 1890,
            serviceCharge: 0,
            serviceChargeTax: 0,
            seniorDiscount: 0,
            pwdDiscount: 0,
            taxableSales: 0,
            taxExemptedSales: 0,
            zeroRatedSales: 0,
            totalDeductedTax: 0,
            display: {
                subtotal: 1766.36,
                discount: 0,
                serviceCharge: 0,
                tax: 123.64,
                total: 1890,
            },
        });

        const result = receipt(res, true, 'PH');
        expect(result.items[0].receipt).toEqual({
            total: 1890.0, // 1890.00
            a4Total: 1890.0,
            price: 1890.0, // 1890.00
            discount: 0,
            qty: 1,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            adhocDiscount: 0,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountable: 1766.36,
            lessVAT: 123.64,
            receiptTotalExcTax: 1766.36,
            medalOfValorDiscount: 0,
            takeawayCharges: 0,
            pwdDiscount: 0,
            serviceCharge: 0,
            subtotal: 1890,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 1890,
            vatAmount: 123.64,
            zeroRatedSales: 0,
            seniorDiscount: 0,
            amusementTax: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge:Reprint', () => {
        const transaction = {
            discount: 491.07,
            items: [
                {
                    adhocDiscount: 44.64285714285714,
                    discount: 491.07,
                    promotions: [
                        {
                            discount: 446.4285714285714,
                            inputValue: 83.3333333,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    selectedOptions: [
                        {
                            optionValue: 89.2857143,
                            quantity: 1,
                        },
                        {
                            optionValue: 44.6428571,
                            quantity: 4,
                        },
                    ],
                    subTotal: 535.71,
                    tax: 5.36,
                    taxRate: 0.12,
                    total: 44.64,
                    unitPrice: 267.857143,
                },
                {
                    discount: 0,
                    itemType: 'ServiceCharge',
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 4.46,
                    tax: 0.54,
                    taxRate: 0.12,
                    total: 4.46,
                    unitPrice: 4.464000000000005,
                },
            ],
            serviceCharge: 4.46,
            serviceChargeTax: 0.54,
            subtotal: 535.71,
            tax: 5.9,
            total: 55,
        };
        const result = receipt(transaction, true, 'PH');
        expect(result.items[0].receipt).toEqual({
            total: 600,
            a4Total: 108.93,
            price: 600,
            qty: 1,
            discount: 491.07,
            discountWithoutPromo: 44.64,
        });
        expect(result.receipt).toEqual({
            adhocDiscount: 491.07,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountable: 535.71,
            lessVAT: 5.9,
            medalOfValorDiscount: 0,
            pwdDiscount: 0,
            seniorDiscount: 0,
            receiptTotalExcTax: 535.71,
            serviceCharge: 5,
            subtotal: 541.07,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 55,
            vatAmount: 5.36,
            zeroRatedSales: 0,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge:Reprint', () => {
        const transaction = {
            discount: 546.81,
            display: {
                discount: -95.53,
                serviceCharge: 9.55,
                subtotal: 191.07,
                tax: 12.62,
                total: 117.71,
            },
            fixedFee: 0,
            isBirDiscountAvailable: false,
            items: [
                {
                    adhocDiscount: 44.65,
                    discount: 491.07,
                    display: { subtotal: 535.71, tax: 10.71, total: 89.29 },
                    fullBillDiscount: 44.65,
                    notRoundedOriginalTax: 5.36,
                    promotions: [
                        {
                            discount: 446.42,
                            display: { discount: 446.42 },
                            inputValue: 83.3333333,
                            promotionId: '7dd77sdy8asd89a8989w',
                            quantity: 1,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    selectedOptions: [
                        { optionValue: 89.2857143, quantity: 1 },
                        { optionValue: 44.6428571, quantity: 4 },
                    ],
                    subTotal: 535.71,
                    tax: 5.36,
                    taxRate: 0.12,
                    total: 44.64,
                    unitPrice: 267.857143,
                },
                {
                    adhocDiscount: 9.36,
                    discount: 11.1,
                    display: { subtotal: 17.35, tax: 1.5, total: 12.49 },
                    fullBillDiscount: 6.24,
                    itemLevelDiscount: { equivalentValue: 3.12, inputValue: 20, type: 'percent' },
                    notRoundedOriginalTax: 0.75,
                    promotions: [
                        {
                            discount: 1.74,
                            display: { discount: 1.74 },
                            inputValue: 10,
                            promotionId: '7dd77sdy95sd89a8989w',
                            quantity: 1,
                            type: 'percentage',
                        },
                    ],
                    quantity: 1,
                    subTotal: 17.35,
                    tax: 0.75,
                    taxRate: 0.12,
                    total: 6.25,
                    unitPrice: 17.3482143,
                },
                {
                    adhocDiscount: 44.64,
                    discount: 44.64,
                    display: { subtotal: 89.29, tax: 10.71, total: 89.29 },
                    fullBillDiscount: 44.64,
                    notRoundedOriginalTax: 5.36,
                    quantity: 1,
                    subTotal: 89.29,
                    tax: 5.36,
                    taxRate: 0.12,
                    total: 44.65,
                    unitPrice: 89.2857142857,
                },
                {
                    display: { discount: 95.53 },
                    equivalentValue: 95.53,
                    inputValue: 50,
                    itemType: 'Discount',
                    taxRate: 0.12,
                    type: 'percent',
                },
                {
                    adhocDiscount: 0,
                    discount: 0,
                    display: { subtotal: 9.55, tax: 1.15, total: 9.55 },
                    itemType: 'ServiceCharge',
                    notRoundedOriginalTax: 1.15,
                    quantity: 1,
                    rate: 0.1,
                    subTotal: 9.55,
                    tax: 1.15,
                    taxRate: 0.12,
                    total: 9.55,
                    unitPrice: 9.554000000000006,
                },
            ],
            maximumDiscountInputValue: 191.07,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 9.55,
            serviceChargeTax: 1.15,
            subtotal: 642.35,
            takeawayCharges: 0,
            tax: 12.62,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 117.71,
            totalDeductedTax: 0,
            zeroRatedSales: 0,
        };
        const result = receipt(transaction, false, 'PH');
        expect(result.items[0].receipt).toEqual({
            discount: 491.07,
            a4Total: 44.64,
            discountWithoutPromo: 44.65,
            price: 535.71,
            qty: 1,
            total: 535.71,
        });
        expect(result.receipt).toEqual({
            adhocDiscount: 546.81,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountable: 642.35,
            lessVAT: 12.62,
            medalOfValorDiscount: 0,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 10.7,
            subtotal: 653.82,
            taxExemptedSales: 0,
            taxableSales: 0,
            receiptTotalExcTax: 642.35,
            total: 117.71,
            vatAmount: 11.47,
            zeroRatedSales: 0,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, Non-BIR, takeawayCharges', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 1766.3551401869158,
                    quantity: 1,
                    taxRate: 0.07,
                    isTakeaway: true,
                },
            ],
            takeawayCharge: 10,
        };

        const res = calculate(transaction, false);

        expect(res).toEqual({
            amusementTax: 0,
            calculation: {
                discounts: [],
                original: { subtotal: 1776.36, tax: 123.64, total: 1900 },
                taxes: [],
            },
            discount: 0,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 1776.36,
                tax: 123.65,
                total: 1900.01,
            },
            fixedFee: 0,
            isBirDiscountAvailable: false,
            items: [
                {
                    adhocDiscount: 0,
                    calculation: {
                        discounts: [],
                        fullPrice: 1766.3551401869158,
                        original: { subtotal: 1776.36, tax: 123.64, total: 1900 },
                        taxes: [],
                    },
                    discount: 0,
                    display: { subtotal: 1776.36, tax: 123.65, total: 1776.36 },
                    isTakeaway: true,
                    notRoundedOriginalTax: 123.65,
                    quantity: 1,
                    subTotal: 1776.36,
                    takeawayCharges: 10,
                    tax: 123.65,
                    taxRate: 0.07,
                    total: 1776.36,
                    unitPrice: 1766.3551401869158,
                },
            ],
            maximumDiscountInputValue: 1766.36,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 1776.36,
            takeawayCharge: 10,
            takeawayCharges: 10,
            tax: 123.65,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 1900.01,
            totalDeductedTax: 0,
            zeroRatedSales: 0,
        });

        const result = receipt(res, false, 'PH');
        expect(result.items[0].receipt).toEqual({
            total: 1766.36, // 1890.00
            a4Total: 1766.36,
            price: 1766.3551401869158, // 1890.00
            discount: 0,
            qty: 1,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            adhocDiscount: 0,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountable: 1766.36,
            lessVAT: 123.65,
            medalOfValorDiscount: 0,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 0,
            subtotal: 1890.01,
            receiptTotalExcTax: 1766.36,
            takeawayCharges: 10,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 1900.01,
            vatAmount: 123.65,
            zeroRatedSales: 0,
            amusementTax: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxInclusiveDisplay:item, Non-BIR, takeawayCharges', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 1766.3551401869158,
                    quantity: 1,
                    taxRate: 0.07,
                    isTakeaway: true,
                },
            ],
            takeawayCharge: 10,
        };

        const res = calculate(transaction, true);

        expect(res).toEqual({
            amusementTax: 0,
            calculation: {
                discounts: [],
                original: { subtotal: 1776.36, tax: 123.64, total: 1900 },
                taxes: [],
            },
            discount: 0,
            display: { discount: 0, serviceCharge: 0, subtotal: 1776.36, tax: 123.64, total: 1900 },
            fixedFee: 0,
            isBirDiscountAvailable: false,
            items: [
                {
                    adhocDiscount: 0,
                    calculation: {
                        discounts: [],
                        fullPrice: 1766.3551401869158,
                        original: { subtotal: 1776.36, tax: 123.64, total: 1900 },
                        taxes: [],
                    },
                    discount: 0,
                    display: { subtotal: 1900, tax: 123.64, total: 1900 },
                    isTakeaway: true,
                    notRoundedOriginalTax: 123.6400000000001,
                    quantity: 1,
                    subTotal: 1776.36,
                    takeawayCharges: 10,
                    tax: 123.64,
                    taxRate: 0.07,
                    total: 1776.36,
                    unitPrice: 1766.3551401869158,
                },
            ],
            maximumDiscountInputValue: 1890,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 1776.36,
            takeawayCharge: 10,
            takeawayCharges: 10,
            tax: 123.64,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 1900,
            totalDeductedTax: 0,
            zeroRatedSales: 0,
        });

        const result = receipt(res, true, 'PH');
        expect(result.items[0].receipt).toEqual({
            total: 1890.0, // 1890.00
            a4Total: 1890.0,
            price: 1890.0, // 1890.00
            discount: 0,
            qty: 1,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            adhocDiscount: 0,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountable: 1766.36,
            lessVAT: 123.64,
            receiptTotalExcTax: 1766.36,
            medalOfValorDiscount: 0,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 0,
            subtotal: 1890,
            takeawayCharges: 10,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 1900,
            vatAmount: 123.64,
            zeroRatedSales: 0,
            amusementTax: 0,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxInclusiveDisplay:item, Non-BIR, item manual discount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0.12,
                    taxCode: 'das7dasd7ahda78sd3',
                    itemLevelDiscount: {
                        inputValue: 50,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0.1,
                    taxCode: 'das7dasd7ahda78sd4',
                    isVatExempted: true,
                    isAmusementTax: true,
                    itemLevelDiscount: {
                        inputValue: 50,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 100,
                    quantity: 1,
                    taxRate: 0.07,
                    taxCode: 'das7dasd7ahda78sd5',
                    itemLevelDiscount: {
                        inputValue: 50,
                        type: 'amount',
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res).toEqual({
            amusementTax: 5.45,
            calculation: {
                discounts: [],
                original: {
                    subtotal: 300,
                    tax: 29,
                    total: 329,
                },
                taxes: [
                    {
                        isAmusementTax: false,
                        isVatExempted: false,
                        tax: 6.64,
                        taxCode: 'das7dasd7ahda78sd3',
                        taxRate: 0.12,
                    },
                    {
                        isAmusementTax: true,
                        isVatExempted: true,
                        tax: 5.45,
                        taxCode: 'das7dasd7ahda78sd4',
                        taxRate: 0.1,
                    },
                    {
                        isAmusementTax: false,
                        isVatExempted: false,
                        tax: 3.73,
                        taxCode: 'das7dasd7ahda78sd5',
                        taxRate: 0.07,
                    },
                ],
            },
            discount: 136.82,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 163.18,
                tax: 15.82,
                total: 179,
            },
            fixedFee: 0,
            isBirDiscountAvailable: false,
            items: [
                {
                    adhocDiscount: 44.64285714285714,
                    calculation: {
                        discounts: [
                            {
                                deductedTax: 5.36,
                                discount: 44.64,
                                type: 'ItemManualDiscount',
                            },
                        ],
                        fullPrice: 100,
                        original: {
                            subtotal: 100,
                            tax: 12,
                            total: 112,
                        },
                        taxes: [
                            {
                                isAmusementTax: false,
                                isVatExempted: false,
                                tax: 6.64,
                                taxCode: 'das7dasd7ahda78sd3',
                                taxRate: 0.12,
                            },
                        ],
                    },
                    taxCode: 'das7dasd7ahda78sd3',
                    discount: 44.64,
                    display: {
                        subtotal: 112,
                        tax: 6.64,
                        total: 62,
                    },
                    itemLevelDiscount: {
                        equivalentValue: 44.64,
                        inputValue: 50,
                        type: 'amount',
                    },
                    notRoundedOriginalTax: 6.640000000000001,
                    quantity: 1,
                    subTotal: 100,
                    tax: 6.64,
                    taxRate: 0.12,
                    total: 55.36,
                    unitPrice: 100,
                },
                {
                    adhocDiscount: 45.45454545454545,
                    calculation: {
                        discounts: [
                            {
                                deductedTax: 4.55,
                                discount: 45.45,
                                type: 'ItemManualDiscount',
                            },
                        ],
                        fullPrice: 100,
                        original: {
                            subtotal: 100,
                            tax: 10,
                            total: 110,
                        },
                        taxes: [
                            {
                                isAmusementTax: true,
                                isVatExempted: true,
                                tax: 5.45,
                                taxCode: 'das7dasd7ahda78sd4',
                                taxRate: 0.1,
                            },
                        ],
                    },
                    taxCode: 'das7dasd7ahda78sd4',
                    discount: 45.45,
                    display: {
                        subtotal: 110,
                        tax: 5.45,
                        total: 60,
                    },
                    isAmusementTax: true,
                    isVatExempted: true,
                    itemLevelDiscount: {
                        equivalentValue: 45.45,
                        inputValue: 50,
                        type: 'amount',
                    },
                    notRoundedOriginalTax: 5.450000000000003,
                    quantity: 1,
                    subTotal: 100,
                    tax: 5.45,
                    taxRate: 0.1,
                    total: 54.55,
                    unitPrice: 100,
                },
                {
                    adhocDiscount: 46.72897196261682,
                    calculation: {
                        discounts: [
                            {
                                deductedTax: 3.27,
                                discount: 46.73,
                                type: 'ItemManualDiscount',
                            },
                        ],
                        fullPrice: 100,
                        original: {
                            subtotal: 100,
                            tax: 7,
                            total: 107,
                        },
                        taxes: [
                            {
                                isAmusementTax: false,
                                isVatExempted: false,
                                tax: 3.73,
                                taxCode: 'das7dasd7ahda78sd5',
                                taxRate: 0.07,
                            },
                        ],
                    },
                    taxCode: 'das7dasd7ahda78sd5',
                    discount: 46.73,
                    display: {
                        subtotal: 107,
                        tax: 3.73,
                        total: 57,
                    },
                    itemLevelDiscount: {
                        equivalentValue: 46.73,
                        inputValue: 50,
                        type: 'amount',
                    },
                    notRoundedOriginalTax: 3.729999999999997,
                    quantity: 1,
                    subTotal: 100,
                    tax: 3.73,
                    taxRate: 0.07,
                    total: 53.27,
                    unitPrice: 100,
                },
            ],
            maximumDiscountInputValue: 179,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 300,
            takeawayCharges: 0,
            tax: 15.82,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 179,
            totalDeductedTax: 0,
            zeroRatedSales: 0,
        });

        const result = receipt(res, true, 'PH');
        expect(result.items[0].receipt).toEqual({
            a4Total: 62,
            discount: 50,
            discountWithoutPromo: 50,
            price: 112,
            qty: 1,
            total: 112,
        });
        expect(result.items[1].receipt).toEqual({
            a4Total: 60,
            discount: 50,
            discountWithoutPromo: 50,
            price: 110,
            qty: 1,
            total: 110,
        });
        expect(result.receipt).toEqual({
            adhocDiscount: 136.82,
            amusementTax: 5.45,
            athleteAndCoachDiscount: 0,
            discountable: 300,
            lessVAT: 15.82,
            medalOfValorDiscount: 0,
            pwdDiscount: 0,
            receiptTotalExcTax: 300,
            seniorDiscount: 0,
            serviceCharge: 0,
            soloParentDiscount: 0,
            subtotal: 315.82,
            takeawayCharges: 0,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 179,
            vatAmount: 10.37,
            zeroRatedSales: 0,
        });
    });

    test('calculator:receipt:TH Func:receipt:taxInclusiveDisplay:item, Non-BIR, item manual discount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 4392.523364485981,
                    quantity: 1,
                    taxRate: 0.07,
                    itemLevelDiscount: {
                        inputValue: 100,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 439.252336448598,
                    quantity: 1,
                    taxRate: 0.07,
                    itemLevelDiscount: {
                        inputValue: 100,
                        type: 'amount',
                    },
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res).toEqual({
            amusementTax: 0,
            calculation: {
                discounts: [],
                original: { subtotal: 4831.77, tax: 338.23, total: 5170 },
                taxes: [],
            },
            discount: 186.91,
            display: { discount: 0, serviceCharge: 0, subtotal: 4644.86, tax: 325.14, total: 4970 },
            fixedFee: 0,
            isBirDiscountAvailable: false,
            items: [
                {
                    adhocDiscount: 93.45794392523364,
                    calculation: {
                        discounts: [
                            { deductedTax: 6.54, discount: 93.46, type: 'ItemManualDiscount' },
                        ],
                        fullPrice: 4392.523364485981,
                        original: { subtotal: 4392.52, tax: 307.48, total: 4700 },
                        taxes: [],
                    },
                    discount: 93.45,
                    display: { subtotal: 4700, tax: 300.93, total: 4600 },
                    itemLevelDiscount: { equivalentValue: 2.13, inputValue: 100, type: 'amount' },
                    notRoundedOriginalTax: 300.9300000000003,
                    quantity: 1,
                    subTotal: 4392.52,
                    tax: 300.93,
                    taxRate: 0.07,
                    total: 4299.07,
                    unitPrice: 4392.523364485981,
                },
                {
                    adhocDiscount: 93.45794392523364,
                    calculation: {
                        discounts: [
                            { deductedTax: 6.54, discount: 93.46, type: 'ItemManualDiscount' },
                        ],
                        fullPrice: 439.252336448598,
                        original: { subtotal: 439.25, tax: 30.75, total: 470 },
                        taxes: [],
                    },
                    discount: 93.46,
                    display: { subtotal: 470, tax: 24.21, total: 370 },
                    itemLevelDiscount: { equivalentValue: 21.28, inputValue: 100, type: 'amount' },
                    notRoundedOriginalTax: 24.20999999999998,
                    quantity: 1,
                    subTotal: 439.25,
                    tax: 24.21,
                    taxRate: 0.07,
                    total: 345.79,
                    unitPrice: 439.252336448598,
                },
            ],
            maximumDiscountInputValue: 4970,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 4831.77,
            takeawayCharges: 0,
            tax: 325.14,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 4970,
            totalDeductedTax: 0,
            zeroRatedSales: 0,
        });

        const result = receipt(res, true, 'TH');
        expect(result.items[0].receipt).toEqual({
            price: 4700,
            qty: 1,
            total: 4700,
            a4Total: 4600,
            discount: 100,
            discountWithoutPromo: 100,
        }); // 4700.00
        expect(result.items[1].receipt).toEqual({
            price: 470,
            qty: 1,
            total: 470,
            a4Total: 370,
            discount: 100,
            discountWithoutPromo: 100,
        });
        expect(result.receipt).toEqual({
            discount: 186.91,
            receiptTotalExcTax: 4831.77,
            serviceCharge: 0,
            takeawayCharges: 0,
            tax: 325.14,
            total: 4970,
        });
    });

    test('calculator:receipt:TH Func:receipt:taxExclusiveDisplay:item, Non-BIR, item manual discount', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 4392.523364485981,
                    quantity: 1,
                    taxRate: 0.07,
                    itemLevelDiscount: {
                        inputValue: 100,
                        type: 'amount',
                    },
                },
                {
                    unitPrice: 439.252336448598,
                    quantity: 1,
                    taxRate: 0.07,
                    itemLevelDiscount: {
                        inputValue: 100,
                        type: 'amount',
                    },
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res).toEqual({
            amusementTax: 0,
            calculation: {
                discounts: [],
                original: { subtotal: 4831.77, tax: 338.23, total: 5170 },
                taxes: [],
            },
            discount: 200,
            display: { discount: 0, serviceCharge: 0, subtotal: 4631.77, tax: 324.23, total: 4956 },
            fixedFee: 0,
            isBirDiscountAvailable: false,
            items: [
                {
                    adhocDiscount: 100,
                    calculation: {
                        discounts: [{ deductedTax: 7, discount: 100, type: 'ItemManualDiscount' }],
                        fullPrice: 4392.523364485981,
                        original: { subtotal: 4392.52, tax: 307.48, total: 4700 },
                        taxes: [],
                    },
                    discount: 100,
                    display: { subtotal: 4392.52, tax: 300.48, total: 4292.52 },
                    itemLevelDiscount: { equivalentValue: 2.28, inputValue: 100, type: 'amount' },
                    notRoundedOriginalTax: 300.48,
                    quantity: 1,
                    subTotal: 4392.52,
                    tax: 300.48,
                    taxRate: 0.07,
                    total: 4292.52,
                    unitPrice: 4392.523364485981,
                },
                {
                    adhocDiscount: 100,
                    calculation: {
                        discounts: [{ deductedTax: 7, discount: 100, type: 'ItemManualDiscount' }],
                        fullPrice: 439.252336448598,
                        original: { subtotal: 439.25, tax: 30.75, total: 470 },
                        taxes: [],
                    },
                    discount: 100,
                    display: { subtotal: 439.25, tax: 23.75, total: 339.25 },
                    itemLevelDiscount: { equivalentValue: 22.77, inputValue: 100, type: 'amount' },
                    notRoundedOriginalTax: 23.75,
                    quantity: 1,
                    subTotal: 439.25,
                    tax: 23.75,
                    taxRate: 0.07,
                    total: 339.25,
                    unitPrice: 439.252336448598,
                },
            ],
            maximumDiscountInputValue: 4631.77,
            pwdDiscount: 0,
            seniorDiscount: 0,
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 4831.77,
            takeawayCharges: 0,
            tax: 324.23,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 4956,
            totalDeductedTax: 0,
            zeroRatedSales: 0,
        });

        const result = receipt(res, false, 'TH');
        expect(result.items[0].receipt).toEqual({
            price: 4392.523364485981,
            qty: 1,
            total: 4392.52,
            a4Total: 4292.52,
            discount: 100,
            discountWithoutPromo: 100,
        }); // 4700.00
        expect(result.items[1].receipt).toEqual({
            price: 439.252336448598,
            qty: 1,
            total: 439.25,
            a4Total: 339.25,
            discount: 100,
            discountWithoutPromo: 100,
        });
        expect(result.receipt).toEqual({
            discount: 200,
            receiptTotalExcTax: 4831.77,
            serviceCharge: 0,
            takeawayCharges: 0,
            tax: 324.23,
            total: 4956,
        });
    });

    test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, ServiceCharge, BirFnb, lose some field', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 8.92857142,
                    quantity: 1,
                    taxRate: 0.12,
                    adhocDiscount: 0,
                    total: 8.03,
                    tax: 0.54,
                    discount: 0.89,
                    subTotal: 8.93,
                    seniorDiscount: 0,
                    pwdDiscount: 0.89,
                    taxableAmount: 4.46,
                    taxExemptAmount: 4.46,
                },
            ],
            birInfo: {
                type: 'f&b',
                discountRate: 0.2,
                seniorsCount: 0,
                pwdCount: 0,
                headCount: 1,
            },
            addonBirCompliance: {
                discountType: 'SC/PWD',
            },
            subtotal: 8.93,
            discount: 0.98,
            tax: 0.59,
            total: 9.43,
            zeroRatedSales: 0,
            display: {
                subtotal: 8.03,
                discount: 0,
                serviceCharge: 0.81,
                tax: 0.59,
                total: 9.43,
            },
        };
        const result = receipt(transaction, false, 'PH');
        expect(result.items[0].receipt).toEqual({
            total: 8.93,
            a4Total: 8.04,
            price: 8.93,
            qty: 1,
            discount: 0.89,
            discountWithoutPromo: 0.89,
        });
        expect(result.receipt).toEqual({
            adhocDiscount: 0.98,
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountable: 8.93,
            lessVAT: 0.59,
            medalOfValorDiscount: 0,
            pwdDiscount: 0,
            receiptTotalExcTax: 8.93,
            seniorDiscount: 0,
            serviceCharge: 0,
            subtotal: 10.41,
            taxExemptedSales: 0,
            taxableSales: 0,
            total: 9.43,
            vatAmount: 0.59,
            zeroRatedSales: 0,
            takeawayCharges: 0,
        });
    });

    test('calculator:receipt:CN Func:receipt:not support countryCode', () => {
        const transaction = {
            discount: 0,
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                },
            ],
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        };

        expect(() => {
            receipt(transaction, false, 'CN');
        }).toThrowError(Error('countryCode not support, only support MY, TH, PH.'));
    });
});

test('calculator:receipt:wrong countryCode', () => {
    const transaction = {
        discount: 0,
        display: {
            discount: 0,
            serviceCharge: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        },
        items: [
            {
                adhocDiscount: 0,
                discount: 0,
                display: {
                    subtotal: 535.71,
                    tax: 64.29,
                    total: 535.71,
                },
                quantity: 1,
                subTotal: 535.71,
                tax: 64.29,
                taxExclusiveSubtotal: 535.71,
                taxInclusiveSubtotal: 600,
                taxRate: 0.12,
                total: 535.71,
                unitPrice: 535.714286,
            },
        ],
        serviceCharge: 0,
        serviceChargeTax: 0,
        subtotal: 535.71,
        tax: 64.29,
        total: 600,
    };
    expect(() => receipt(transaction, false, 'US')).toThrowError();
});

test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, ServiceCharge, BirFnb, some fields value is 0', () => {
    const transaction = {
        items: [
            {
                unitPrice: 10,
                quantity: 1,
                taxRate: 0,
                adhocDiscount: 0,
                total: 10,
                tax: 0,
                discount: 0,
                subTotal: 10,
                seniorDiscount: 0,
                pwdDiscount: 0,
                taxableAmount: 0,
                taxExemptAmount: 0,
                totalDeductedTax: 0,
                zeroRatedSales: 10,
            },
            {
                itemType: 'ServiceCharge',
                rate: 0,
                taxRate: 0,
                unitPrice: 0,
                quantity: 1,
                adhocDiscount: 0,
                total: 0,
                tax: 0,
                discount: 0,
                subTotal: 0,
                seniorDiscount: 0,
                pwdDiscount: 0,
                taxableAmount: 0,
                taxExemptAmount: 0,
                totalDeductedTax: 0,
            },
        ],
        birInfo: {
            type: 'f&b',
            discountRate: 0.2,
            seniorsCount: 0,
            pwdCount: 0,
            headCount: 1,
        },
        subtotal: 10,
        discount: 0,
        tax: 0,
        total: 10,
        serviceCharge: 0,
        serviceChargeTax: 0,
        seniorDiscount: 0,
        pwdDiscount: 0,
        taxableSales: 0,
        taxExemptedSales: 0,
        zeroRatedSales: 10,
        totalDeductedTax: 0,
        display: {
            subtotal: 10,
            discount: 0,
            serviceCharge: 0,
            tax: 0,
            total: 10,
        },
    };
    const result = receipt(transaction, false, 'PH');
    expect(result.items[0].receipt).toEqual({
        total: 10,
        a4Total: 10,
        price: 10,
        qty: 1,
        discount: 0,
        discountWithoutPromo: 0,
    });
    expect(result.receipt).toEqual({
        subtotal: 10.0,
        lessVAT: 0,
        discountable: 10,
        adhocDiscount: 0,
        seniorDiscount: 0,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        medalOfValorDiscount: 0,
        pwdDiscount: 0,
        vatAmount: 0,
        receiptTotalExcTax: 10,
        serviceCharge: 0,
        total: 10,
        taxableSales: 0,
        taxExemptedSales: 0,
        zeroRatedSales: 10,
        takeawayCharges: 0,
    });
});

test('Func:receipt:taxInclusiveDisplay:item, selectedOptions, itemDiscount, takeAmountOffPromotion, loyaltyDiscount, ServiceCharge', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
                itemLevelDiscount: {
                    inputValue: 500,
                    type: 'amount',
                },
                promotionAppliedQuantityMap: {
                    test1: 1,
                },
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
                promotionAppliedQuantityMap: {
                    test1: 1,
                },
            },
            {
                itemType: 'ServiceCharge',
                rate: 0.1,
                taxRate: 0,
            },
        ],
        promotions: [
            {
                promotionId: 'test1',
                inputValue: 50,
                type: 'absolute',
                taxRate: 0.12,
            },
        ],
        loyaltyDiscounts: [
            {
                inputValue: 10,
                type: 'cashback',
                taxRate: 0.12,
            },
        ],
    };
    const res = calculate(transaction, true);
    expect(res).toEqual({
        amusementTax: 0,
        calculation: {
            discounts: [
                {
                    deductedTax: 5.36,
                    discount: 44.64,
                    promotionId: 'test1',
                    subType: 'absolute',
                    type: 'Promotion',
                },
                { deductedTax: 1.07, discount: 8.93, type: 'Loyalty' },
            ],
            original: { subtotal: 625, tax: 75, total: 762.5 },
            taxes: [],
        },
        discount: 500,
        display: { discount: 0, serviceCharge: 12.5, subtotal: 133.93, tax: 15, total: 152.5 },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 488.0982142857143,
                calculation: {
                    discounts: [
                        { deductedTax: 53.57, discount: 446.43, type: 'ItemManualDiscount' },
                        {
                            deductedTax: 4.59,
                            discount: 38.27,
                            promotionId: 'test1',
                            subType: 'absolute',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.41, discount: 3.4, type: 'Loyalty' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                discount: 488.09,
                display: { subtotal: 600, tax: 10.71, total: 100 },
                itemLevelDiscount: { equivalentValue: 83.33, inputValue: 500, type: 'amount' },
                loyaltyDiscount: 3.81,
                loyaltyDiscountInfo: { discountTax: 0.41, taxExclusiveDiscount: 3.401785714285714 },
                notRoundedOriginalTax: 5.7100000000000435,
                promotionAppliedQuantityMap: { test1: 1 },
                promotionDiscount: 42.86,
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                isServiceChargeNotApplicable: false,
                subTotal: 535.71,
                tax: 5.71,
                taxRate: 0.12,
                total: 47.62,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 11.901785714285714,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.77,
                            discount: 6.38,
                            promotionId: 'test1',
                            subType: 'absolute',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.66, discount: 5.53, type: 'Loyalty' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 11.91,
                display: { subtotal: 100, tax: 10.71, total: 100 },
                loyaltyDiscount: 6.19,
                loyaltyDiscountInfo: { discountTax: 0.66, taxExclusiveDiscount: 5.526785714285714 },
                notRoundedOriginalTax: 9.290000000000006,
                promotionAppliedQuantityMap: { test1: 1 },
                promotionDiscount: 7.14,
                quantity: 1,
                subTotal: 89.29,
                tax: 9.29,
                taxRate: 0.12,
                total: 77.38,
                unitPrice: 89.2857142857,
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 62.5,
                    original: { subtotal: 62.5, tax: 0, total: 62.5 },
                    taxes: [],
                },
                discount: 0,
                display: { subtotal: 12.5, tax: 0, total: 12.5 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 0,
                quantity: 1,
                rate: 0.1,
                subTotal: 12.5,
                tax: 0,
                taxRate: 0,
                total: 12.5,
                unitPrice: 12.500000000000007,
            },
        ],
        loyaltyDiscounts: [
            {
                display: { discount: 8.93, discountedTax: 1.07 },
                inputValue: 10,
                spentValue: 10,
                taxRate: 0.12,
                type: 'cashback',
            },
        ],
        maximumDiscountInputValue: 150,
        promotions: [
            {
                discount: 44.64,
                display: { discount: 50 },
                inputValue: 50,
                promotionId: 'test1',
                taxRate: 0.12,
                type: 'absolute',
            },
        ],
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 12.5,
        serviceChargeTax: 0,
        subtotal: 625,
        takeawayCharges: 0,
        tax: 15,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 152.5,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });
    const result = receipt(res, true, 'PH');
    expect(result.items[0].receipt).toEqual({
        discount: 546.67,
        discountWithoutPromo: 546.67,
        price: 600,
        qty: 1,
        total: 600,
        a4Total: 53.33,
    });
    expect(result.items[1].receipt).toEqual({
        discount: 13.33,
        discountWithoutPromo: 13.33,
        price: 100,
        qty: 1,
        total: 100,
        a4Total: 86.67,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 500,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 625,
        lessVAT: 15,
        medalOfValorDiscount: 0,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 12.5,
        receiptTotalExcTax: 625,
        amusementTax: 0,
        subtotal: 640,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 152.5,
        vatAmount: 15,
        zeroRatedSales: 0,
        takeawayCharges: 0,
    });
});

test('Func:calculate:taxInclusiveDisplay:item, selectedOptions,  BirFnb: idempotence, BIR-PWD', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
        ],
        birInfo: {
            type: 'f&b',
            discountType: 'SC/PWD',
            discountRate: 0.2,
            seniorsCount: 0,
            pwdCount: 1,
            headCount: 2,
        },
    };
    const res = calculate(transaction, true);
    expect(res).toEqual({
        amusementTax: 0,
        addonBirCompliance: {
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountType: 'SC/PWD',
            medalOfValorDiscount: 0,
        },
        birInfo: {
            discountRate: 0.2,
            discountType: 'SC/PWD',
            headCount: 2,
            pwdCount: 1,
            seniorsCount: 0,
            type: 'f&b',
        },
        calculation: {
            discounts: [{ deductedTax: 37.5, discount: 62.5, subType: 'PWD', type: 'BIR' }],
            original: { subtotal: 625, tax: 75, total: 700 },
            taxes: [],
        },
        discount: 62.5,
        display: { discount: 0, serviceCharge: 0, subtotal: 562.5, tax: 37.5, total: 600 },
        fixedFee: 0,
        isBirDiscountAvailable: true,
        items: [
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [
                        { deductedTax: 32.14, discount: 53.57, subType: 'PWD', type: 'BIR' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                discount: 53.57,
                display: { subtotal: 600, tax: 32.14, total: 514.29 },
                notRoundedOriginalTax: 32.139999999999986,
                pwdDiscount: 53.57,
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                seniorDiscount: 0,
                subTotal: 535.71,
                tax: 32.14,
                taxExemptAmount: 214.29,
                taxRate: 0.12,
                taxableAmount: 267.86,
                total: 482.15,
                totalDeductedTax: 32.14,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [{ deductedTax: 5.36, discount: 8.93, subType: 'PWD', type: 'BIR' }],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                discount: 8.93,
                display: { subtotal: 100, tax: 5.36, total: 85.71 },
                notRoundedOriginalTax: 5.359999999999999,
                pwdDiscount: 8.93,
                quantity: 1,
                seniorDiscount: 0,
                subTotal: 89.29,
                tax: 5.36,
                taxExemptAmount: 35.71,
                taxRate: 0.12,
                taxableAmount: 44.64,
                total: 80.35,
                totalDeductedTax: 5.36,
                unitPrice: 89.2857142857,
            },
        ],
        maximumDiscountInputValue: 700,
        pwdDiscount: 62.5,
        seniorDiscount: 0,
        serviceCharge: 0,
        serviceChargeTax: 0,
        subtotal: 625,
        takeawayCharges: 0,
        tax: 37.5,
        taxExemptedSales: 250,
        taxableSales: 312.5,
        total: 600,
        totalDeductedTax: 37.5,
        zeroRatedSales: 0,
    });

    const result = receipt(res, true, 'PH');
    expect(result.items[0].receipt).toEqual({
        discount: 85.71,
        discountWithoutPromo: 85.71,
        price: 600,
        qty: 1,
        total: 600,
        a4Total: 514.29,
    });
    expect(result.items[1].receipt).toEqual({
        discount: 14.29,
        discountWithoutPromo: 14.29,
        price: 100,
        qty: 1,
        total: 100,
        a4Total: 85.71,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 0,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 625,
        amusementTax: 0,
        lessVAT: 75,
        medalOfValorDiscount: 0,
        pwdDiscount: 62.5,
        seniorDiscount: 0,
        serviceCharge: 0,
        subtotal: 700,
        receiptTotalExcTax: 625,
        taxExemptedSales: 250,
        taxableSales: 312.5,
        total: 600,
        vatAmount: 37.5,
        zeroRatedSales: 0,
        takeawayCharges: 0,
    });
});

test('Func:calculate:taxInclusiveDisplay:item, selectedOptions,  BirFnb: idempotence, BIR-SC', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
        ],
        birInfo: {
            type: 'f&b',
            discountType: 'SC/PWD',
            discountRate: 0.2,
            seniorsCount: 1,
            pwdCount: 0,
            headCount: 2,
        },
    };
    const res = calculate(transaction, true);
    expect(res).toEqual({
        amusementTax: 0,
        addonBirCompliance: {
            athleteAndCoachDiscount: 0,
            soloParentDiscount: 0,
            discountType: 'SC/PWD',
            medalOfValorDiscount: 0,
        },
        birInfo: {
            discountRate: 0.2,
            discountType: 'SC/PWD',
            headCount: 2,
            pwdCount: 0,
            seniorsCount: 1,
            type: 'f&b',
        },
        calculation: {
            discounts: [{ deductedTax: 37.5, discount: 62.5, subType: 'SC', type: 'BIR' }],
            original: { subtotal: 625, tax: 75, total: 700 },
            taxes: [],
        },
        discount: 62.5,
        display: { discount: 0, serviceCharge: 0, subtotal: 562.5, tax: 37.5, total: 600 },
        fixedFee: 0,
        isBirDiscountAvailable: true,
        items: [
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [
                        { deductedTax: 32.14, discount: 53.57, subType: 'SC', type: 'BIR' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                discount: 53.57,
                display: { subtotal: 600, tax: 32.14, total: 514.29 },
                notRoundedOriginalTax: 32.139999999999986,
                pwdDiscount: 0,
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                seniorDiscount: 53.57,
                subTotal: 535.71,
                tax: 32.14,
                taxExemptAmount: 214.29,
                taxRate: 0.12,
                taxableAmount: 267.86,
                total: 482.15,
                totalDeductedTax: 32.14,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [{ deductedTax: 5.36, discount: 8.93, subType: 'SC', type: 'BIR' }],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                discount: 8.93,
                display: { subtotal: 100, tax: 5.36, total: 85.71 },
                notRoundedOriginalTax: 5.359999999999999,
                pwdDiscount: 0,
                quantity: 1,
                seniorDiscount: 8.93,
                subTotal: 89.29,
                tax: 5.36,
                taxExemptAmount: 35.71,
                taxRate: 0.12,
                taxableAmount: 44.64,
                total: 80.35,
                totalDeductedTax: 5.36,
                unitPrice: 89.2857142857,
            },
        ],
        maximumDiscountInputValue: 700,
        pwdDiscount: 0,
        seniorDiscount: 62.5,
        serviceCharge: 0,
        serviceChargeTax: 0,
        subtotal: 625,
        takeawayCharges: 0,
        tax: 37.5,
        taxExemptedSales: 250,
        taxableSales: 312.5,
        total: 600,
        totalDeductedTax: 37.5,
        zeroRatedSales: 0,
    });

    const result = receipt(res, true, 'PH');
    expect(result.items[0].receipt).toEqual({
        discount: 85.71,
        discountWithoutPromo: 85.71,
        price: 600,
        qty: 1,
        total: 600,
        a4Total: 514.29,
    });
    expect(result.items[1].receipt).toEqual({
        discount: 14.29,
        discountWithoutPromo: 14.29,
        price: 100,
        qty: 1,
        total: 100,
        a4Total: 85.71,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 0,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 625,
        amusementTax: 0,
        lessVAT: 75,
        medalOfValorDiscount: 0,
        pwdDiscount: 0,
        seniorDiscount: 62.5,
        serviceCharge: 0,
        subtotal: 700,
        receiptTotalExcTax: 625,
        taxExemptedSales: 250,
        taxableSales: 312.5,
        total: 600,
        vatAmount: 37.5,
        zeroRatedSales: 0,
        takeawayCharges: 0,
    });
});

test('Func:calculate:taxInclusiveDisplay:item, selectedOptions,  BirFnb: idempotence, BIR-SC/PWD', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
        ],
        birInfo: {
            type: 'f&b',
            discountType: 'SC/PWD',
            discountRate: 0.2,
            seniorsCount: 1,
            pwdCount: 1,
            headCount: 4,
        },
    };
    const res = calculate(transaction, true);
    expect(res).toEqual({
        addonBirCompliance: {
            athleteAndCoachDiscount: 0,
            discountType: 'SC/PWD',
            medalOfValorDiscount: 0,
            soloParentDiscount: 0,
        },
        amusementTax: 0,
        birInfo: {
            discountRate: 0.2,
            discountType: 'SC/PWD',
            headCount: 4,
            pwdCount: 1,
            seniorsCount: 1,
            type: 'f&b',
        },
        calculation: {
            discounts: [
                {
                    deductedTax: 18.75,
                    discount: 31.25,
                    subType: 'SC',
                    type: 'BIR',
                },
                {
                    deductedTax: 18.75,
                    discount: 31.25,
                    subType: 'PWD',
                    type: 'BIR',
                },
            ],
            original: {
                subtotal: 625,
                tax: 75,
                total: 700,
            },
            taxes: [],
        },
        discount: 62.5,
        display: {
            discount: 0,
            serviceCharge: 0,
            subtotal: 562.5,
            tax: 37.5,
            total: 600,
        },
        fixedFee: 0,
        isBirDiscountAvailable: true,
        items: [
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 16.07,
                            discount: 26.79,
                            subType: 'SC',
                            type: 'BIR',
                        },
                        {
                            deductedTax: 16.07,
                            discount: 26.78,
                            subType: 'PWD',
                            type: 'BIR',
                        },
                    ],
                    fullPrice: 535.7142857,
                    original: {
                        subtotal: 535.71,
                        tax: 64.29,
                        total: 600,
                    },
                    taxes: [],
                },
                discount: 53.57,
                display: {
                    subtotal: 600,
                    tax: 32.14,
                    total: 514.29,
                },
                notRoundedOriginalTax: 32.139999999999986,
                pwdDiscount: 26.78,
                quantity: 1,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                seniorDiscount: 26.79,
                subTotal: 535.71,
                tax: 32.14,
                taxExemptAmount: 214.29,
                taxRate: 0.12,
                taxableAmount: 267.86,
                total: 482.15,
                totalDeductedTax: 32.14,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 2.68,
                            discount: 4.46,
                            subType: 'SC',
                            type: 'BIR',
                        },
                        {
                            deductedTax: 2.68,
                            discount: 4.47,
                            subType: 'PWD',
                            type: 'BIR',
                        },
                    ],
                    fullPrice: 89.2857142857,
                    original: {
                        subtotal: 89.29,
                        tax: 10.71,
                        total: 100,
                    },
                    taxes: [],
                },
                discount: 8.93,
                display: {
                    subtotal: 100,
                    tax: 5.36,
                    total: 85.71,
                },
                notRoundedOriginalTax: 5.359999999999999,
                pwdDiscount: 4.47,
                quantity: 1,
                seniorDiscount: 4.46,
                subTotal: 89.29,
                tax: 5.36,
                taxExemptAmount: 35.71,
                taxRate: 0.12,
                taxableAmount: 44.64,
                total: 80.35,
                totalDeductedTax: 5.36,
                unitPrice: 89.2857142857,
            },
        ],
        maximumDiscountInputValue: 700,
        pwdDiscount: 31.25,
        seniorDiscount: 31.25,
        serviceCharge: 0,
        serviceChargeTax: 0,
        subtotal: 625,
        takeawayCharges: 0,
        tax: 37.5,
        taxExemptedSales: 250,
        taxableSales: 312.5,
        total: 600,
        totalDeductedTax: 37.5,
        zeroRatedSales: 0,
    });

    const result = receipt(res, true, 'PH');
    expect(result.items[0].receipt).toEqual({
        a4Total: 514.29,
        discount: 85.71,
        discountWithoutPromo: 85.71,
        price: 600,
        qty: 1,
        total: 600,
    });
    expect(result.items[1].receipt).toEqual({
        a4Total: 85.71,
        discount: 14.29,
        discountWithoutPromo: 14.29,
        price: 100,
        qty: 1,
        total: 100,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 0,
        amusementTax: 0,
        athleteAndCoachDiscount: 0,
        discountable: 625,
        lessVAT: 75,
        medalOfValorDiscount: 0,
        pwdDiscount: 31.25,
        receiptTotalExcTax: 625,
        seniorDiscount: 31.25,
        serviceCharge: 0,
        soloParentDiscount: 0,
        subtotal: 700,
        takeawayCharges: 0,
        taxExemptedSales: 250,
        taxableSales: 312.5,
        total: 600,
        vatAmount: 37.5,
        zeroRatedSales: 0,
    });
});

test('Func:receipt: MY, taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
                promotions: [
                    {
                        inputValue: 83.3333333,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy8asd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 17.3482143,
                quantity: 1,
                taxRate: 0.12,
                itemLevelDiscount: {
                    inputValue: 20,
                    type: 'percent',
                },
                promotions: [
                    {
                        inputValue: 10,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy95sd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
            {
                itemType: 'Discount',
                inputValue: 50,
                type: 'percent',
                taxRate: 0.12,
            },
            {
                itemType: 'ServiceCharge',
                rate: 0.1,
                taxRate: 0.12,
            },
        ],
    };

    const res = calculate(transaction, false);
    expect(res).toEqual({
        amusementTax: 0,
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.53, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
            taxes: [],
        },
        discount: 546.81,
        display: {
            discount: -95.53,
            serviceCharge: 9.55,
            subtotal: 191.07,
            tax: 12.62,
            total: 117.71,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.65,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.42,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.65, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 535.71, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.65,
                notRoundedOriginalTax: 5.36,
                promotions: [
                    {
                        discount: 446.42,
                        display: { discount: 446.42 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.36,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.21,
                            discount: 1.74,
                            promotionId: '7dd77sdy95sd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.37, discount: 3.12, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.24, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 11.1,
                display: { subtotal: 17.35, tax: 1.5, total: 12.49 },
                fullBillDiscount: 6.24,
                itemLevelDiscount: { equivalentValue: 3.12, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.74,
                        display: { discount: 1.74 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.25,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 44.64,
                display: { subtotal: 89.29, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.64,
                notRoundedOriginalTax: 5.36,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.65,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.53 },
                equivalentValue: 95.53,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                    taxes: [],
                },
                discount: 0,
                display: { subtotal: 9.55, tax: 1.15, total: 9.55 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.15,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.554000000000006,
            },
        ],
        maximumDiscountInputValue: 191.07,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.71,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });

    const result = receipt(res, false, 'MY');
    expect(result.items[0].receipt).toEqual({
        discount: 491.07,
        discountWithoutPromo: 44.65,
        price: 535.7142857,
        qty: 1,
        total: 535.71,
        a4Total: 44.64,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 446.42,
            display: { discount: 446.42 },
            promotionId: '7dd77sdy8asd89a8989w',
            inputValue: 83.3333333,
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 11.1,
        discountWithoutPromo: 9.36,
        price: 17.3482143,
        qty: 1,
        total: 17.35,
        a4Total: 6.25,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 44.64,
        discountWithoutPromo: 44.64,
        price: 89.2857142857,
        qty: 1,
        total: 89.29,
        a4Total: 44.65,
    });
    expect(result.receipt).toEqual({
        discount: 546.81,
        receiptTotalExcTax: 642.35,
        serviceCharge: 9.55,
        takeawayCharges: 0,
        tax: 12.62,
        total: 117.71,
    });
});

test('Func:receipt: MY, taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge, hasDiscountColumn', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
                promotions: [
                    {
                        inputValue: 83.3333333,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy8asd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 17.3482143,
                quantity: 1,
                taxRate: 0.12,
                itemLevelDiscount: {
                    inputValue: 20,
                    type: 'percent',
                },
                promotions: [
                    {
                        inputValue: 10,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy95sd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
            {
                itemType: 'Discount',
                inputValue: 50,
                type: 'percent',
                taxRate: 0.12,
            },
            {
                itemType: 'ServiceCharge',
                rate: 0.1,
                taxRate: 0.12,
            },
        ],
    };

    const res = calculate(transaction, false);
    expect(res).toEqual({
        amusementTax: 0,
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.53, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
            taxes: [],
        },
        discount: 546.81,
        display: {
            discount: -95.53,
            serviceCharge: 9.55,
            subtotal: 191.07,
            tax: 12.62,
            total: 117.71,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.65,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.42,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.65, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 535.71, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.65,
                notRoundedOriginalTax: 5.36,
                promotions: [
                    {
                        discount: 446.42,
                        display: { discount: 446.42 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.36,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.21,
                            discount: 1.74,
                            promotionId: '7dd77sdy95sd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.37, discount: 3.12, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.24, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 11.1,
                display: { subtotal: 17.35, tax: 1.5, total: 12.49 },
                fullBillDiscount: 6.24,
                itemLevelDiscount: { equivalentValue: 3.12, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.74,
                        display: { discount: 1.74 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.25,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 44.64,
                display: { subtotal: 89.29, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.64,
                notRoundedOriginalTax: 5.36,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.65,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.53 },
                equivalentValue: 95.53,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                    taxes: [],
                },
                discount: 0,
                display: { subtotal: 9.55, tax: 1.15, total: 9.55 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.15,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.554000000000006,
            },
        ],
        maximumDiscountInputValue: 191.07,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.71,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });

    const result = receipt(res, false, 'MY', true);
    expect(result.items[0].receipt).toEqual({
        discount: 491.07,
        discountWithoutPromo: 44.65,
        price: 535.7142857,
        qty: 1,
        total: 44.64,
        a4Total: 44.64,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 446.42,
            display: { discount: 446.42 },
            promotionId: '7dd77sdy8asd89a8989w',
            inputValue: 83.3333333,
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 11.1,
        discountWithoutPromo: 9.36,
        price: 17.3482143,
        qty: 1,
        total: 6.25,
        a4Total: 6.25,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 44.64,
        discountWithoutPromo: 44.64,
        price: 89.2857142857,
        qty: 1,
        total: 44.65,
        a4Total: 44.65,
    });
    expect(result.receipt).toEqual({
        discount: 546.81,
        receiptTotalExcTax: 642.35,
        serviceCharge: 9.55,
        takeawayCharges: 0,
        tax: 12.62,
        total: 117.71,
    });
});

test('Func:receipt: PH, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
                promotions: [
                    {
                        inputValue: 83.3333333,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy8asd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 17.3482143,
                quantity: 1,
                taxRate: 0.12,
                itemLevelDiscount: {
                    inputValue: 20,
                    type: 'percent',
                },
                promotions: [
                    {
                        inputValue: 10,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy95sd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
            {
                itemType: 'Discount',
                inputValue: 50,
                type: 'percent',
                taxRate: 0.12,
            },
            {
                itemType: 'ServiceCharge',
                rate: 0.1,
                taxRate: 0.12,
            },
        ],
    };

    const res = calculate(transaction, true);
    expect(res).toEqual({
        amusementTax: 0,
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.54, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
            taxes: [],
        },
        discount: 546.83,
        display: {
            discount: -95.54,
            serviceCharge: 9.55,
            subtotal: 191.06,
            tax: 12.62,
            total: 117.69,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.43,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 600, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                promotions: [
                    {
                        discount: 446.4285714285714,
                        display: { discount: 500 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.374999999999998,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.21,
                            discount: 1.73,
                            promotionId: '7dd77sdy95sd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.38, discount: 3.13, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.25, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 11.11,
                display: { subtotal: 19.43, tax: 1.5, total: 13.99 },
                fullBillDiscount: 7,
                itemLevelDiscount: { equivalentValue: 3.5, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.732142857142857,
                        display: { discount: 1.94 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.24,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 44.65,
                display: { subtotal: 100, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.54 },
                equivalentValue: 107,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                    taxes: [],
                },
                discount: 0,
                display: { subtotal: 10.7, tax: 1.15, total: 10.7 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.1499999999999986,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.552000000000005,
            },
        ],
        maximumDiscountInputValue: 213.99,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });

    const result = receipt(res, true, 'PH');
    expect(result.items[0].receipt).toEqual({
        discount: 550,
        discountWithoutPromo: 50,
        price: 600,
        qty: 1,
        total: 600,
        a4Total: 50,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 500,
            display: { discount: 500 },
            inputValue: 83.3333333,
            promotionId: '7dd77sdy8asd89a8989w',
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 12.44,
        discountWithoutPromo: 10.5,
        price: 19.43,
        qty: 1,
        total: 19.43,
        a4Total: 6.99,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 50,
        discountWithoutPromo: 50,
        price: 100,
        qty: 1,
        total: 100,
        a4Total: 50,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 546.83,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 642.35,
        lessVAT: 12.62,
        medalOfValorDiscount: 0,
        receiptTotalExcTax: 642.35,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 10.7,
        amusementTax: 0,
        subtotal: 653.82,
        takeawayCharges: 0,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        vatAmount: 11.47,
        zeroRatedSales: 0,
    });
});

test('Func:receipt: PH, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge, lose promotion discount on calculation', () => {
    const transaction = {
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.54, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
        },
        discount: 546.83,
        display: {
            discount: -95.54,
            serviceCharge: 9.55,
            subtotal: 191.06,
            tax: 12.62,
            total: 117.69,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.43,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 600, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                promotions: [
                    {
                        discount: 446.4285714285714,
                        display: { discount: 500 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.374999999999998,
                calculation: {
                    discounts: [
                        { deductedTax: 0.38, discount: 3.13, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.25, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                },
                isServiceChargeNotApplicable: false,
                discount: 11.11,
                display: { subtotal: 19.43, tax: 1.5, total: 13.99 },
                fullBillDiscount: 7,
                itemLevelDiscount: { equivalentValue: 3.5, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.732142857142857,
                        display: { discount: 1.94 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.24,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                },
                isServiceChargeNotApplicable: false,
                discount: 44.65,
                display: { subtotal: 100, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.54 },
                equivalentValue: 107,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                },
                discount: 0,
                display: { subtotal: 10.7, tax: 1.15, total: 10.7 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.1499999999999986,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.552000000000005,
            },
        ],
        maximumDiscountInputValue: 213.99,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    };

    const result = receipt(transaction, true, 'PH');
    expect(result.items[0].receipt).toEqual({
        discount: 550,
        discountWithoutPromo: 50,
        price: 600,
        qty: 1,
        total: 600,
        a4Total: 50,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 500,
            display: { discount: 500 },
            inputValue: 83.3333333,
            promotionId: '7dd77sdy8asd89a8989w',
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 12.44,
        discountWithoutPromo: 10.71,
        price: 19.43,
        qty: 1,
        total: 19.43,
        a4Total: 6.99,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 50,
        discountWithoutPromo: 50,
        price: 100,
        qty: 1,
        total: 100,
        a4Total: 50,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 546.83,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 642.35,
        lessVAT: 12.62,
        medalOfValorDiscount: 0,
        receiptTotalExcTax: 642.35,
        pwdDiscount: 0,
        amusementTax: 0,
        seniorDiscount: 0,
        serviceCharge: 10.7,
        subtotal: 653.82,
        takeawayCharges: 0,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        vatAmount: 11.47,
        zeroRatedSales: 0,
    });
});

test('Func:receipt: PH, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge, lose promotion discount on calculation, hasDiscountColumn', () => {
    const transaction = {
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.54, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
        },
        discount: 546.83,
        display: {
            discount: -95.54,
            serviceCharge: 9.55,
            subtotal: 191.06,
            tax: 12.62,
            total: 117.69,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.43,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 600, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                promotions: [
                    {
                        discount: 446.4285714285714,
                        display: { discount: 500 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.374999999999998,
                calculation: {
                    discounts: [
                        { deductedTax: 0.38, discount: 3.13, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.25, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                },
                isServiceChargeNotApplicable: false,
                discount: 11.11,
                display: { subtotal: 19.43, tax: 1.5, total: 13.99 },
                fullBillDiscount: 7,
                itemLevelDiscount: { equivalentValue: 3.5, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.732142857142857,
                        display: { discount: 1.94 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.24,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                },
                isServiceChargeNotApplicable: false,
                discount: 44.65,
                display: { subtotal: 100, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.54 },
                equivalentValue: 107,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                },
                discount: 0,
                display: { subtotal: 10.7, tax: 1.15, total: 10.7 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.1499999999999986,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.552000000000005,
            },
        ],
        maximumDiscountInputValue: 213.99,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    };

    const result = receipt(transaction, true, 'PH', true);
    expect(result.items[0].receipt).toEqual({
        discount: 550,
        discountWithoutPromo: 50,
        price: 600,
        qty: 1,
        total: 50,
        a4Total: 50,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 500,
            display: { discount: 500 },
            inputValue: 83.3333333,
            promotionId: '7dd77sdy8asd89a8989w',
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 12.44,
        discountWithoutPromo: 10.71,
        price: 19.43,
        qty: 1,
        total: 6.99,
        a4Total: 6.99,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 50,
        discountWithoutPromo: 50,
        price: 100,
        qty: 1,
        total: 50,
        a4Total: 50,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 546.83,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 642.35,
        lessVAT: 12.62,
        medalOfValorDiscount: 0,
        receiptTotalExcTax: 642.35,
        pwdDiscount: 0,
        amusementTax: 0,
        seniorDiscount: 0,
        serviceCharge: 10.7,
        subtotal: 653.82,
        takeawayCharges: 0,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        vatAmount: 11.47,
        zeroRatedSales: 0,
    });
});

test('Func:receipt: PH, taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
                promotions: [
                    {
                        inputValue: 83.3333333,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy8asd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 17.3482143,
                quantity: 1,
                taxRate: 0.12,
                itemLevelDiscount: {
                    inputValue: 20,
                    type: 'percent',
                },
                promotions: [
                    {
                        inputValue: 10,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy95sd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
            {
                itemType: 'Discount',
                inputValue: 50,
                type: 'percent',
                taxRate: 0.12,
            },
            {
                itemType: 'ServiceCharge',
                rate: 0.1,
                taxRate: 0.12,
            },
        ],
    };

    const res = calculate(transaction, false);
    expect(res).toEqual({
        amusementTax: 0,
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.53, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
            taxes: [],
        },
        discount: 546.81,
        display: {
            discount: -95.53,
            serviceCharge: 9.55,
            subtotal: 191.07,
            tax: 12.62,
            total: 117.71,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.65,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.42,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.65, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 535.71, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.65,
                notRoundedOriginalTax: 5.36,
                promotions: [
                    {
                        discount: 446.42,
                        display: { discount: 446.42 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.36,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.21,
                            discount: 1.74,
                            promotionId: '7dd77sdy95sd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.37, discount: 3.12, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.24, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 11.1,
                display: { subtotal: 17.35, tax: 1.5, total: 12.49 },
                fullBillDiscount: 6.24,
                itemLevelDiscount: { equivalentValue: 3.12, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.74,
                        display: { discount: 1.74 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.25,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 44.64,
                display: { subtotal: 89.29, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.64,
                notRoundedOriginalTax: 5.36,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.65,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.53 },
                equivalentValue: 95.53,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                    taxes: [],
                },
                discount: 0,
                display: { subtotal: 9.55, tax: 1.15, total: 9.55 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.15,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.554000000000006,
            },
        ],
        maximumDiscountInputValue: 191.07,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.71,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });

    const result = receipt(res, false, 'PH');
    expect(result.items[0].receipt).toEqual({
        discount: 491.07,
        discountWithoutPromo: 44.65,
        price: 535.7142857,
        qty: 1,
        total: 535.71,
        a4Total: 44.64,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 446.42,
            display: { discount: 446.42 },
            promotionId: '7dd77sdy8asd89a8989w',
            inputValue: 83.3333333,
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 11.1,
        discountWithoutPromo: 9.36,
        price: 17.3482143,
        qty: 1,
        total: 17.35,
        a4Total: 6.25,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 44.64,
        discountWithoutPromo: 44.64,
        price: 89.2857142857,
        qty: 1,
        total: 89.29,
        a4Total: 44.65,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 546.81,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 642.35,
        lessVAT: 12.62,
        medalOfValorDiscount: 0,
        receiptTotalExcTax: 642.35,
        pwdDiscount: 0,
        seniorDiscount: 0,
        amusementTax: 0,
        serviceCharge: 10.7,
        subtotal: 653.82,
        takeawayCharges: 0,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.71,
        vatAmount: 11.47,
        zeroRatedSales: 0,
    });
});

test('Func:receipt: PH, taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge, hasDiscountColumn', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
                promotions: [
                    {
                        inputValue: 83.3333333,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy8asd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 17.3482143,
                quantity: 1,
                taxRate: 0.12,
                itemLevelDiscount: {
                    inputValue: 20,
                    type: 'percent',
                },
                promotions: [
                    {
                        inputValue: 10,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy95sd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
            {
                itemType: 'Discount',
                inputValue: 50,
                type: 'percent',
                taxRate: 0.12,
            },
            {
                itemType: 'ServiceCharge',
                rate: 0.1,
                taxRate: 0.12,
            },
        ],
    };

    const res = calculate(transaction, false);
    expect(res).toEqual({
        amusementTax: 0,
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.53, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
            taxes: [],
        },
        discount: 546.81,
        display: {
            discount: -95.53,
            serviceCharge: 9.55,
            subtotal: 191.07,
            tax: 12.62,
            total: 117.71,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.65,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.42,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.65, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 535.71, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.65,
                notRoundedOriginalTax: 5.36,
                promotions: [
                    {
                        discount: 446.42,
                        display: { discount: 446.42 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.36,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.21,
                            discount: 1.74,
                            promotionId: '7dd77sdy95sd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.37, discount: 3.12, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.24, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 11.1,
                display: { subtotal: 17.35, tax: 1.5, total: 12.49 },
                fullBillDiscount: 6.24,
                itemLevelDiscount: { equivalentValue: 3.12, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.74,
                        display: { discount: 1.74 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.25,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 44.64,
                display: { subtotal: 89.29, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.64,
                notRoundedOriginalTax: 5.36,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.65,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.53 },
                equivalentValue: 95.53,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                    taxes: [],
                },
                discount: 0,
                display: { subtotal: 9.55, tax: 1.15, total: 9.55 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.15,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.554000000000006,
            },
        ],
        maximumDiscountInputValue: 191.07,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.71,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });

    const result = receipt(res, false, 'PH', true);
    expect(result.items[0].receipt).toEqual({
        discount: 491.07,
        discountWithoutPromo: 44.65,
        price: 535.7142857,
        qty: 1,
        total: 44.64,
        a4Total: 44.64,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 446.42,
            display: { discount: 446.42 },
            promotionId: '7dd77sdy8asd89a8989w',
            inputValue: 83.3333333,
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 11.1,
        discountWithoutPromo: 9.36,
        price: 17.3482143,
        qty: 1,
        total: 6.25,
        a4Total: 6.25,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 44.64,
        discountWithoutPromo: 44.64,
        price: 89.2857142857,
        qty: 1,
        total: 44.65,
        a4Total: 44.65,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 546.81,
        athleteAndCoachDiscount: 0,
        soloParentDiscount: 0,
        discountable: 642.35,
        lessVAT: 12.62,
        medalOfValorDiscount: 0,
        pwdDiscount: 0,
        receiptTotalExcTax: 642.35,
        seniorDiscount: 0,
        amusementTax: 0,
        serviceCharge: 10.7,
        subtotal: 653.82,
        takeawayCharges: 0,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.71,
        vatAmount: 11.47,
        zeroRatedSales: 0,
    });
});

test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
    const transaction = {
        items: [
            {
                unitPrice: 267.857143,
                selectedOptions: [
                    {
                        optionValue: 89.2857143,
                        quantity: 1,
                    },
                    {
                        optionValue: 44.6428571,
                        quantity: 4,
                    },
                ],
                quantity: 1,
                taxRate: 0.12,
                promotions: [
                    {
                        inputValue: 83.3333333,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy8asd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 17.3482143,
                quantity: 1,
                taxRate: 0.12,
                itemLevelDiscount: {
                    inputValue: 20,
                    type: 'percent',
                },
                promotions: [
                    {
                        inputValue: 10,
                        type: 'percentage',
                        quantity: 1,
                        promotionId: '7dd77sdy95sd89a8989w',
                    },
                ],
            },
            {
                unitPrice: 89.2857142857,
                quantity: 1,
                taxRate: 0.12,
            },
            {
                itemType: 'Discount',
                inputValue: 50,
                type: 'percent',
                taxRate: 0.12,
            },
            {
                itemType: 'ServiceCharge',
                rate: 0.1,
                taxRate: 0.12,
            },
        ],
    };

    const res = calculate(transaction, true);
    expect(res).toEqual({
        amusementTax: 0,
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.54, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
            taxes: [],
        },
        discount: 546.83,
        display: {
            discount: -95.54,
            serviceCharge: 9.55,
            subtotal: 191.06,
            tax: 12.62,
            total: 117.69,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.43,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 491.07,
                display: { subtotal: 600, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                promotions: [
                    {
                        discount: 446.4285714285714,
                        display: { discount: 500 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.374999999999998,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.21,
                            discount: 1.73,
                            promotionId: '7dd77sdy95sd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.38, discount: 3.13, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.25, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 11.11,
                display: { subtotal: 19.43, tax: 1.5, total: 13.99 },
                fullBillDiscount: 7,
                itemLevelDiscount: { equivalentValue: 3.5, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.732142857142857,
                        display: { discount: 1.94 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.24,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                    taxes: [],
                },
                isServiceChargeNotApplicable: false,
                discount: 44.65,
                display: { subtotal: 100, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.54 },
                equivalentValue: 107,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                    taxes: [],
                },
                discount: 0,
                display: { subtotal: 10.7, tax: 1.15, total: 10.7 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.1499999999999986,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.552000000000005,
            },
        ],
        maximumDiscountInputValue: 213.99,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });

    const result = receipt(res, true, 'MY');
    expect(result.items[0].receipt).toEqual({
        discount: 550,
        discountWithoutPromo: 50,
        price: 600,
        qty: 1,
        total: 600,
        a4Total: 50,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 500,
            display: { discount: 500 },
            inputValue: 83.3333333,
            promotionId: '7dd77sdy8asd89a8989w',
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 12.44,
        discountWithoutPromo: 10.5,
        price: 19.43,
        qty: 1,
        total: 19.43,
        a4Total: 6.99,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 50,
        discountWithoutPromo: 50,
        price: 100,
        qty: 1,
        total: 100,
        a4Total: 50,
    });
    expect(result.receipt).toEqual({
        discount: 546.83,
        receiptTotalExcTax: 642.35,
        serviceCharge: 9.55,
        takeawayCharges: 0,
        tax: 12.62,
        total: 117.69,
    });
});

test('Func:receipt: MY, taxInclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge, lose promotion discount on calculation', () => {
    const transaction = {
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.54, type: 'TransactionManualDiscount' }],
            original: { subtotal: 642.35, tax: 84.79, total: 791.37 },
        },
        discount: 546.83,
        display: {
            discount: -95.54,
            serviceCharge: 9.55,
            subtotal: 191.06,
            tax: 12.62,
            total: 117.69,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.43,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subtotal: 535.71, tax: 64.29, total: 600 },
                },
                discount: 491.07,
                display: { subtotal: 600, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                promotions: [
                    {
                        discount: 446.4285714285714,
                        display: { discount: 500 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.374999999999998,
                calculation: {
                    discounts: [
                        { deductedTax: 0.38, discount: 3.13, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.25, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subtotal: 17.35, tax: 2.08, total: 19.43 },
                },
                discount: 11.11,
                display: { subtotal: 19.43, tax: 1.5, total: 13.99 },
                fullBillDiscount: 7,
                itemLevelDiscount: { equivalentValue: 3.5, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.732142857142857,
                        display: { discount: 1.94 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.24,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64285714285714,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subtotal: 89.29, tax: 10.71, total: 100 },
                },
                discount: 44.65,
                display: { subtotal: 100, tax: 10.71, total: 100 },
                fullBillDiscount: 50,
                notRoundedOriginalTax: 5.359999999999999,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.54 },
                equivalentValue: 107,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subtotal: 64.23, tax: 7.71, total: 71.94 },
                },
                discount: 0,
                display: { subtotal: 10.7, tax: 1.15, total: 10.7 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.1499999999999986,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.552000000000005,
            },
        ],
        maximumDiscountInputValue: 213.99,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.69,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    };

    const result = receipt(transaction, true, 'MY');
    expect(result.items[0].receipt).toEqual({
        discount: 550,
        discountWithoutPromo: 50,
        price: 600,
        qty: 1,
        total: 600,
        a4Total: 50,
    });
    expect(result.items[0].promotions).toEqual([
        {
            discount: 500,
            display: { discount: 500 },
            inputValue: 83.3333333,
            promotionId: '7dd77sdy8asd89a8989w',
            quantity: 1,
            type: 'percentage',
        },
    ]);
    expect(result.items[1].receipt).toEqual({
        discount: 12.44,
        discountWithoutPromo: 10.71,
        price: 19.43,
        qty: 1,
        total: 19.43,
        a4Total: 6.99,
    });
    expect(result.items[2].receipt).toEqual({
        discount: 50,
        discountWithoutPromo: 50,
        price: 100,
        qty: 1,
        total: 100,
        a4Total: 50,
    });
    expect(result.receipt).toEqual({
        discount: 546.83,
        receiptTotalExcTax: 642.35,
        serviceCharge: 9.55,
        takeawayCharges: 0,
        tax: 12.62,
        total: 117.69,
    });
});

test('Func: receipt: MY, taxInclusiveDisplay: transaction level promotion (absolute) with item level condition(isRepeatable), totalAvailableQuantity > minQuantity', () => {
    const absolutePromotion = {
        _id: 'pid',
        business: 'b',
        discountType: 'absolute',
        discountValue: 10,
        isEnabled: true,
        validTimeTo: 24,
        validTimeFrom: 0,
        validTo: null,
        validFrom: new Date('2019-05-22T00:00:00.00008:00'),
        requiredProducts: [],
        isRepeatable: true,
        minQuantity: 3,
        maxQuantity: 5,
        conditions: [
            {
                operand: ['c1', 'c2'],
                entity: 'product',
                propertyName: 'category',
                operator: 'in',
            },
        ],
        taxCode: 'tax1',
        taxRate: 0.1,
    };
    const absoluteTransaction = {
        business: 'b',
        total: 132,
        channel: ChannelType.ECOMMERCE,
        items: [
            {
                id: 'id1',
                quantity: 2,
                unitPrice: 10,
                productId: 'pid1',
                taxRate: 0.1,
                taxCode: 'tax1',
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
            {
                id: 'id2',
                quantity: 5,
                unitPrice: 20,
                productId: 'pid2',
                taxRate: 0.1,
                taxCode: 'tax1',
                product: {
                    _id: 'pid2',
                    category: 'c2',
                },
            },
        ],
    };

    promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], {
        timezone: 'Asia/Kuala_Lumpur',
    });
    expect(absoluteTransaction).toEqual({
        business: 'b',
        channel: 1,
        items: [
            {
                id: 'id1',
                product: { _id: 'pid1', category: 'c1' },
                productId: 'pid1',
                promotionAppliedQuantityMap: { pid: 2 },
                promotionPlaceholderQuantityMap: {},
                quantity: 2,
                taxCode: 'tax1',
                taxRate: 0.1,
                unitPrice: 10,
            },
            {
                id: 'id2',
                product: { _id: 'pid2', category: 'c2' },
                productId: 'pid2',
                promotionAppliedQuantityMap: { pid: 5 },
                promotionPlaceholderQuantityMap: {},
                quantity: 5,
                taxCode: 'tax1',
                taxRate: 0.1,
                unitPrice: 20,
            },
        ],
        promotions: [
            {
                discountType: 'absolute',
                inputValue: 10,
                maxDiscountAmount: undefined,
                originalDiscountType: undefined,
                promotionCode: undefined,
                promotionId: 'pid',
                promotionName: undefined,
                promotionType: 'merchant',
                quantity: 2,
                storehubPaidPercentage: undefined,
                taxCode: 'tax1',
                taxRate: 0.1,
                type: 'absolute',
            },
        ],
        total: 132,
    });

    const res = calculate(absoluteTransaction, true);
    expect(res).toEqual({
        business: 'b',
        amusementTax: 0,
        calculation: {
            discounts: [
                {
                    deductedTax: 1.82,
                    discount: 18.18,
                    promotionId: 'pid',
                    subType: 'absolute',
                    type: 'Promotion',
                },
            ],
            original: { subtotal: 120, tax: 12, total: 132 },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 10.18,
                    taxCode: 'tax1',
                    taxRate: 0.1,
                },
            ],
        },
        channel: 1,
        discount: 18.18,
        display: { discount: 0, serviceCharge: 0, subtotal: 101.82, tax: 10.18, total: 112 },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 3.027272727272727,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.3,
                            discount: 3.03,
                            promotionId: 'pid',
                            subType: 'absolute',
                            type: 'Promotion',
                        },
                    ],
                    fullPrice: 10,
                    original: { subtotal: 20, tax: 2, total: 22 },
                    taxes: [
                        {
                            isAmusementTax: false,
                            isVatExempted: false,
                            tax: 1.7,
                            taxCode: 'tax1',
                            taxRate: 0.1,
                        },
                    ],
                },
                discount: 3.03,
                display: { subtotal: 22, tax: 2, total: 22 },
                id: 'id1',
                notRoundedOriginalTax: 1.7000000000000028,
                product: { _id: 'pid1', category: 'c1' },
                productId: 'pid1',
                promotionAppliedQuantityMap: { pid: 2 },
                promotionDiscount: 3.33,
                promotionPlaceholderQuantityMap: {},
                quantity: 2,
                subTotal: 20,
                tax: 1.7,
                taxCode: 'tax1',
                taxRate: 0.1,
                total: 16.97,
                unitPrice: 10,
            },
            {
                adhocDiscount: 15.154545454545454,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 1.52,
                            discount: 15.15,
                            promotionId: 'pid',
                            subType: 'absolute',
                            type: 'Promotion',
                        },
                    ],
                    fullPrice: 20,
                    original: { subtotal: 100, tax: 10, total: 110 },
                    taxes: [
                        {
                            isAmusementTax: false,
                            isVatExempted: false,
                            tax: 8.48,
                            taxCode: 'tax1',
                            taxRate: 0.1,
                        },
                    ],
                },
                discount: 15.15,
                display: { subtotal: 110, tax: 10, total: 110 },
                id: 'id2',
                notRoundedOriginalTax: 8.480000000000004,
                product: { _id: 'pid2', category: 'c2' },
                productId: 'pid2',
                promotionAppliedQuantityMap: { pid: 5 },
                promotionDiscount: 16.67,
                promotionPlaceholderQuantityMap: {},
                quantity: 5,
                subTotal: 100,
                tax: 8.48,
                taxCode: 'tax1',
                taxRate: 0.1,
                total: 84.85,
                unitPrice: 20,
            },
        ],
        maximumDiscountInputValue: 112,
        promotions: [
            {
                discount: 18.18,
                discountType: 'absolute',
                display: { discount: 20 },
                inputValue: 10,
                maxDiscountAmount: undefined,
                originalDiscountType: undefined,
                promotionCode: undefined,
                promotionId: 'pid',
                promotionName: undefined,
                promotionType: 'merchant',
                quantity: 2,
                storehubPaidPercentage: undefined,
                taxCode: 'tax1',
                taxRate: 0.1,
                type: 'absolute',
            },
        ],
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 0,
        serviceChargeTax: 0,
        subtotal: 120,
        takeawayCharges: 0,
        tax: 10.18,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 112,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });
    const result = receipt(res, true, 'MY');
    expect(result.items[0].receipt).toEqual({
        discount: 3.33,
        discountWithoutPromo: 3.33,
        price: 11,
        qty: 2,
        total: 22,
        a4Total: 18.67,
    });
    expect(result.items[1].receipt).toEqual({
        discount: 16.67,
        discountWithoutPromo: 16.67,
        price: 22,
        qty: 5,
        total: 110,
        a4Total: 93.33,
    });
    expect(result.receipt).toEqual({
        discount: 18.18,
        receiptTotalExcTax: 120,
        serviceCharge: 0,
        takeawayCharges: 0,
        tax: 10.18,
        total: 112,
    });
});

test('Func: receipt: MY, taxInclusiveDisplay: transaction level promotion (absolute) with item level condition(isRepeatable), totalAvailableQuantity > minQuantity, hasDiscountColumn', () => {
    const absolutePromotion = {
        _id: 'pid',
        business: 'b',
        discountType: 'absolute',
        discountValue: 10,
        isEnabled: true,
        validTimeTo: 24,
        validTimeFrom: 0,
        validTo: null,
        validFrom: new Date('2019-05-22T00:00:00.00008:00'),
        requiredProducts: [],
        isRepeatable: true,
        minQuantity: 3,
        maxQuantity: 5,
        conditions: [
            {
                operand: ['c1', 'c2'],
                entity: 'product',
                propertyName: 'category',
                operator: 'in',
            },
        ],
        taxCode: 'tax1',
        taxRate: 0.1,
    };
    const absoluteTransaction = {
        business: 'b',
        total: 132,
        channel: ChannelType.ECOMMERCE,
        items: [
            {
                id: 'id1',
                quantity: 2,
                unitPrice: 10,
                productId: 'pid1',
                taxRate: 0.1,
                taxCode: 'tax1',
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
            {
                id: 'id2',
                quantity: 5,
                unitPrice: 20,
                productId: 'pid2',
                taxRate: 0.1,
                taxCode: 'tax1',
                product: {
                    _id: 'pid2',
                    category: 'c2',
                },
            },
        ],
    };

    promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], {
        timezone: 'Asia/Kuala_Lumpur',
    });
    expect(absoluteTransaction).toEqual({
        business: 'b',
        channel: 1,
        items: [
            {
                id: 'id1',
                product: { _id: 'pid1', category: 'c1' },
                productId: 'pid1',
                promotionAppliedQuantityMap: { pid: 2 },
                promotionPlaceholderQuantityMap: {},
                quantity: 2,
                taxCode: 'tax1',
                taxRate: 0.1,
                unitPrice: 10,
            },
            {
                id: 'id2',
                product: { _id: 'pid2', category: 'c2' },
                productId: 'pid2',
                promotionAppliedQuantityMap: { pid: 5 },
                promotionPlaceholderQuantityMap: {},
                quantity: 5,
                taxCode: 'tax1',
                taxRate: 0.1,
                unitPrice: 20,
            },
        ],
        promotions: [
            {
                discountType: 'absolute',
                inputValue: 10,
                maxDiscountAmount: undefined,
                originalDiscountType: undefined,
                promotionCode: undefined,
                promotionId: 'pid',
                promotionName: undefined,
                promotionType: 'merchant',
                quantity: 2,
                storehubPaidPercentage: undefined,
                taxCode: 'tax1',
                taxRate: 0.1,
                type: 'absolute',
            },
        ],
        total: 132,
    });

    const res = calculate(absoluteTransaction, true);
    expect(res).toEqual({
        business: 'b',
        amusementTax: 0,
        calculation: {
            discounts: [
                {
                    deductedTax: 1.82,
                    discount: 18.18,
                    promotionId: 'pid',
                    subType: 'absolute',
                    type: 'Promotion',
                },
            ],
            original: { subtotal: 120, tax: 12, total: 132 },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 10.18,
                    taxCode: 'tax1',
                    taxRate: 0.1,
                },
            ],
        },
        channel: 1,
        discount: 18.18,
        display: { discount: 0, serviceCharge: 0, subtotal: 101.82, tax: 10.18, total: 112 },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 3.027272727272727,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.3,
                            discount: 3.03,
                            promotionId: 'pid',
                            subType: 'absolute',
                            type: 'Promotion',
                        },
                    ],
                    fullPrice: 10,
                    original: { subtotal: 20, tax: 2, total: 22 },
                    taxes: [
                        {
                            isAmusementTax: false,
                            isVatExempted: false,
                            tax: 1.7,
                            taxCode: 'tax1',
                            taxRate: 0.1,
                        },
                    ],
                },
                discount: 3.03,
                display: { subtotal: 22, tax: 2, total: 22 },
                id: 'id1',
                notRoundedOriginalTax: 1.7000000000000028,
                product: { _id: 'pid1', category: 'c1' },
                productId: 'pid1',
                promotionAppliedQuantityMap: { pid: 2 },
                promotionDiscount: 3.33,
                promotionPlaceholderQuantityMap: {},
                quantity: 2,
                subTotal: 20,
                tax: 1.7,
                taxCode: 'tax1',
                taxRate: 0.1,
                total: 16.97,
                unitPrice: 10,
            },
            {
                adhocDiscount: 15.154545454545454,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 1.52,
                            discount: 15.15,
                            promotionId: 'pid',
                            subType: 'absolute',
                            type: 'Promotion',
                        },
                    ],
                    fullPrice: 20,
                    original: { subtotal: 100, tax: 10, total: 110 },
                    taxes: [
                        {
                            isAmusementTax: false,
                            isVatExempted: false,
                            tax: 8.48,
                            taxCode: 'tax1',
                            taxRate: 0.1,
                        },
                    ],
                },
                discount: 15.15,
                display: { subtotal: 110, tax: 10, total: 110 },
                id: 'id2',
                notRoundedOriginalTax: 8.480000000000004,
                product: { _id: 'pid2', category: 'c2' },
                productId: 'pid2',
                promotionAppliedQuantityMap: { pid: 5 },
                promotionDiscount: 16.67,
                promotionPlaceholderQuantityMap: {},
                quantity: 5,
                subTotal: 100,
                tax: 8.48,
                taxCode: 'tax1',
                taxRate: 0.1,
                total: 84.85,
                unitPrice: 20,
            },
        ],
        maximumDiscountInputValue: 112,
        promotions: [
            {
                discount: 18.18,
                discountType: 'absolute',
                display: { discount: 20 },
                inputValue: 10,
                maxDiscountAmount: undefined,
                originalDiscountType: undefined,
                promotionCode: undefined,
                promotionId: 'pid',
                promotionName: undefined,
                promotionType: 'merchant',
                quantity: 2,
                storehubPaidPercentage: undefined,
                taxCode: 'tax1',
                taxRate: 0.1,
                type: 'absolute',
            },
        ],
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 0,
        serviceChargeTax: 0,
        subtotal: 120,
        takeawayCharges: 0,
        tax: 10.18,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 112,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });
    const result = receipt(res, true, 'MY', true);
    expect(result.items[0].receipt).toEqual({
        discount: 3.33,
        discountWithoutPromo: 3.33,
        price: 11,
        qty: 2,
        total: 18.67,
        a4Total: 18.67,
    });
    expect(result.items[1].receipt).toEqual({
        discount: 16.67,
        discountWithoutPromo: 16.67,
        price: 22,
        qty: 5,
        total: 93.33,
        a4Total: 93.33,
    });
    expect(result.receipt).toEqual({
        discount: 18.18,
        receiptTotalExcTax: 120,
        serviceCharge: 0,
        takeawayCharges: 0,
        tax: 10.18,
        total: 112,
    });
});

test('calculator:receipt:PH Func:receipt:taxExclusiveDisplay:item, Non-BIR, test rounding issue', () => {
    const transaction = {
        headcount: 1,
        seniorsCount: 0,
        pwdCount: 0,
        birInfo: {
            type: 'f&b',
            discountType: null,
            discountRate: 0,
            seniorsCount: 0,
            pwdCount: 0,
            headCount: 1,
        },
        items: [
            {
                id: 'id1',
                productId: 'pid1',
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
                unitPrice: 607.14,
                // unitPrice: 607.1428571428571,
                quantity: 2,
                taxCode: 'tax1',
                taxRate: 0.12,
            },
            {
                id: 'id2',
                productId: 'pid2',
                product: {
                    _id: 'pid2',
                    category: 'c1',
                },
                unitPrice: 580.36,
                // unitPrice: 580.3571428571428,
                quantity: 3,
                taxCode: 'tax1',
                taxRate: 0.12,
            },
            {
                id: 'id3',
                productId: 'pid3',
                product: {
                    _id: 'pid3',
                    category: 'c1',
                },
                unitPrice: 607.14,
                // unitPrice: 607.1428571428571,
                quantity: 3,
                taxCode: 'tax1',
                taxRate: 0.12,
            },
        ],
    };

    const res = calculate(transaction, true);

    expect(res).toEqual({
        amusementTax: 0,
        headcount: 1,
        seniorsCount: 0,
        pwdCount: 0,
        birInfo: {
            type: 'f&b',
            discountType: null,
            discountRate: 0,
            seniorsCount: 0,
            pwdCount: 0,
            headCount: 1,
        },
        calculation: {
            discounts: [],
            original: {
                subtotal: 4776.79,
                tax: 573.21,
                total: 5350,
            },
            taxes: [
                {
                    isAmusementTax: false,
                    isVatExempted: false,
                    tax: 573.21,
                    taxCode: 'tax1',
                    taxRate: 0.12,
                },
            ],
        },
        discount: 0,
        display: {
            discount: 0,
            serviceCharge: 0,
            subtotal: 4776.79,
            tax: 573.21,
            total: 5350,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 607.14,
                    original: {
                        subtotal: 1214.29,
                        tax: 145.71,
                        total: 1360,
                    },
                    taxes: [
                        {
                            isAmusementTax: false,
                            isVatExempted: false,
                            tax: 145.71,
                            taxCode: 'tax1',
                            taxRate: 0.12,
                        },
                    ],
                },
                discount: 0,
                display: {
                    subtotal: 1360,
                    tax: 145.71,
                    total: 1360,
                },
                id: 'id1',
                notRoundedOriginalTax: 145.71000000000004,
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
                productId: 'pid1',
                quantity: 2,
                subTotal: 1214.29,
                tax: 145.71,
                taxCode: 'tax1',
                taxRate: 0.12,
                taxableAmount: 1214.29,
                total: 1214.29,
                unitPrice: 607.14,
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 580.36,
                    original: {
                        subtotal: 1741.07,
                        tax: 208.93,
                        total: 1950,
                    },
                    taxes: [
                        {
                            isAmusementTax: false,
                            isVatExempted: false,
                            tax: 208.93,
                            taxCode: 'tax1',
                            taxRate: 0.12,
                        },
                    ],
                },
                discount: 0,
                display: {
                    subtotal: 1950,
                    tax: 208.93,
                    total: 1950,
                },
                id: 'id2',
                notRoundedOriginalTax: 208.93000000000006,
                product: {
                    _id: 'pid2',
                    category: 'c1',
                },
                productId: 'pid2',
                quantity: 3,
                subTotal: 1741.07,
                tax: 208.93,
                taxCode: 'tax1',
                taxRate: 0.12,
                taxableAmount: 1741.07,
                total: 1741.07,
                unitPrice: 580.36,
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 607.14,
                    original: {
                        subtotal: 1821.43,
                        tax: 218.57,
                        total: 2040,
                    },
                    taxes: [
                        {
                            isAmusementTax: false,
                            isVatExempted: false,
                            tax: 218.57,
                            taxCode: 'tax1',
                            taxRate: 0.12,
                        },
                    ],
                },
                discount: 0,
                display: {
                    subtotal: 2040,
                    tax: 218.57,
                    total: 2040,
                },
                id: 'id3',
                notRoundedOriginalTax: 218.56999999999994,
                product: {
                    _id: 'pid3',
                    category: 'c1',
                },
                productId: 'pid3',
                quantity: 3,
                subTotal: 1821.43,
                tax: 218.57,
                taxCode: 'tax1',
                taxRate: 0.12,
                taxableAmount: 1821.43,
                total: 1821.43,
                unitPrice: 607.14,
            },
        ],
        maximumDiscountInputValue: 5350,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 0,
        serviceChargeTax: 0,
        subtotal: 4776.79,
        takeawayCharges: 0,
        tax: 573.21,
        taxExemptedSales: 0,
        taxableSales: 4776.79,
        total: 5350,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    });

    const result = receipt(res, true, 'PH');
    expect(result.items[0].receipt).toEqual({
        a4Total: 1360,
        discount: 0,
        discountWithoutPromo: 0,
        price: 680,
        qty: 2,
        total: 1360,
    });
    expect(result.items[1].receipt).toEqual({
        a4Total: 1950,
        discount: 0,
        discountWithoutPromo: 0,
        price: 650,
        qty: 3,
        total: 1950,
    });
    expect(result.receipt).toEqual({
        adhocDiscount: 0,
        amusementTax: 0,
        athleteAndCoachDiscount: 0,
        discountable: 4776.79,
        lessVAT: 573.21,
        medalOfValorDiscount: 0,
        pwdDiscount: 0,
        receiptTotalExcTax: 4776.79,
        seniorDiscount: 0,
        serviceCharge: 0,
        soloParentDiscount: 0,
        subtotal: 5350,
        takeawayCharges: 0,
        taxExemptedSales: 0,
        taxableSales: 4776.79,
        total: 5350,
        vatAmount: 573.21,
        zeroRatedSales: 0,
    });
});
