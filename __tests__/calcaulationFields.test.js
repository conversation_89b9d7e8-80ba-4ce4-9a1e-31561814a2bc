import { insertTrxCalculationDiscount } from '../src/calculator/calculationFields';

test('Func: insertTrxCalculationDiscount: PH, taxExclusiveDisplay:item, selectedOptions, takePercentageOffPromotion, fullBillDiscount, ServiceCharge', () => {
    const transaction = {
        calculation: {
            discounts: [{ deductedTax: 11.46, discount: 95.53, type: 'TransactionManualDiscount' }],
            original: { subTotal: 642.35, tax: 84.79, total: 791.37 },
        },
        discount: 546.81,
        display: {
            discount: -95.53,
            serviceCharge: 9.55,
            subtotal: 191.07,
            tax: 12.62,
            total: 117.71,
        },
        fixedFee: 0,
        isBirDiscountAvailable: false,
        items: [
            {
                adhocDiscount: 44.65,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 53.57,
                            discount: 446.42,
                            promotionId: '7dd77sdy8asd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 5.36, discount: 44.65, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 535.7142857,
                    original: { subTotal: 535.71, tax: 64.29, total: 600 },
                },
                discount: 491.07,
                display: { subtotal: 535.71, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.65,
                notRoundedOriginalTax: 5.36,
                promotions: [
                    {
                        discount: 446.42,
                        display: { discount: 446.42 },
                        inputValue: 83.3333333,
                        promotionId: '7dd77sdy8asd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                selectedOptions: [
                    { optionValue: 89.2857143, quantity: 1 },
                    { optionValue: 44.6428571, quantity: 4 },
                ],
                subTotal: 535.71,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.64,
                unitPrice: 267.857143,
            },
            {
                adhocDiscount: 9.36,
                calculation: {
                    discounts: [
                        {
                            deductedTax: 0.21,
                            discount: 1.74,
                            promotionId: '7dd77sdy95sd89a8989w',
                            subType: 'percentage',
                            type: 'Promotion',
                        },
                        { deductedTax: 0.37, discount: 3.12, type: 'ItemManualDiscount' },
                        { deductedTax: 0.75, discount: 6.24, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 17.3482143,
                    original: { subTotal: 17.35, tax: 2.08, total: 19.43 },
                },
                discount: 11.1,
                display: { subtotal: 17.35, tax: 1.5, total: 12.49 },
                fullBillDiscount: 6.24,
                itemLevelDiscount: { equivalentValue: 3.12, inputValue: 20, type: 'percent' },
                notRoundedOriginalTax: 0.75,
                promotions: [
                    {
                        discount: 1.74,
                        display: { discount: 1.74 },
                        inputValue: 10,
                        promotionId: '7dd77sdy95sd89a8989w',
                        quantity: 1,
                        type: 'percentage',
                    },
                ],
                quantity: 1,
                subTotal: 17.35,
                tax: 0.75,
                taxRate: 0.12,
                total: 6.25,
                unitPrice: 17.3482143,
            },
            {
                adhocDiscount: 44.64,
                calculation: {
                    discounts: [
                        { deductedTax: 5.36, discount: 44.64, type: 'TransactionManualDiscount' },
                    ],
                    fullPrice: 89.2857142857,
                    original: { subTotal: 89.29, tax: 10.71, total: 100 },
                },
                discount: 44.64,
                display: { subtotal: 89.29, tax: 10.71, total: 89.29 },
                fullBillDiscount: 44.64,
                notRoundedOriginalTax: 5.36,
                quantity: 1,
                subTotal: 89.29,
                tax: 5.36,
                taxRate: 0.12,
                total: 44.65,
                unitPrice: 89.2857142857,
            },
            {
                display: { discount: 95.53 },
                equivalentValue: 95.53,
                inputValue: 50,
                itemType: 'Discount',
                taxRate: 0.12,
                type: 'percent',
            },
            {
                adhocDiscount: 0,
                calculation: {
                    discounts: [],
                    fullPrice: 64.235,
                    original: { subTotal: 64.23, tax: 7.71, total: 71.94 },
                },
                discount: 0,
                display: { subtotal: 9.55, tax: 1.15, total: 9.55 },
                itemType: 'ServiceCharge',
                notRoundedOriginalTax: 1.15,
                quantity: 1,
                rate: 0.1,
                subTotal: 9.55,
                tax: 1.15,
                taxRate: 0.12,
                total: 9.55,
                unitPrice: 9.554000000000006,
            },
        ],
        maximumDiscountInputValue: 191.07,
        pwdDiscount: 0,
        seniorDiscount: 0,
        serviceCharge: 9.55,
        serviceChargeTax: 1.15,
        subtotal: 642.35,
        takeawayCharges: 0,
        tax: 12.62,
        taxExemptedSales: 0,
        taxableSales: 0,
        total: 117.71,
        totalDeductedTax: 0,
        zeroRatedSales: 0,
    };

    insertTrxCalculationDiscount(transaction, 'TransactionManualDiscount', 100, 12);
    expect(transaction.calculation).toEqual({
        discounts: [{ deductedTax: 12, discount: 100, type: 'TransactionManualDiscount' }],
        original: { subTotal: 642.35, tax: 84.79, total: 791.37 },
    });
});
