import calculator from '../../index';
import { ChannelType } from '../../src/utils';

describe('combo promotions', () => {
    const transactionSession = {
        channel: ChannelType.ECOMMERCE,
        transactionId: 'fe750473-8e3c-4aca-82d0-e45bbe32fd4b',
        appVersion: '********',
        transactionType: 'Sale',
        appliedPriceBooks: ['5d08c85d0055d90017ec7ad4', '5d1ef4ed49694b0018a4fa3c'],
        priorPriceBook: {
            targetStore: '5b62d783081ccd016185cf08',
            id: '5d1ef4ed49694b0018a4fa3c',
            name: 'vip',
            order: 0.5,
            serviceChargeRate: 0.05,
            serviceChargeTax: null,
            targetCustomerType: '',
            enableServiceCharge: true,
        },
        items: [
            {
                itemType: 'ServiceCharge',
                rate: 0.05,
                taxRate: 0,
                product: {},
                unitPrice: 0.47150000000000003,
                quantity: 1,
                total: 0.47,
                tax: 0,
                discount: 0,
                subTotal: 0.47,
            },
            {
                id: '1:5ccfe19c143eda001986a033',
                productId: '5ccfe19c143eda001986a033',
                unitPrice: 9.433962264150942,
                priceType: 'fixed',
                kitchenPrinter: '',
                selectedOptions: [],
                quantity: 1,
                taxRate: 0.06,
                title: 'Bun',
                total: 9.43,
                taxCode: '5d08c8430055d90017ec7ad3',
                product: {
                    category: null,
                    tags: [],
                    _id: '5ccfe19c143eda001986a033',
                },
                taxInclusiveSubtotal: 10,
                taxExclusiveSubtotal: 9.43,
                adhocDiscount: 0,
                tax: 0.57,
                discount: 0,
                subTotal: 9.43,
                display: {
                    total: 10,
                    subtotal: 10,
                    tax: 0.57,
                },
            },
        ],
        serialNumbers: {},
        subtotal: 9.43,
        discount: 0,
        tax: 0.57,
        total: 10.47,
        serviceCharge: 0.47,
        serviceChargeTax: 0,
        display: {
            subtotal: 9.43,
            discount: 0,
            serviceCharge: 0.47,
            tax: 0.57,
            total: 10.47,
        },
        business: 'ck',
        storeId: '5b62d783081ccd016185cf08',
    };

    const comboPromotion = {
        _id: '5c3d48c7ba8b0401fb0a8751',
        promotionId: '5c3d48c7ba8b0401fb0a8751',
        appliedStores: null,
        createdTime: '2019-01-15T02:43:19.272Z',
        discountType: 'combo',
        discountValue: 2,
        isEnabled: true,
        maxQuantity: 5,
        minQuantity: 3,
        modifiedTime: '2019-09-16T11:21:23.582Z',
        name: 'Combo',
        ordering: 5.3125,
        taxCode: '5d08c8430055d90017ec7ad3',
        validDays: null,
        validFrom: '2019-09-15T16:00:00.000Z',
        validTimeFrom: 0,
        validTimeTo: 24,
        validTo: null,
        conditions: [
            {
                maxQuantity: 1,
                minQuantity: 1,
                objectName: null,
                entity: 'product',
                operand: ['5ccfe19c143eda001986a033'],
                operator: 'in',
                propertyName: 'id',
                owner: null,
                requiredProductOwner: {},
            },
        ],
        requiredProducts: [],
        business: 'ck',
        taxRate: 0.06,
        owner: null,
    };

    test('apply result should contain taxRate', () => {
        const promotions = [comboPromotion];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;
        const errors = calculator.applyPromotion(transactionSession, promotions, businessSettings);
        expect(errors.length).toBe(0);
        expect(transactionSession.promotions[0].taxRate).toBe(0.06);
        calculator.calculate(transactionSession, includingTaxInDisplay);

        expect(transactionSession.items[1].promotions).toBeUndefined();
        expect(transactionSession.promotions.length).toBeDefined();
    });

    test('partial item applied to combo', () => {
        const trx = {
            business: 'mp51_test',
            channel: ChannelType.ECOMMERCE,
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0,
                    product: {},
                    unitPrice: 37.736000000000004,
                    quantity: 1,
                    total: 37.74,
                    tax: 0,
                    discount: 0,
                    subTotal: 37.74,
                },
                {
                    id: '1:5d426383d0a2520017afabf5',
                    productId: '5d426383d0a2520017afabf5',
                    unitPrice: 94.34,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 4,
                    taxRate: 0.06,
                    title: 'Franzzi',
                    total: 377.36,
                    taxCode: '5d3fcf2d8733f400169a5264',
                    product: {
                        category: 'Divya',
                        tags: ['divya'],
                        _id: '5d426383d0a2520017afabf5',
                    },
                    taxInclusiveSubtotal: 400,
                    taxExclusiveSubtotal: 377.36,
                    tax: 22.64,
                    discount: 0,
                    subTotal: 377.36,
                    adhocDiscount: 0,
                    display: {
                        total: 400,
                        subtotal: 400,
                        tax: 22.64,
                    },
                },
                {
                    id: '2:5ceced2a9ddbdb01d064f5c7',
                    productId: '5ceced2a9ddbdb01d064f5c7',
                    unitPrice: 94.34,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.06,
                    title: 'The product123',
                    total: 0,
                    taxCode: '5d3fcf2d8733f400169a5264',
                    product: {
                        category: 'Divya',
                        tags: [],
                        _id: '5ceced2a9ddbdb01d064f5c7',
                    },
                },
            ],
            discount: 0,
            display: {
                subtotal: 377.36,
                discount: 0,
                serviceCharge: 37.74,
                tax: 22.64,
                total: 437.74,
            },
            serviceChargeTax: 0,
            subtotal: 377.36,
            total: 437.74,
            serviceCharge: 37.74,
            serviceChargeRate: 0.1,
            tax: 22.64,
            serialNumbers: {},
            appVersion: '********',
            transactionType: 'Sale',
            transactionId: 'e9be70ee-b4de-4119-86ca-d49f11d7ffee',
            storeId: '5c493dc4fdab93018f10eb04',
            serviceChargeTaxId: '',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5c36bf47223be802458124fc',

                promotionId: '5c36bf47223be802458124fc',
                appliedStores: null,
                createdTime: '2019-01-10T03:43:03.479Z',
                discountType: 'combo',
                discountValue: 94.33962264150944,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-11-15T06:57:55.348Z',
                ordering: 2,
                taxCode: '5d3fcf2d8733f400169a5264',
                validDays: null,
                validFrom: '2019-11-14T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: 2,
                        minQuantity: 2,
                        objectName: null,
                        entity: 'product',
                        operand: ['Divya'],
                        operator: 'in',
                        propertyName: 'category',
                        owner: null,
                        requiredProductOwner: {},
                    },
                    {
                        maxQuantity: 1,
                        minQuantity: 1,
                        objectName: null,
                        entity: 'product',
                        operand: ['divya'],
                        operator: 'contains',
                        propertyName: 'tags',
                        owner: null,
                        requiredProductOwner: {},
                    },
                    {
                        maxQuantity: 1,
                        minQuantity: 1,
                        objectName: null,
                        entity: 'product',
                        operand: ['5ceced2a9ddbdb01d064f5c7'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [],
                business: 'mp51_test',
                taxRate: 0.06,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = false;
        calculator.calculate(trx, includingTaxInDisplay);
        calculator.applyPromotion(trx, promotions, businessSettings);
        calculator.calculate(trx, includingTaxInDisplay);
        expect(trx.items[1].promotionAppliedQuantityMap).toEqual({ '5c36bf47223be802458124fc': 3 });
        expect(trx.items[2].promotionAppliedQuantityMap).toEqual({ '5c36bf47223be802458124fc': 1 });
    });

    test('all item applied to combo', () => {
        const trx = {
            business: 'mp51_test',
            channel: ChannelType.ECOMMERCE,
            items: [
                {
                    id: '1:5d426383d0a2520017afabf5',
                    productId: '5d426383d0a2520017afabf5',
                    unitPrice: 18,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 4,
                    taxRate: 0,
                    title: 'gmd1',
                    total: 72,
                    taxCode: '5d3fcf2d8733f400169a5264',
                    product: {
                        category: 'Divya',
                        tags: ['divya'],
                        _id: '5d426383d0a2520017afabf5',
                    },
                    taxInclusiveSubtotal: 72,
                    taxExclusiveSubtotal: 72,
                    tax: 0,
                    discount: 0,
                    subTotal: 72,
                    adhocDiscount: 0,
                    display: {
                        total: 72,
                        subtotal: 72,
                        tax: 0,
                    },
                },
                {
                    id: '1:5d426383d0a2520017afabf6',
                    productId: '5d426383d0a2520017afabf6',
                    unitPrice: 18,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 4,
                    taxRate: 0,
                    title: 'gmd2',
                    total: 72,
                    taxCode: '5d3fcf2d8733f400169a5264',
                    product: {
                        category: 'Divya',
                        tags: ['divya'],
                        _id: '5d426383d0a2520017afabf6',
                    },
                    taxInclusiveSubtotal: 72,
                    taxExclusiveSubtotal: 72,
                    tax: 0,
                    discount: 0,
                    subTotal: 72,
                    adhocDiscount: 0,
                    display: {
                        total: 72,
                        subtotal: 72,
                        tax: 0,
                    },
                },
                {
                    id: '1:5d426383d0a2520017afabf7',
                    productId: '5d426383d0a2520017afabf7',
                    unitPrice: 18,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 4,
                    taxRate: 0,
                    title: 'gmd3',
                    total: 72,
                    taxCode: '5d3fcf2d8733f400169a5264',
                    product: {
                        category: 'Divya',
                        tags: ['divya'],
                        _id: '5d426383d0a2520017afabf7',
                    },
                    taxInclusiveSubtotal: 72,
                    taxExclusiveSubtotal: 72,
                    tax: 0,
                    discount: 0,
                    subTotal: 72,
                    adhocDiscount: 0,
                    display: {
                        total: 72,
                        subtotal: 72,
                        tax: 0,
                    },
                },
            ],
            discount: 0,
            display: {
                subtotal: 216,
                discount: 0,
                serviceCharge: 0,
                tax: 0,
                total: 216,
            },
            serviceChargeTax: 0,
            subtotal: 216,
            total: 216,
            serviceCharge: 0,
            serviceChargeRate: 0,
            tax: 0,
            serialNumbers: {},
            appVersion: '********',
            transactionType: 'Sale',
            transactionId: 'e9be70ee-b4de-4119-86ca-d49f11d7ffee',
            storeId: '5c493dc4fdab93018f10eb04',
            serviceChargeTaxId: '',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5c36bf47223be802458124fc',
                promotionId: '5c36bf47223be802458124fc',
                appliedStores: null,
                createdTime: '2019-01-10T03:43:03.479Z',
                discountType: 'combo',
                discountValue: 185,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-11-15T06:57:55.348Z',
                ordering: 2,
                taxCode: '5d3fcf2d8733f400169a5264',
                validDays: null,
                validFrom: '2019-11-14T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: 4,
                        minQuantity: 4,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d426383d0a2520017afabf5'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                    {
                        maxQuantity: 4,
                        minQuantity: 4,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d426383d0a2520017afabf6'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                    {
                        maxQuantity: 4,
                        minQuantity: 4,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d426383d0a2520017afabf7'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [],
                business: 'mp51_test',
                taxRate: 0,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;
        calculator.calculate(trx, includingTaxInDisplay);
        calculator.applyPromotion(trx, promotions, businessSettings);
        calculator.calculate(trx, includingTaxInDisplay);
        expect(trx.display).toEqual({
            discount: 0,
            serviceCharge: 0,
            subtotal: 185,
            tax: 0,
            total: 185,
        });
    });
});
