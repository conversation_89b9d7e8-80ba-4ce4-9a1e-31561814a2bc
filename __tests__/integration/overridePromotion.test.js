import calculator from '../../index';

describe('multiple promotions', () => {
    test('success', () => {
        const transactionSession = {
            business: 'android',
            items: [
                {
                    id: '5d495ad48c98300016266530',
                    productId: '5d495ad48c98300016266530',
                    unitPrice: 100,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 5,
                    taxRate: 0.05,
                    title: 'Different Tax Code',
                    total: 425,
                    taxCode: '5d4956378c98300016266421',
                    product: {
                        category: 'Price Category',
                        tags: [],
                        _id: '5d495ad48c98300016266530',
                    },
                    taxInclusiveSubtotal: 525,
                    taxExclusiveSubtotal: 500,
                    tax: 21.25,
                    discount: 75,
                    subTotal: 500,
                    taxInclusiveUnitPrice: 105,
                    promotions: [
                        {
                            inputValue: 15,
                            type: 'percentage',
                            discountType: 'percentage',
                            promotionId: '5d4a8797b52ce300171b8057',
                            promotionName: 'Take % Off',
                            taxCode: '5cf4ed0f0c7a7e001605e4f7',
                            discount: 75,
                            display: {
                                discount: 78.75,
                            },
                        },
                    ],
                    promotionAppliedQuantityMap: {
                        '5d4a8797b52ce300171b8057': 5,
                    },
                    adhocDiscount: 0,
                    display: {
                        total: 446.25,
                        subtotal: 525,
                        tax: 21.25,
                    },
                },
                {
                    id: '5d4959b68c98300016266520',
                    productId: '5d4959b68c98300016266520',
                    unitPrice: 6.9,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 7,
                    taxRate: 0.1,
                    title: 'Popiah',
                    total: 56.39,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Local Favourites',
                        tags: [],
                        _id: '5d4959b68c98300016266520',
                    },
                    taxInclusiveSubtotal: 53.13,
                    taxExclusiveSubtotal: 48.3,
                    tax: 5.64,
                    discount: -8.09,
                    subTotal: 48.3,
                    taxInclusiveUnitPrice: 7.59,
                    itemLevelDiscount: {
                        inputValue: 0,
                        type: 'amount',
                    },
                    discountInputValue: 0,
                    discountType: 'amount',
                    promotions: [
                        {
                            inputValue: 15,
                            type: 'percentage',
                            discountType: 'percentage',
                            promotionId: '5d4a8797b52ce300171b8057',
                            promotionName: 'Take % Off',
                            taxCode: '5cf4ed0f0c7a7e001605e4f7',
                            discount: 7.245454545454544,
                            display: {
                                discount: 7.97,
                            },
                        },
                        {
                            inputValue: 9.09090909090909,
                            type: 'fixedUnitPrice',
                            discountType: 'fixedUnitPrice',
                            promotionId: '5d4a9908b52ce300171b8107',
                            promotionName: 'Override Existing Price',
                            taxCode: '5cf4ed0f0c7a7e001605e4f7',
                            discount: -15.336363636363636,
                            display: {
                                discount: -16.87,
                            },
                        },
                    ],
                    promotionAppliedQuantityMap: {
                        '5d4a8797b52ce300171b8057': 7,
                        '5d4a9908b52ce300171b8107': 7,
                    },
                    adhocDiscount: 0,
                    display: {
                        total: 62.03,
                        subtotal: 53.13,
                        tax: 5.64,
                    },
                },
                {
                    id: '5d4a43948c983000162665d0',
                    productId: '5d4a43948c983000162665d0',
                    unitPrice: 9.9,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 3,
                    taxRate: 0.1,
                    title: 'Wan Tan Mee',
                    total: 22.82,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Local Favourites',
                        tags: [],
                        _id: '5d4a43948c983000162665d0',
                    },
                    taxInclusiveSubtotal: 32.67,
                    taxExclusiveSubtotal: 29.7,
                    tax: 2.28,
                    discount: 6.88,
                    subTotal: 29.7,
                    taxInclusiveUnitPrice: 10.89,
                    promotions: [
                        {
                            inputValue: 15,
                            type: 'percentage',
                            discountType: 'percentage',
                            promotionId: '5d4a8797b52ce300171b8057',
                            promotionName: 'Take % Off',
                            taxCode: '5cf4ed0f0c7a7e001605e4f7',
                            discount: 4.454545454545454,
                            display: {
                                discount: 4.9,
                            },
                        },
                        {
                            inputValue: 9.09090909090909,
                            type: 'fixedUnitPrice',
                            discountType: 'fixedUnitPrice',
                            promotionId: '5d4a9908b52ce300171b8107',
                            promotionName: 'Override Existing Price',
                            taxCode: '5cf4ed0f0c7a7e001605e4f7',
                            discount: 2.427272727272727,
                            display: {
                                discount: 2.67,
                            },
                        },
                    ],
                    promotionAppliedQuantityMap: {
                        '5d4a8797b52ce300171b8057': 2,
                        '5d4a9908b52ce300171b8107': 2,
                    },
                    adhocDiscount: 0,
                    display: {
                        total: 25.1,
                        subtotal: 32.67,
                        tax: 2.28,
                    },
                },
            ],
            discount: 73.79,
            serviceChargeTax: 0,
            subtotal: 578,
            total: 533.38,
            serviceCharge: 0,
            tax: 29.17,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            transactionId: '4ded87ea-8eee-4382-9874-c805356dda91',
            storeId: '5cf4ed0e0c7a7e001605e4f3',
            appliedPriceBooks: [],
            display: {
                subtotal: 504.21,
                discount: 0,
                serviceCharge: 0,
                tax: 29.17,
                total: 533.38,
            },
        };

        const promotions = [
            {
                _id: '5d4a9908b52ce300171b8107',
                promotionId: '5d4a9908b52ce300171b8107',
                appliedStores: ['5cf4ed0e0c7a7e001605e4f3'],
                createdTime: '2019-08-07T09:25:28.457Z',
                discountType: 'fixedUnitPrice',
                discountValue: 9.09090909090909,
                isEnabled: true,
                maxQuantity: 7,
                minQuantity: 0.01,
                modifiedTime: '2019-08-09T10:20:15.033Z',
                name: 'Override Existing Price',
                ordering: 4,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: null,
                        objectName: null,
                        entity: 'product',
                        operand: [
                            '5d4959b68c98300016266520',
                            '5d4a43948c983000162665d0',
                            '5d49571c8c98300016266427',
                        ],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [
                    {
                        maxQuantity: null,
                        minQuantity: 4,
                        objectName: null,
                        entity: 'product',
                        operand: ['Price Category'],
                        operator: 'in',
                        propertyName: 'category',
                        owner: {},
                        requiredProductOwner: null,
                    },
                ],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        calculator.applyPromotion(transactionSession, promotions, businessSettings);

        const record = calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(record.items[1].promotions).toBeUndefined();
    });
});
