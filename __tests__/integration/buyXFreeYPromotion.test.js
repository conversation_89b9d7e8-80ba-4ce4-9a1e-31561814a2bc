import calculator from '../../index';
import { ChannelType } from '../../src/utils';

describe('buyXFreeY promotions', () => {
    test('single promotion with one item applied', () => {
        const transactionSession = {
            business: 'android',
            items: [
                {
                    id: '5d4a8e56b52ce300171b80ab',
                    productId: '5d4a8e56b52ce300171b80ab',
                    unitPrice: 14.545454545454545,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 2,
                    taxRate: 0.1,
                    title: 'Fried Chicken',
                    total: 14.55,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e56b52ce300171b80ab',
                    },
                    taxInclusiveSubtotal: 16,
                    taxExclusiveSubtotal: 14.55,
                    adhocDiscount: 0,
                    tax: 1.45,
                    discount: 0,
                    subTotal: 14.55,
                    display: {
                        total: 16,
                        subtotal: 16,
                        tax: 1.45,
                    },
                },
            ],
            serviceChargeTax: 0,
            serviceCharge: 0,
            tax: 1.45,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            transactionId: '63e409e9-a197-4936-827c-d01f8be28124',
            storeId: '5cf4ed0e0c7a7e001605e4f3',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5d4bdfc7813e4b33ee753290',
                promotionId: '5d4bdfc7813e4b33ee753290',
                appliedStores: null,
                createdTime: '2019-08-08T08:39:35.740Z',
                discountType: 'buyXFreeY',
                discountValue: 1,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-08-09T05:26:43.195Z',
                name: 'Buy 2, Free 1',
                ordering: 8,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 2,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d4a8e85b52ce300171b80ad', '5d4a8e56b52ce300171b80ab'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        const errors = calculator.applyPromotion(transactionSession, promotions, businessSettings);
        const record = calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(record.items[0].promotions).toBeUndefined();
        expect(errors.length).toBe(1);
    });

    test('multiple promotion with one item applied', () => {
        const transactionSession = {
            business: 'android',
            channel: ChannelType.ECOMMERCE,
            items: [
                {
                    id: '5d4a8e56b52ce300171b80ab',
                    productId: '5d4a8e56b52ce300171b80ab',
                    unitPrice: 14.545454545454545,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 3,
                    taxRate: 0.1,
                    title: 'Fried Chicken',
                    total: 29.09,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e56b52ce300171b80ab',
                    },
                },
                {
                    id: '5d4a8e85b52ce300171b80ad',
                    productId: '5d4a8e85b52ce300171b80ad',
                    unitPrice: 18.18181818181818,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 2,
                    taxRate: 0.1,
                    title: 'Chicken Wings',
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e85b52ce300171b80ad',
                    },
                },
            ],
            serviceChargeTax: 0,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            transactionId: '0899eed5-620d-41a3-b7e8-9bbbe8e84c52',
            storeId: '5cf4ed0e0c7a7e001605e4f3',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5d4a9908b52ce300171b8107',
                promotionId: '5d4a9908b52ce300171b8107',
                appliedStores: ['5cf4ed0e0c7a7e001605e4f3'],
                createdTime: '2019-08-07T09:25:28.457Z',
                discountType: 'fixedUnitPrice',
                discountValue: 9.09090909090909,
                isEnabled: true,
                maxQuantity: 7,
                minQuantity: 0.01,
                modifiedTime: '2019-08-09T10:20:15.033Z',
                name: 'Override Existing Price',
                ordering: 4,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: null,
                        objectName: null,
                        entity: 'product',
                        operand: [
                            '5d4959b68c98300016266520',
                            '5d4a43948c983000162665d0',
                            '5d49571c8c98300016266427',
                        ],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [
                    {
                        maxQuantity: null,
                        minQuantity: 4,
                        objectName: null,
                        entity: 'product',
                        operand: ['Price Category'],
                        operator: 'in',
                        propertyName: 'category',
                        owner: {},
                        requiredProductOwner: null,
                    },
                ],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
            {
                _id: '5d4bdfc7813e4b33ee753290',
                promotionId: '5d4bdfc7813e4b33ee753290',
                appliedStores: null,
                createdTime: '2019-08-08T08:39:35.740Z',
                discountType: 'buyXFreeY',
                discountValue: 1,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-08-12T09:20:29.452Z',
                name: 'Buy 2, Free 1',
                ordering: 8,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 2,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d4a8e85b52ce300171b80ad', '5d4a8e56b52ce300171b80ab'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        calculator.applyPromotion(transactionSession, promotions, businessSettings);
        const record = calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(record.items[0].promotions.length).toBe(1);
        expect(record.items[0].promotions[0].inputValue).toBe(1);
        expect(record.items[1].promotions.length).toBe(0);
    });

    test('multiple promotion with two combo applied', () => {
        const transactionSession = {
            business: 'android',
            channel: ChannelType.ECOMMERCE,
            items: [
                {
                    id: '5d4a8e56b52ce300171b80ab',
                    productId: '5d4a8e56b52ce300171b80ab',
                    unitPrice: 14.545454545454545,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 3,
                    taxRate: 0.1,
                    title: 'Fried Chicken',
                    total: 29.09,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e56b52ce300171b80ab',
                    },
                },
                {
                    id: '5d4a8e85b52ce300171b80ad',
                    productId: '5d4a8e85b52ce300171b80ad',
                    unitPrice: 18.18181818181818,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 3,
                    taxRate: 0.1,
                    title: 'Chicken Wings',
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e85b52ce300171b80ad',
                    },
                },
            ],
            serviceChargeTax: 0,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            transactionId: '0899eed5-620d-41a3-b7e8-9bbbe8e84c52',
            storeId: '5cf4ed0e0c7a7e001605e4f3',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5d4bdfc7813e4b33ee753290',
                promotionId: '5d4bdfc7813e4b33ee753290',
                appliedStores: null,
                createdTime: '2019-08-08T08:39:35.740Z',
                discountType: 'buyXFreeY',
                discountValue: 1,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-08-12T09:20:29.452Z',
                name: 'Buy 2, Free 1',
                ordering: 8,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 2,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d4a8e85b52ce300171b80ad', '5d4a8e56b52ce300171b80ab'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        calculator.applyPromotion(transactionSession, promotions, businessSettings);
        const record = calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(record.items[0].promotions.length).toBe(1);
    });

    test('multiple promotion with three combo applied', () => {
        const transactionSession = {
            channel: ChannelType.ECOMMERCE,
            business: 'android',
            items: [
                {
                    id: '5d4a8e56b52ce300171b80ab',
                    productId: '5d4a8e56b52ce300171b80ab',
                    unitPrice: 14.545454545454545,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 2,
                    taxRate: 0.1,
                    title: 'Fried Chicken',
                    total: 29.09,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e56b52ce300171b80ab',
                    },
                },
                {
                    id: '5d4a8e85b52ce300171b80ad',
                    productId: '5d4a8e85b52ce300171b80ad',
                    unitPrice: 18.18181818181818,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 4,
                    taxRate: 0.1,
                    title: 'Chicken Wings',
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e85b52ce300171b80ad',
                    },
                },
            ],
            serviceChargeTax: 0,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            transactionId: '0899eed5-620d-41a3-b7e8-9bbbe8e84c52',
            storeId: '5cf4ed0e0c7a7e001605e4f3',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5d4bdfc7813e4b33ee753290',
                promotionId: '5d4bdfc7813e4b33ee753290',
                appliedStores: null,
                createdTime: '2019-08-08T08:39:35.740Z',
                discountType: 'buyXFreeY',
                discountValue: 1,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-08-12T09:20:29.452Z',
                name: 'Buy 2, Free 1',
                ordering: 8,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 1,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d4a8e85b52ce300171b80ad', '5d4a8e56b52ce300171b80ab'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        calculator.applyPromotion(transactionSession, promotions, businessSettings);
        const record = calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(record.items[1].promotions.length).toBe(1);
        expect(record.items[0].promotions.length).toBe(1);
        expect(record.items[0].promotions[0].inputValue).toBe(2);
        expect(record.items[1].promotions[0].inputValue).toBe(1);
    });

    test('buyXFreeY reduce available quantity', () => {
        const transactionSession = {
            channel: ChannelType.ECOMMERCE,
            business: 'android',
            items: [
                {
                    id: '5d4a8e56b52ce300171b80ab',
                    productId: '5d4a8e56b52ce300171b80ab',
                    unitPrice: 10,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 4,
                    taxRate: 0,
                    title: 'Fried Chicken',
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e56b52ce300171b80ab',
                    },
                },
            ],
            serviceChargeTax: 0,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            transactionId: '0899eed5-620d-41a3-b7e8-9bbbe8e84c52',
            storeId: '5cf4ed0e0c7a7e001605e4f3',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5d4bdfc7813e4b33ee753290',
                promotionId: '5d4bdfc7813e4b33ee753290',
                appliedStores: null,
                createdTime: '2019-08-08T08:39:35.740Z',
                discountType: 'buyXFreeY',
                discountValue: 1,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-08-12T09:20:29.452Z',
                name: 'Buy 2, Free 1',
                ordering: 7,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 2,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d4a8e85b52ce300171b80ad', '5d4a8e56b52ce300171b80ab'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },

            {
                _id: '5d4bdfc7813e4b33ee753290',
                promotionId: '5d4bdfc7813e4b33ee753290',
                appliedStores: null,
                createdTime: '2019-08-08T08:39:35.740Z',
                discountType: 'percentage',
                discountValue: 10,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-08-12T09:20:29.452Z',
                name: '10% off',
                ordering: 8,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-08T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 1,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d4a8e85b52ce300171b80ad', '5d4a8e56b52ce300171b80ab'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: [],
                    },
                ],
                requiredProducts: [],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        calculator.applyPromotion(transactionSession, promotions, businessSettings);
        const record = calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(record.items[0].promotions.length).toBe(2);
    });

    test('buyXFreeY with not applicable items', () => {
        const transactionSession = {
            business: 'android',
            channel: ChannelType.ECOMMERCE,
            items: [
                {
                    id: '5d4a8e27b52ce300171b80a8',
                    productId: '5d4a8e27b52ce300171b80a8',
                    unitPrice: 13.636363636363635,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.1,
                    title: 'Chicken Burger',
                    total: 13.64,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e27b52ce300171b80a8',
                    },
                    taxInclusiveSubtotal: 15,
                    taxExclusiveSubtotal: 13.64,
                    tax: 1.36,
                    discount: 0,
                    subTotal: 13.64,
                    adhocDiscount: 0,
                    display: {
                        total: 15,
                        subtotal: 15,
                        tax: 1.36,
                    },
                },
                {
                    id: '5d4a8e85b52ce300171b80ad',
                    productId: '5d4a8e85b52ce300171b80ad',
                    unitPrice: 18.18181818181818,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.1,
                    title: 'Chicken Wings',
                    total: 18.18,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e85b52ce300171b80ad',
                    },
                    taxInclusiveSubtotal: 20,
                    taxExclusiveSubtotal: 18.18,
                    tax: 1.82,
                    discount: 0,
                    subTotal: 18.18,
                    adhocDiscount: 0,
                    display: {
                        total: 20,
                        subtotal: 20,
                        tax: 1.82,
                    },
                },
                {
                    id: '5d4a8e56b52ce300171b80ab',
                    productId: '5d4a8e56b52ce300171b80ab',
                    unitPrice: 14.545454545454545,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 2,
                    taxRate: 0.1,
                    title: 'Fried Chicken',
                    total: 14.55,
                    taxCode: '5cf4ed0f0c7a7e001605e4f7',
                    product: {
                        category: 'Fast Food',
                        tags: ['fast food'],
                        _id: '5d4a8e56b52ce300171b80ab',
                    },
                    taxInclusiveSubtotal: 16,
                    taxExclusiveSubtotal: 14.55,
                    adhocDiscount: 0,
                    tax: 1.45,
                    discount: 0,
                    subTotal: 14.55,
                    display: {
                        total: 16,
                        subtotal: 16,
                        tax: 1.45,
                    },
                },
            ],
            discount: 0,
            display: {
                subtotal: 46.37,
                discount: 0,
                serviceCharge: 0,
                tax: 4.63,
                total: 51,
            },
            serviceChargeTax: 0,
            subtotal: 46.37,
            total: 51,
            serviceCharge: 0,
            tax: 4.63,
            serialNumbers: {},
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            transactionId: 'b2305299-a5ef-4870-b0b6-64a00c9dd407',
            storeId: '5cf4ed0e0c7a7e001605e4f3',
            appliedPriceBooks: [],
        };

        const promotions = [
            {
                _id: '5d4bdfc7813e4b33ee753290',
                promotionId: '5d4bdfc7813e4b33ee753290',
                appliedStores: null,
                createdTime: '2019-08-08T08:39:35.740Z',
                discountType: 'buyXFreeY',
                discountValue: 1,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-08-13T08:35:33.857Z',
                name: 'Buy 2, Free 1',
                ordering: 8,
                taxCode: '5cf4ed0f0c7a7e001605e4f7',
                validDays: null,
                validFrom: '2019-08-12T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [
                    {
                        maxQuantity: null,
                        minQuantity: 2,
                        objectName: null,
                        entity: 'product',
                        operand: ['5d4a8e85b52ce300171b80ad', '5d4a8e56b52ce300171b80ab'],
                        operator: 'in',
                        propertyName: 'id',
                        owner: null,
                        requiredProductOwner: {},
                    },
                ],
                requiredProducts: [],
                business: 'android',
                taxRate: 0.1,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;
        calculator.applyPromotion(transactionSession, promotions, businessSettings);
        const record = calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(record.items[0].promotions.length).toBe(0);
    });
});
