import calculator from '../../index';
import { ChannelType } from '../../src/utils';

describe('multiple promotions', () => {
    const takePercentageOffPromotion = {
        _id: '5c11dea4e41aa70183c574d0',
        promotionId: '5c11dea4e41aa70183c574d0',
        appliedStores: null,
        createdTime: '2018-12-13T04:23:00.376Z',
        discountType: 'percentage',
        discountValue: 10,
        isEnabled: true,
        maxQuantity: 4294967295,
        minQuantity: 0.01,
        modifiedTime: '2019-09-16T11:20:36.678Z',
        name: 'TakePercentageOff',
        ordering: 6.375,
        taxCode: '5d08c8430055d90017ec7ad3',
        validDays: null,
        validFrom: '2019-09-15T16:00:00.000Z',
        validTimeFrom: 0,
        validTimeTo: 24,
        conditions: [
            {
                maxQuantity: null,
                minQuantity: null,
                objectName: null,
                entity: 'transaction',
                operand: ['1'],
                operator: 'gte',
                propertyName: 'total',
                owner: null,
                requiredProductOwner: {},
            },
        ],
        requiredProducts: [],
        business: 'ck',
        taxRate: 0.06,
        owner: null,
    };

    const comboPromotion = {
        _id: '5c3d48c7ba8b0401fb0a8751',
        promotionId: '5c3d48c7ba8b0401fb0a8751',
        appliedStores: null,
        createdTime: '2019-01-15T02:43:19.272Z',
        discountType: 'combo',
        discountValue: 2,
        isEnabled: true,
        maxQuantity: 5,
        minQuantity: 3,
        modifiedTime: '2019-09-16T11:21:23.582Z',
        name: 'Combo',
        ordering: 5.3125,
        taxCode: '5d08c8430055d90017ec7ad3',
        validDays: null,
        validFrom: '2019-09-15T16:00:00.000Z',
        validTimeFrom: 0,
        validTimeTo: 24,
        validTo: null,
        conditions: [
            {
                maxQuantity: 1,
                minQuantity: 1,
                objectName: null,
                entity: 'product',
                operand: ['5ccfe19c143eda001986a033'],
                operator: 'in',
                propertyName: 'id',
                owner: null,
                requiredProductOwner: {},
            },
        ],
        requiredProducts: [],
        business: 'ck',
        taxRate: 0.06,
        owner: null,
    };

    test('takePercentagePromotion first, should apply both', () => {
        const transactionSession = {
            channel: ChannelType.ECOMMERCE,
            transactionId: 'fe750473-8e3c-4aca-82d0-e45bbe32fd4b',
            appVersion: '********',
            transactionType: 'Sale',
            appliedPriceBooks: ['5d08c85d0055d90017ec7ad4', '5d1ef4ed49694b0018a4fa3c'],
            priorPriceBook: {
                targetStore: '5b62d783081ccd016185cf08',
                id: '5d1ef4ed49694b0018a4fa3c',
                name: 'vip',
                order: 0.5,
                serviceChargeRate: 0.05,
                serviceChargeTax: null,
                targetCustomerType: '',
                enableServiceCharge: true,
            },
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.05,
                    taxRate: 0,
                    product: {},
                    unitPrice: 0.47150000000000003,
                    quantity: 1,
                    total: 0.47,
                    tax: 0,
                    discount: 0,
                    subTotal: 0.47,
                },
                {
                    id: '1:5ccfe19c143eda001986a033',
                    productId: '5ccfe19c143eda001986a033',
                    unitPrice: 9.433962264150942,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.06,
                    title: 'Bun',
                    total: 9.43,
                    taxCode: '5d08c8430055d90017ec7ad3',
                    product: {
                        category: null,
                        tags: [],
                        _id: '5ccfe19c143eda001986a033',
                    },
                    taxInclusiveSubtotal: 10,
                    taxExclusiveSubtotal: 9.43,
                    adhocDiscount: 0,
                    tax: 0.57,
                    discount: 0,
                    subTotal: 9.43,
                    display: {
                        total: 10,
                        subtotal: 10,
                        tax: 0.57,
                    },
                },
            ],
            serialNumbers: {},
            subtotal: 9.43,
            discount: 0,
            tax: 0.57,
            total: 10.47,
            serviceCharge: 0.47,
            serviceChargeTax: 0,
            display: {
                subtotal: 9.43,
                discount: 0,
                serviceCharge: 0.47,
                tax: 0.57,
                total: 10.47,
            },
            business: 'ck',
            storeId: '5b62d783081ccd016185cf08',
        };
        const promotions = [takePercentageOffPromotion, comboPromotion];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        const errors = calculator.applyPromotion(transactionSession, promotions, businessSettings);
        expect(errors).toHaveLength(0);
        calculator.calculate(transactionSession, includingTaxInDisplay);

        expect(transactionSession.items[1].promotions.length).toBe(1);
        expect(transactionSession.promotions.length).toBeDefined();
    });

    test('comboPromotion first, should only apply combo', () => {
        const transactionSession = {
            channel: ChannelType.ECOMMERCE,
            transactionId: 'fe750473-8e3c-4aca-82d0-e45bbe32fd4b',
            appVersion: '********',
            transactionType: 'Sale',
            appliedPriceBooks: ['5d08c85d0055d90017ec7ad4', '5d1ef4ed49694b0018a4fa3c'],
            priorPriceBook: {
                targetStore: '5b62d783081ccd016185cf08',
                id: '5d1ef4ed49694b0018a4fa3c',
                name: 'vip',
                order: 0.5,
                serviceChargeRate: 0.05,
                serviceChargeTax: null,
                targetCustomerType: '',
                enableServiceCharge: true,
            },
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.05,
                    taxRate: 0,
                    product: {},
                    unitPrice: 0.47150000000000003,
                    quantity: 1,
                    total: 0.47,
                    tax: 0,
                    discount: 0,
                    subTotal: 0.47,
                },
                {
                    id: '1:5ccfe19c143eda001986a033',
                    productId: '5ccfe19c143eda001986a033',
                    unitPrice: 9.433962264150942,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.06,
                    title: 'Bun',
                    total: 9.43,
                    taxCode: '5d08c8430055d90017ec7ad3',
                    product: {
                        category: null,
                        tags: [],
                        _id: '5ccfe19c143eda001986a033',
                    },
                    taxInclusiveSubtotal: 10,
                    taxExclusiveSubtotal: 9.43,
                    adhocDiscount: 0,
                    tax: 0.57,
                    discount: 0,
                    subTotal: 9.43,
                    display: {
                        total: 10,
                        subtotal: 10,
                        tax: 0.57,
                    },
                },
            ],
            serialNumbers: {},
            subtotal: 9.43,
            discount: 0,
            tax: 0.57,
            total: 10.47,
            serviceCharge: 0.47,
            serviceChargeTax: 0,
            display: {
                subtotal: 9.43,
                discount: 0,
                serviceCharge: 0.47,
                tax: 0.57,
                total: 10.47,
            },
            business: 'ck',
            storeId: '5b62d783081ccd016185cf08',
        };
        const promotions = [comboPromotion, takePercentageOffPromotion];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;
        const errors = calculator.applyPromotion(transactionSession, promotions, businessSettings);
        expect(errors).toHaveLength(1);
        calculator.calculate(transactionSession, includingTaxInDisplay);

        expect(transactionSession.items[1].promotions).toEqual([]);
        expect(transactionSession.promotions.length).toBeDefined();
    });

    test('comboPromotion consumes all quantity of one item', () => {
        const transactionSession = {
            business: 'ck',
            channel: ChannelType.ECOMMERCE,
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.05,
                    taxRate: 0,
                    product: {},
                    quantity: 1,
                    tax: 0,
                    promotionAppliedQuantityMap: {},
                },
                {
                    id: '1:5ccfe19c143eda001986a033',
                    productId: '5ccfe19c143eda001986a033',
                    unitPrice: 9.433962264150942,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.06,
                    title: 'Bun',
                    total: 1.89,
                    taxCode: '5d08c8430055d90017ec7ad3',
                    product: {
                        category: null,
                        tags: [],
                        _id: '5ccfe19c143eda001986a033',
                    },
                },
                {
                    id: '2:5cd4ee4ffad73b0017093572',
                    productId: '5cd4ee4ffad73b0017093572',
                    unitPrice: 0.09433962264150944,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [],
                    quantity: 1,
                    taxRate: 0.06,
                    title: 'Cappucino',
                    total: 0.09,
                    taxCode: '5d08c8430055d90017ec7ad3',
                    product: {
                        category: 'Drinks',
                        tags: [],
                        _id: '5cd4ee4ffad73b0017093572',
                    },
                },
            ],
            serviceChargeTax: 0,
            priorPriceBook: {
                targetStore: '5b62d783081ccd016185cf08',
                id: '5d1ef4ed49694b0018a4fa3c',
                name: 'vip',
                order: 0.5,
                serviceChargeRate: 0.05,
                serviceChargeTax: null,
                targetCustomerType: '',
                enableServiceCharge: true,
            },
            serviceCharge: 0.1,
            tax: 0.12,
            serialNumbers: {},
            appVersion: '********',
            transactionType: 'Sale',
            transactionId: 'e1cea044-3dd8-42b7-8c23-42786807933b',
            storeId: '5b62d783081ccd016185cf08',
            appliedPriceBooks: ['5d08c85d0055d90017ec7ad4', '5d1ef4ed49694b0018a4fa3c'],
        };
        const promotions = [comboPromotion, takePercentageOffPromotion];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;
        calculator.calculate(transactionSession, includingTaxInDisplay);
        const errors = calculator.applyPromotion(transactionSession, promotions, businessSettings);
        expect(errors).toHaveLength(0);
        calculator.calculate(transactionSession, includingTaxInDisplay);

        expect(transactionSession.items[2].promotions).toHaveLength(1);
        expect(transactionSession.items[1].promotions).toEqual([]);
        expect(transactionSession.promotions).toHaveLength(1);
    });
});
