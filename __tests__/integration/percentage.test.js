import calculator from '../../index';
import { ChannelType } from '../../src/utils';
import promotionCalculator from '../../src/promotionCalculator';

describe('test TakeAmountOff promotion', () => {
    let business;
    let promotion;
    let items;
    let transaction;

    beforeEach(() => {
        business = {
            timezone: 'Asia/Kuala_Lumpur',
            taxCodes: [],
        };
        promotion = {
            _id: 'pid',

            business: 'b',
            discountType: 'absolute',
            discountValue: 1,
            minQuantity: 1,
            maxQuantity: 2,
            isEnabled: true,
            validTimeTo: 24,
            validTimeFrom: 0,
            validTo: null,
            validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
            requiredProducts: [],
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: null,
                    maxQuantity: null,
                },
            ],
        };
        items = [
            {
                taxCode: '',
                id: 'id1',
                quantity: 1,
                unitPrice: 10,
                productId: 'pid1',
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
        ];
        transaction = {
            business: 'b',
            total: 10,
            customer: { tags: ['c1'] },
            items,
            channel: ChannelType.ECOMMERCE,
        };
    });

    test('success: transaction level promotion (percentage) with item level condition(isRepeatable), apply count with requiredProducts < apply count with conditions, apply multiple times', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 5,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(10);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 10,
            }),
        );
    });

    test('success: transaction level promotion (percentage) with item level condition(isRepeatable), apply count with requiredProducts > apply count with conditions, apply multiple times', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(12);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 12,
            }),
        );
    });

    test('success: transaction level promotion (percentage) with item level condition(isRepeatable), apply count is 0 limited by multiple requiredProducts', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 3,
                    maxQuantity: null,
                },
                {
                    operand: ['c4'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 4,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(percentageTransaction.items[0].promotionAppliedQuantityMap).toBeUndefined();
        expect(percentageTransaction.items[0].promotions).toBeUndefined();
    });

    test('success: transaction level promotion (percentage) with item level condition(isRepeatable),apply count with multiple requiredProducts < apply count with conditions, apply multiple times', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
                {
                    operand: ['c4'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 8,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
                {
                    id: 'id4',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid4',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid4',
                        category: 'c4',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(5);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 5,
            }),
        );
    });

    // requiredProducts
    // 1. A  B   2
    // 2. B  C   2
    // transaction
    // A * 4    B * 2    C * 2
    // should apply 2 times
    test('success: transaction level promotion (percentage) with item level condition(isRepeatable), apply count with requiredProducts < apply count with conditions, apply multiple times, the multiple requiredProducts with Duplicate items', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 2,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['A', 'B', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['A', 'B'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
                {
                    operand: ['B', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 4,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'A',
                    },
                },
                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid2',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'B',
                    },
                },
                {
                    id: 'id3',
                    quantity: 2,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'C',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(4);
        expect(
            percentageTransaction.items[1].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(2);
        expect(
            percentageTransaction.items[2].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(2);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[1].promotions.length).toBe(1);
        expect(percentageTransaction.items[2].promotions.length).toBe(1);

        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 4,
            }),
        );
        expect(percentageTransaction.items[1].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 2,
            }),
        );
        expect(percentageTransaction.items[2].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 2,
            }),
        );
    });

    // requiredProducts
    // 1. A  B   2
    // 2. B  C   2
    // 3. A  C   3
    // transaction
    // A * 6    B * 5    C * 5
    // 分配方案： 1. A*4   2.B*4    3. B*1 (A*2) + C*5
    // should apply 2 times
    test('success: transaction level promotion (percentage) with item level condition(isRepeatable), apply count with requiredProducts < apply count with conditions, apply multiple times, the multiple requiredProducts all with Duplicate items', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 2,
            maxQuantity: 5,
            tag: 'test',
            conditions: [
                {
                    operand: ['A', 'B', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['A', 'B'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
                {
                    operand: ['B', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
                {
                    operand: ['A', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 3,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 6,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'A',
                    },
                },
                {
                    id: 'id2',
                    quantity: 5,
                    unitPrice: 10,
                    productId: 'pid2',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid2',
                        category: 'B',
                    },
                },
                {
                    id: 'id3',
                    quantity: 5,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'C',
                    },
                },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(6);
        expect(
            percentageTransaction.items[1].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(4);
        expect(percentageTransaction.items[2].promotionAppliedQuantityMap).toEqual({});
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[1].promotions.length).toBe(1);

        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 6,
            }),
        );
        expect(percentageTransaction.items[1].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 4,
            }),
        );
        expect(percentageTransaction.items[2].promotions).toEqual([]);
    });

    // requiredProducts
    // 1. A  B   5
    // 2. B  C   4
    // 3. A  C   3
    // 3. A  C   2
    // 4. D  E   1
    // transaction
    // A * 15    B * 8    C * 8   D * 2   E *3
    // 分配方案： 1. A*10   2.B*8    3. A*5 + C*1   4.C*4    4. D*2
    // should apply 2 times
    test('success: transaction level promotion (percentage) with item level condition(isRepeatable), apply count with requiredProducts < apply count with conditions, apply multiple times, the multiple requiredProducts with Duplicate items, complex', () => {
        const percentagePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'percentage',
            minQuantity: 2,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['A', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['A', 'B'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 5,
                    maxQuantity: null,
                },
                {
                    operand: ['B', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 4,
                    maxQuantity: null,
                },
                {
                    operand: ['A', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 3,
                    maxQuantity: null,
                },
                {
                    operand: ['A', 'C'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
                {
                    operand: ['D', 'E'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 1,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const percentageTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 15,
                    unitPrice: 2,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'A',
                    },
                },
                {
                    id: 'id2',
                    quantity: 8,
                    unitPrice: 3,
                    productId: 'pid2',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid2',
                        category: 'B',
                    },
                },
                {
                    id: 'id3',
                    quantity: 8,
                    unitPrice: 4,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'C',
                    },
                },
                {
                    id: 'id4',
                    quantity: 2,
                    unitPrice: 5,
                    productId: 'pid4',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid4',
                        category: 'D',
                    },
                },
                {
                    id: 'id5',
                    quantity: 3,
                    unitPrice: 5,
                    productId: 'pid5',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid5',
                        category: 'E',
                    },
                },
                { itemType: 'ServiceCharge', rate: 0.1, taxRate: 0.12 },
            ],
        };

        promotionCalculator.calculate(percentageTransaction, [percentagePromotion], business);
        expect(
            percentageTransaction.items[0].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(2);
        expect(
            percentageTransaction.items[2].promotionAppliedQuantityMap[percentagePromotion._id],
        ).toBe(8);
        expect(percentageTransaction.items[0].promotions.length).toBe(1);
        expect(percentageTransaction.items[2].promotions.length).toBe(1);

        expect(percentageTransaction.items[0].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 2,
            }),
        );
        expect(percentageTransaction.items[1].promotions).toEqual([]);

        expect(percentageTransaction.items[2].promotions[0]).toEqual(
            expect.objectContaining({
                discountType: 'percentage',
                promotionType: 'merchant',
                quantity: 8,
            }),
        );
        expect(percentageTransaction.items[3].promotions).toEqual([]);
    });
});
