import calculator from '../../index';
import { ChannelType } from '../../src/utils';
import promotionCalculator from '../../src/promotionCalculator';

describe('test TakeAmountOff promotion', () => {
    let business;
    let promotion;
    let items;
    let transaction;

    beforeEach(() => {
        business = {
            timezone: 'Asia/Kuala_Lumpur',
            taxCodes: [],
        };
        promotion = {
            _id: 'pid',

            business: 'b',
            discountType: 'absolute',
            discountValue: 1,
            minQuantity: 1,
            maxQuantity: 2,
            isEnabled: true,
            validTimeTo: 24,
            validTimeFrom: 0,
            validTo: null,
            validFrom: new Date('2019-05-22T00:00:00.000+08:00'),
            requiredProducts: [],
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: null,
                    maxQuantity: null,
                },
            ],
        };
        items = [
            {
                taxCode: '',
                id: 'id1',
                quantity: 1,
                unitPrice: 10,
                productId: 'pid1',
                product: {
                    _id: 'pid1',
                    category: 'c1',
                },
            },
        ];
        transaction = {
            business: 'b',
            total: 10,
            customer: { tags: ['c1'] },
            items,
            channel: ChannelType.ECOMMERCE,
        };
    });

    test('success', () => {
        const transactionSession = {
            channel: ChannelType.ECOMMERCE,
            transactionId: '3c840faf-0fd8-4d9b-9fe9-2a0f2f50046c',
            appVersion: '2.23.1.0',
            transactionType: 'Sale',
            appliedPriceBooks: [],
            items: [
                {
                    itemType: 'ServiceCharge',
                    rate: 0.1,
                    taxRate: 0.02,
                    product: {},
                },
                {
                    id:
                        '1:5cf09520fb9bd9001683fe5a:5cf09520fb9bd9001683fe5b:5cf09520fb9bd9001683fe5e',
                    productId: '5cf09520fb9bd9001683fe5a',
                    unitPrice: 1.5,
                    priceType: 'fixed',
                    kitchenPrinter: '',
                    selectedOptions: [
                        {
                            optionValue: 0,
                            quantity: 1,
                        },
                    ],
                    options: [
                        {
                            variationId: '5cf09520fb9bd9001683fe5b',
                            optionId: '5cf09520fb9bd9001683fe5e',
                            optionValue: 'Chicken',
                            priceDiff: 0,
                            quantity: 1,
                        },
                    ],
                    quantity: 1,
                    taxRate: 0.06,
                    title: 'Bait & Switch',
                    total: 0,
                    taxCode: '5cef925efb9bd9001683fd3f',
                    notes: '',
                    product: {
                        category: 'Burger',
                        tags: ['chicken burger'],
                        _id: '5cf09520fb9bd9001683fe5a',
                    },
                },
            ],
            serialNumbers: {},
            business: 'qr',
            storeId: '5cef925dfb9bd9001683fd3b',
        };

        const promotions = [
            {
                _id: '5cf0c5dffb9bd900168400d6',

                promotionId: '5cf0c5dffb9bd900168400d6',
                appliedStores: null,
                createdTime: '2019-05-31T06:12:47.861Z',
                discountType: 'absolute',
                discountValue: 0.05,
                isEnabled: true,
                maxQuantity: 4294967295,
                minQuantity: 0.01,
                modifiedTime: '2019-10-25T02:28:55.151Z',
                name: 'Take amount off',
                ordering: 1,
                taxCode: '5cef925efb9bd9001683fd3f',
                validDays: null,
                validFrom: '2019-10-22T16:00:00.000Z',
                validTimeFrom: 0,
                validTimeTo: 24,
                validTo: null,
                conditions: [],
                requiredProducts: [],
                business: 'qr',
                taxRate: 0.06,
                owner: null,
            },
        ];

        const businessSettings = {
            timezone: 'Asia/Kuala_Lumpur',
        };
        const includingTaxInDisplay = true;

        const errors = calculator.applyPromotion(transactionSession, promotions, businessSettings);

        calculator.calculate(transactionSession, includingTaxInDisplay);
        expect(errors).toHaveLength(0);

        expect(transactionSession.items[1].promotionAppliedQuantityMap).toEqual({
            '5cf0c5dffb9bd900168400d6': 1,
        });
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), apply count with requiredProducts < apply count with conditions, apply multiple times', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 5,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(10);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                quantity: 2,
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), apply count with requiredProducts > apply count with conditions, apply multiple times', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(12);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                quantity: 4,
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), apply count with multiple requiredProducts is 0', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 2,
                    maxQuantity: null,
                },
                {
                    operand: ['c4'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 4,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(absoluteTransaction.items[0].promotionAppliedQuantityMap).toBeUndefined();
        expect(absoluteTransaction.promotions).toBeUndefined();
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), apply count with multiple requiredProducts < apply count with conditions, apply multiple times', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: true,
            discountType: 'absolute',
            minQuantity: 3,
            maxQuantity: 5,
            conditions: [
                {
                    operand: ['c1', 'c2'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
            requiredProducts: [
                {
                    operand: ['c3'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 5,
                    maxQuantity: null,
                },
                {
                    operand: ['c4'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                    minQuantity: 8,
                    maxQuantity: null,
                },
            ],
            taxCode: 'tax1',
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid1',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid3',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c3',
                    },
                },
                {
                    id: 'id4',
                    quantity: 12,
                    unitPrice: 10,
                    productId: 'pid4',
                    taxRate: 0.1,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid4',
                        category: 'c4',
                    },
                },
            ],
        };

        promotionCalculator.calculate(absoluteTransaction, [absolutePromotion], business);
        expect(
            absoluteTransaction.items[0].promotionAppliedQuantityMap[absolutePromotion._id],
        ).toBe(5);
        expect(absoluteTransaction.promotions.length).toBe(1);
        expect(absoluteTransaction.promotions[0]).toEqual(
            expect.objectContaining({
                inputValue: 1,
                type: 'absolute',
                quantity: 1,
                promotionId: 'pid',
                taxCode: 'tax1',
            }),
        );
    });

    test('success: transaction level promotion (absolute) with item level condition(isRepeatable), apply count with only one requiredProducts, with different taxRate', () => {
        const absolutePromotion = {
            ...promotion,
            isRepeatable: false,
            discountType: 'absolute',
            discountValue: 2,
            minQuantity: 0,
            maxQuantity: 100,
            conditions: [
                {
                    operand: ['c1'],
                    entity: 'product',
                    propertyName: 'category',
                    operator: 'in',
                },
            ],
        };
        const absoluteTransaction = {
            ...transaction,
            items: [
                {
                    id: 'id1',
                    quantity: 2,
                    unitPrice: 4.245283018867925,
                    productId: 'pid1',
                    taxRate: 0.06,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid1',
                        category: 'c1',
                    },
                },
                {
                    id: 'id2',
                    quantity: 2,
                    unitPrice: 4.245283018867925,
                    productId: 'pid2',
                    taxRate: 0.06,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid2',
                        category: 'c1',
                    },
                },
                {
                    id: 'id3',
                    quantity: 2,
                    unitPrice: 4.245283018867925,
                    productId: 'pid3',
                    taxRate: 0.06,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid3',
                        category: 'c1',
                    },
                },
                {
                    id: 'id4',
                    quantity: 1,
                    unitPrice: 0,
                    productId: 'pid4',
                    taxRate: 0.06,
                    taxCode: 'tax1',
                    product: {
                        _id: 'pid4',
                        category: 'c4',
                    },
                },
            ],
        };

        const includingTaxInDisplay = true;
        calculator.calculate(absoluteTransaction, includingTaxInDisplay);
        calculator.applyPromotion(absoluteTransaction, [absolutePromotion], business);
        calculator.calculate(absoluteTransaction, includingTaxInDisplay);
        expect(absoluteTransaction.total).toEqual(25);
        expect(absoluteTransaction.display).toEqual({
            discount: 0,
            serviceCharge: 0,
            subtotal: 23.59,
            tax: 1.41,
            total: 25,
        });
    });
});
