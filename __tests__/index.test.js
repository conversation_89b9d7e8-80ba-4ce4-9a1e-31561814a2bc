import { calculate } from '../index';

describe('pos-calculator:ES6+:jest', () => {
    test('Func:calculate:taxInclusiveDisplay:item incorrect', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 'string',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        try {
            calculate(transaction, true);
        } catch (error) {
            expect(error.message).toBe(
                'Product item field "unitPrice" is required and should be Number',
            );
        }
    });

    test('Func:calculate:taxExclusiveDisplay:item incorrect', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 'string',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        try {
            calculate(transaction, false);
        } catch (error) {
            expect(error.message).toBe(
                'Product item field "unitPrice" is required and should be Number',
            );
        }
    });

    test('Func:calculate:taxInclusiveDisplay:item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);
    });

    test('Func:calculate:taxExclusiveDisplay:item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculate(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);
    });
});

const calculateNode = require('../index').calculate;
const receiptNode = require('../index').receipt;

describe('pos-calculator:commonjs:jest', () => {
    test('Func:calculate:taxInclusiveDisplay:item incorrect', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 'string',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        try {
            calculateNode(transaction, true);
        } catch (error) {
            expect(error.message).toBe(
                'Product item field "unitPrice" is required and should be Number',
            );
        }
    });

    test('Func:calculate:taxExclusiveDisplay:item incorrect', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 'string',
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        try {
            calculateNode(transaction, false);
        } catch (error) {
            expect(error.message).toBe(
                'Product item field "unitPrice" is required and should be Number',
            );
        }
    });

    test('Func:calculate:taxInclusiveDisplay:item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculateNode(transaction, true);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);
    });

    test('Func:calculate:taxExclusiveDisplay:item', () => {
        const transaction = {
            items: [
                {
                    unitPrice: 535.714286,
                    quantity: 1,
                    taxRate: 0.12,
                },
            ],
        };

        const res = calculateNode(transaction, false);

        expect(res.serviceCharge).toBe(0);
        expect(res.serviceChargeTax).toBe(0);
        expect(res.subtotal).toBe(535.71);
        expect(res.discount).toBe(0);
        expect(res.tax).toBe(64.29);
        expect(res.total).toBe(600);

        expect(res.display.subtotal).toBe(535.71);
        expect(res.display.discount).toBe(0);
        expect(res.display.serviceCharge).toBe(0);
        expect(res.display.tax).toBe(64.29);
        expect(res.display.total).toBe(600);

        const receiptRes = receiptNode(res, false, 'MY');
        expect(receiptRes.receipt).toEqual({
            discount: 0,
            receiptTotalExcTax: 535.71,
            takeawayCharges: 0,
            serviceCharge: 0,
            tax: 64.29,
            total: 600,
        });
    });

    test('Func:calculate:taxExclusiveDisplay:item', () => {
        const transaction = {
            discount: 0,
            display: {
                discount: 0,
                serviceCharge: 0,
                subtotal: 535.71,
                tax: 64.29,
                total: 600,
            },
            items: [
                {
                    adhocDiscount: 0,
                    discount: 0,
                    display: {
                        subtotal: 535.71,
                        tax: 64.29,
                        total: 535.71,
                    },
                    quantity: 1,
                    subTotal: 535.71,
                    tax: 64.29,
                    taxExclusiveSubtotal: 535.71,
                    taxInclusiveSubtotal: 600,
                    taxRate: 0.12,
                    total: 535.71,
                    unitPrice: 535.714286,
                },
            ],
            serviceCharge: 0,
            serviceChargeTax: 0,
            subtotal: 535.71,
            tax: 64.29,
            total: 600,
        };
        const result = receiptNode(transaction, false, 'MY');
        expect(result.items[0].receipt).toEqual({
            total: 535.71,
            price: 535.71,
            a4Total: 535.71,
            qty: 1,
            discount: 0,
            discountWithoutPromo: 0,
        });
        expect(result.receipt).toEqual({
            discount: 0,
            receiptTotalExcTax: 535.71,
            serviceCharge: 0,
            tax: 64.29,
            total: 600,
            takeawayCharges: 0,
        });
    });
});
