{"name": "calculator-lib", "version": "3.9.2", "main": "index.js", "types": "index.d.ts", "repository": "**************:storehubnet/calculator-lib.git", "author": "<PERSON> <<EMAIL>>", "private": true, "publishConfig": {"registry": "https://npm-proxy.fury.io/byP2nuz4Las5rf72y98D/storehub/"}, "scripts": {"test": "yarn build && jest --logHeapUsage", "test:coverage": "yarn build && jest --coverage --collectCoverageFrom=src/**/*.js --collectCoverageFrom=index.js", "build": "babel src --out-dir dist -s", "postinstall": "babel src --out-dir dist -s"}, "dependencies": {"@babel/cli": "^7.2.3", "@babel/core": "^7.2.2", "@babel/preset-env": "^7.3.1", "@babel/preset-react": "^7.0.0", "decimal.js": "^10.4.3", "lodash": "^4.17.11", "moment-timezone": "^0.5.25"}, "devDependencies": {"@types/jest": "^24.0.18", "babel-eslint": "^10.0.1", "babel-jest": "^24.0.0", "eslint": "^5.12.1", "eslint-config-airbnb-base": "^13.1.0", "eslint-config-prettier": "^3.6.0", "eslint-plugin-import": "^2.14.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-security": "^1.4.0", "jest": "^24.0.0", "prettier": "^1.16.0"}, "jest": {"bail": false, "verbose": true, "moduleFileExtensions": ["js"], "testMatch": ["<rootDir>/__tests__/**/(*.)(spec|test).js?(x)"]}}