const calculator = require('./dist/calculator').default;
const validator = require('./dist/validator').default;
const receiptGenerator = require('./dist/receipt').default;
const promotionCalculator = require('./dist/promotionCalculator').default;
const {
    isNotInDateRange,
    isNotInWeekdays,
    isNotInTimeRange,
    isNotInAcrossTimeRange,
} = require('./dist/promotionCalculator/check');
const { CSource } = require('./dist/utils');
const conditionCalculator = require('./dist/promotionCalculator/conditionCalculator').default;

module.exports = {
    calculate,
    receipt,
    applyPromotion,
    checkConditionSatisfied: conditionCalculator.isSatisfied,
    isNotInDateRange,
    isNotInAcrossTimeRange,
    isNotInWeekdays,
    isNotInTimeRange,
    CSource,
};

function calculate(transaction, isTaxInclusiveDisplay, source = CSource.DEFAULT) {
    const error = validator(transaction, isTaxInclusiveDisplay);
    if (error) {
        throw error;
    } else {
        return calculator(transaction, isTaxInclusiveDisplay, source);
    }
}

function applyPromotion(transaction, promotion, businessSettings) {
    return promotionCalculator.calculate(transaction, promotion, businessSettings);
}

function receipt(
    transaction,
    isTaxInclusiveDisplay,
    countryCode,
    hasDiscountColumn,
    unitPriceUnrounding = false,
) {
    return receiptGenerator(
        transaction,
        isTaxInclusiveDisplay,
        countryCode,
        hasDiscountColumn,
        unitPriceUnrounding,
    );
}
