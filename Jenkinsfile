pipeline {
  agent {
    label 'slave'
  }
  stages {
    stage('Prepare env') {
      steps {
        nodejs('v8.9.3') {
          sh 'npm install'
        }
      }
    }
    stage('Unit Test') {
      steps {
        nodejs('v8.9.3') {
          sh 'npm run test:coverage'
        }
      }
    }
    stage('SonarQube') {
      steps {
        withSonarQubeEnv('SonarQube') {
          sh 'echo "sonar.branch.name=$GIT_BRANCH" >> sonar-project.properties'
          sh 'echo "sonar.projectVersion=$(date +"%Y-%m")" >> sonar-project.properties'
          sh '/usr/local/sonar-scanner/bin/sonar-scanner'
        }
        script {
          timeout(10) {
            def qg = waitForQualityGate()
            if (qg.status != 'OK') {
              error "Pipeline failed due to Sonarqube Quality Gates! failure: ${qg.status}"
            }
          }
        }
      }
    }
  }
  post {
    always {
      cleanWs()
    }
  }
}
